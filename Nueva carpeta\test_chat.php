<?php
// Archivo para probar el funcionamiento del chat
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🧪 Prueba del Sistema de Chat</h1>";

try {
    // 1. Verificar tablas de chat
    echo "<h2>📋 Verificando Tablas de Chat</h2>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'chat_%'");
    $chat_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Tablas de chat encontradas: " . count($chat_tables) . "</p>";
    foreach ($chat_tables as $table) {
        echo "• $table<br>";
    }
    
    // 2. Verificar estructura de chat_sessions
    echo "<h2>💬 Estructura de chat_sessions</h2>";
    try {
        $stmt = $pdo->query("DESCRIBE chat_sessions");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Por defecto</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
    
    // 3. Verificar estructura de chat_messages
    echo "<h2>💭 Estructura de chat_messages</h2>";
    try {
        $stmt = $pdo->query("DESCRIBE chat_messages");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Por defecto</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
    
    // 4. Contar datos existentes
    echo "<h2>📊 Datos Existentes</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions");
    $sessions_count = $stmt->fetchColumn();
    echo "Sesiones de chat: $sessions_count<br>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_messages");
    $messages_count = $stmt->fetchColumn();
    echo "Mensajes de chat: $messages_count<br>";
    
    // 5. Mostrar sesiones existentes
    echo "<h2>💬 Sesiones de Chat</h2>";
    $stmt = $pdo->query("SELECT * FROM chat_sessions ORDER BY started_at DESC LIMIT 5");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sessions)) {
        echo "No hay sesiones de chat<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Usuario</th><th>Estado</th><th>Iniciado</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['user_id']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>{$session['started_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 6. Mostrar mensajes existentes
    echo "<h2>💭 Mensajes de Chat</h2>";
    $stmt = $pdo->query("
        SELECT cm.*, cs.user_id as session_user 
        FROM chat_messages cm 
        LEFT JOIN chat_sessions cs ON cm.session_id = cs.id 
        ORDER BY cm.sent_at DESC LIMIT 10
    ");
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($messages)) {
        echo "No hay mensajes de chat<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Sesión</th><th>Remitente</th><th>Mensaje</th><th>Admin</th><th>Enviado</th></tr>";
        foreach ($messages as $msg) {
            echo "<tr>";
            echo "<td>{$msg['id']}</td>";
            echo "<td>{$msg['session_id']}</td>";
            echo "<td>{$msg['sender_id']}</td>";
            echo "<td>" . substr($msg['message'], 0, 50) . "...</td>";
            echo "<td>" . ($msg['is_admin'] ? 'Sí' : 'No') . "</td>";
            echo "<td>{$msg['sent_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 7. Probar inserción de sesión
    echo "<h2>🧪 Prueba de Inserción</h2>";
    try {
        // Crear sesión de prueba
        $stmt = $pdo->prepare("INSERT INTO chat_sessions (user_id, status) VALUES (?, ?)");
        $result = $stmt->execute([1, 'waiting']);
        
        if ($result) {
            $session_id = $pdo->lastInsertId();
            echo "✅ Sesión de prueba creada con ID: $session_id<br>";
            
            // Crear mensaje de prueba
            $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, ?)");
            $result = $stmt->execute([$session_id, 1, 'Mensaje de prueba desde test_chat.php', 0]);
            
            if ($result) {
                echo "✅ Mensaje de prueba creado<br>";
            } else {
                echo "❌ Error creando mensaje de prueba<br>";
            }
        } else {
            echo "❌ Error creando sesión de prueba<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error en prueba de inserción: " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
    echo "<h2>🔗 Enlaces de Prueba</h2>";
    echo "<p><a href='user_chat_simple.php'>💬 Probar Chat Simple</a></p>";
    echo "<p><a href='user_chat.php'>💬 Probar Chat Original (puede dar error)</a></p>";
    echo "<p><a href='admin_chat_real.php'>⚙️ Panel de Chat Admin</a></p>";
    echo "<p><a href='index2.php'>🏠 Volver a Servicios</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h2>❌ Error de Conexión</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
