<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Admin.php</h1>";

// Verificar sesión
session_start();
echo "<h2>Estado de la Sesión:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Verificar conexión a la base de datos
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h2>Conexión a la Base de Datos:</h2>";
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Conexión exitosa<br>";
    
    // Verificar estructura de la tabla users
    echo "<h3>Estructura de la tabla users:</h3>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Verificar usuarios existentes
    echo "<h3>Usuarios en la base de datos:</h3>";
    $stmt = $pdo->query("SELECT id, username, is_admin, is_cliente_actual, is_mavistv, is_tvdigital, is_limites507 FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($users);
    echo "</pre>";
    
    // Verificar si existen las nuevas columnas
    echo "<h3>Verificando nuevas columnas:</h3>";
    $new_service_columns = ['is_infest84', 'is_rogsmediatv', 'is_saul'];
    foreach ($new_service_columns as $column) {
        $cols = $pdo->query("SHOW COLUMNS FROM users LIKE '$column'")->fetchAll();
        if (count($cols) === 0) {
            echo "❌ Columna '$column' NO existe<br>";
        } else {
            echo "✅ Columna '$column' existe<br>";
        }
    }
    
    // Verificar tabla orders
    echo "<h3>Verificando tabla orders:</h3>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
        $count = $stmt->fetchColumn();
        echo "✅ Tabla orders existe con $count registros<br>";
    } catch (Exception $e) {
        echo "❌ Error con tabla orders: " . $e->getMessage() . "<br>";
    }
    
} catch(PDOException $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "<br>";
}

// Verificar permisos de archivos
echo "<h2>Permisos de Archivos:</h2>";
$files = ['admin.php', 'login.php', 'admin_login.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "$file: " . substr(sprintf('%o', $perms), -4) . "<br>";
    } else {
        echo "$file: ❌ No existe<br>";
    }
}

// Verificar configuración PHP
echo "<h2>Configuración PHP:</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Error Reporting: " . error_reporting() . "<br>";
echo "Display Errors: " . ini_get('display_errors') . "<br>";
echo "Log Errors: " . ini_get('log_errors') . "<br>";
echo "Error Log: " . ini_get('error_log') . "<br>";

?>
