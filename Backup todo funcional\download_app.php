<?php
session_start();
require_once 'config.php';

// Función para obtener IP real del usuario
function get_real_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// Función para registrar descarga
function registerDownload($app_id, $pdo) {
    try {
        $user_id = $_SESSION['user_id'] ?? null;
        $ip_address = get_real_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Verificar si la tabla app_downloads existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'app_downloads'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("INSERT INTO app_downloads (app_id, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?)");
            $stmt->execute([$app_id, $user_id, $ip_address, $user_agent]);
        }
        
        // Actualizar contador en la app
        $stmt = $pdo->prepare("UPDATE support_apps SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$app_id]);
        
        // Crear notificación para admin
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->prepare("SELECT name FROM support_apps WHERE id = ?");
                $stmt->execute([$app_id]);
                $app_name = $stmt->fetchColumn();
                
                $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([
                    'app_download',
                    'Nueva descarga de aplicación',
                    "Se descargó la aplicación: {$app_name}",
                    $app_id,
                    'support_apps'
                ]);
            }
        } catch (Exception $e) {
            // Si hay error con notificaciones, continuar sin ellas
            error_log("Error creando notificación de descarga: " . $e->getMessage());
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Error registrando descarga: " . $e->getMessage());
        return false;
    }
}

// Verificar parámetros
$app_id = (int)($_GET['id'] ?? 0);
$action = $_GET['action'] ?? 'download';

if (!$app_id) {
    http_response_code(400);
    die('ID de aplicación requerido');
}

try {
    // Obtener información de la aplicación
    $stmt = $pdo->prepare("
        SELECT * FROM support_apps 
        WHERE id = ? AND is_active = 1 AND (status = 'active' OR status IS NULL)
    ");
    $stmt->execute([$app_id]);
    $app = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$app) {
        http_response_code(404);
        die('Aplicación no encontrada o no disponible');
    }
    
    // Registrar la descarga
    registerDownload($app_id, $pdo);
    
    if ($action === 'external' && !empty($app['external_url'])) {
        // Redirigir a URL externa
        header('Location: ' . $app['external_url']);
        exit;
    } elseif ($action === 'store' && !empty($app['download_url'])) {
        // Redirigir a tienda de aplicaciones
        header('Location: ' . $app['download_url']);
        exit;
    } elseif (!empty($app['file_path']) && file_exists($app['file_path'])) {
        // Descarga directa del archivo
        $file_path = $app['file_path'];
        $file_name = basename($file_path);
        $file_size = filesize($file_path);
        $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        
        // Configurar headers para descarga
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $file_name . '"');
        header('Content-Length: ' . $file_size);
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Expires: 0');
        
        // Configurar tipo MIME específico según extensión
        switch ($file_extension) {
            case 'apk':
                header('Content-Type: application/vnd.android.package-archive');
                break;
            case 'ipa':
                header('Content-Type: application/octet-stream');
                break;
            case 'exe':
            case 'msi':
                header('Content-Type: application/x-msdownload');
                break;
            case 'dmg':
                header('Content-Type: application/x-apple-diskimage');
                break;
            case 'deb':
                header('Content-Type: application/x-debian-package');
                break;
            case 'zip':
                header('Content-Type: application/zip');
                break;
        }
        
        // Leer y enviar archivo en chunks para archivos grandes
        $handle = fopen($file_path, 'rb');
        if ($handle) {
            while (!feof($handle)) {
                echo fread($handle, 8192); // 8KB chunks
                flush();
            }
            fclose($handle);
        } else {
            http_response_code(500);
            die('Error al leer el archivo');
        }
        exit;
    } else {
        // No hay archivo ni URLs disponibles
        http_response_code(404);
        die('Archivo no encontrado. La aplicación puede no tener un archivo de descarga disponible.');
    }
    
} catch (Exception $e) {
    error_log("Error en descarga de app: " . $e->getMessage());
    http_response_code(500);
    die('Error interno del servidor');
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Descarga de Aplicación - RGS TOOL</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        .error {
            color: #ef4444;
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>❌ Error en la Descarga</h1>
        <p class="error">No se pudo procesar la descarga de la aplicación.</p>
        <a href="index2.php" class="btn">🏠 Volver al Inicio</a>
        <a href="user_apps.php" class="btn">📱 Ver Aplicaciones</a>
    </div>
</body>
</html>
