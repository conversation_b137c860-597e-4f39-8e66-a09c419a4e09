<?php
// Archivo de configuración inicial del sistema
session_start();

// Configuración de la base de datos (temporal para setup)
$setup_config = [
    'db_host' => 'localhost',
    'db_name' => 'rgs_support_system',
    'db_user' => 'root',
    'db_pass' => ''
];

$step = (int)($_GET['step'] ?? 1);
$error_message = '';
$success_message = '';

// Procesar formularios
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_connection'])) {
        // Probar conexión a la base de datos
        try {
            $pdo = new PDO(
                "mysql:host={$_POST['db_host']};charset=utf8mb4", 
                $_POST['db_user'], 
                $_POST['db_pass']
            );
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Crear base de datos si no existe
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$_POST['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Guardar configuración en sesión
            $_SESSION['setup_config'] = $_POST;
            $success_message = "Conexión exitosa. Base de datos creada/verificada.";
            
        } catch (PDOException $e) {
            $error_message = "Error de conexión: " . $e->getMessage();
        }
    }
    
    if (isset($_POST['create_tables'])) {
        // Crear tablas
        try {
            $config = $_SESSION['setup_config'];
            $pdo = new PDO(
                "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4", 
                $config['db_user'], 
                $config['db_pass']
            );
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Leer y ejecutar SQL
            $sql_file = 'support_system_tables.sql';
            if (file_exists($sql_file)) {
                $sql = file_get_contents($sql_file);
                $statements = explode(';', $sql);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                $success_message = "Tablas creadas exitosamente.";
                $step = 3;
            } else {
                $error_message = "Archivo SQL no encontrado: $sql_file";
            }
            
        } catch (PDOException $e) {
            $error_message = "Error creando tablas: " . $e->getMessage();
        }
    }
    
    if (isset($_POST['create_config'])) {
        // Crear archivo config.php
        $config = $_SESSION['setup_config'];
        
        $config_content = "<?php
// Configuración de la base de datos
\$db_host = '{$config['db_host']}';
\$db_name = '{$config['db_name']}';
\$db_user = '{$config['db_user']}';
\$db_pass = '{$config['db_pass']}';

// Configuración del sistema
define('SITE_URL', 'http://{$_SERVER['HTTP_HOST']}');
define('ADMIN_EMAIL', '{$_POST['admin_email']}');
define('SYSTEM_NAME', 'RGS Support System');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB

// Configuración de sesión
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Cambiar a 1 si usas HTTPS

// Conexión a la base de datos
try {
    \$pdo = new PDO(\"mysql:host=\$db_host;dbname=\$db_name;charset=utf8mb4\", \$db_user, \$db_pass);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException \$e) {
    die(\"Error de conexión a la base de datos. Contacte al administrador.\");
}

// Funciones auxiliares incluidas aquí...
// [El resto del contenido de config.php se incluiría aquí]
?>";

        if (file_put_contents('config.php', $config_content)) {
            $success_message = "Archivo de configuración creado exitosamente.";
            $step = 4;
        } else {
            $error_message = "Error creando archivo de configuración. Verifica permisos de escritura.";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Configuración Inicial - RGS Support System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }

        .setup-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .setup-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .setup-logo {
            font-size: 4rem;
            color: var(--support-color);
            margin-bottom: 1rem;
        }

        .setup-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .setup-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .progress-bar {
            background: var(--secondary-color);
            border-radius: 25px;
            padding: 0.5rem;
            margin-bottom: 3rem;
            border: 1px solid var(--border-color);
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .progress-step.active {
            background: var(--primary-color);
            color: white;
        }

        .progress-step.completed {
            background: var(--success-color);
            color: white;
        }

        .progress-step.pending {
            background: transparent;
            color: var(--text-secondary);
        }

        .setup-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .requirements-list {
            list-style: none;
            padding: 0;
        }

        .requirements-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .requirements-list .check {
            color: var(--success-color);
        }

        .requirements-list .cross {
            color: var(--error-color);
        }

        .code-block {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .final-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .final-link {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            text-decoration: none;
            color: var(--text-primary);
            transition: var(--transition);
        }

        .final-link:hover {
            background: var(--primary-color);
            transform: translateY(-5px);
        }

        .final-link i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <div class="setup-logo">
                <i class="fas fa-rocket"></i>
            </div>
            <h1 class="setup-title">Configuración Inicial</h1>
            <p class="setup-subtitle">Sistema de Soporte Técnico RGS</p>
        </div>

        <!-- Barra de progreso -->
        <div class="progress-bar">
            <div class="progress-steps">
                <div class="progress-step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : 'pending'; ?>">
                    <i class="fas fa-database"></i>
                    <span>Base de Datos</span>
                </div>
                <div class="progress-step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : 'pending'; ?>">
                    <i class="fas fa-table"></i>
                    <span>Tablas</span>
                </div>
                <div class="progress-step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : 'pending'; ?>">
                    <i class="fas fa-cog"></i>
                    <span>Configuración</span>
                </div>
                <div class="progress-step <?php echo $step >= 4 ? 'active' : 'pending'; ?>">
                    <i class="fas fa-check"></i>
                    <span>Finalizado</span>
                </div>
            </div>
        </div>

        <?php if ($error_message): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <?php if ($step == 1): ?>
        <!-- Paso 1: Configuración de Base de Datos -->
        <div class="setup-card">
            <h2 class="card-title">
                <i class="fas fa-database"></i>
                Configuración de Base de Datos
            </h2>
            
            <form method="POST">
                <div class="form-group">
                    <label class="form-label">Servidor de Base de Datos</label>
                    <input type="text" name="db_host" class="form-input" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Nombre de la Base de Datos</label>
                    <input type="text" name="db_name" class="form-input" value="rgs_support_system" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Usuario de Base de Datos</label>
                    <input type="text" name="db_user" class="form-input" value="root" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Contraseña de Base de Datos</label>
                    <input type="password" name="db_pass" class="form-input" placeholder="Dejar vacío si no tiene contraseña">
                </div>
                
                <button type="submit" name="test_connection" class="btn btn-primary">
                    <i class="fas fa-plug"></i>
                    Probar Conexión
                </button>
            </form>
        </div>
        <?php endif; ?>

        <?php if ($step == 2): ?>
        <!-- Paso 2: Crear Tablas -->
        <div class="setup-card">
            <h2 class="card-title">
                <i class="fas fa-table"></i>
                Crear Tablas de la Base de Datos
            </h2>
            
            <p style="margin-bottom: 1.5rem; color: var(--text-secondary);">
                Se crearán todas las tablas necesarias para el sistema de soporte.
            </p>
            
            <form method="POST">
                <button type="submit" name="create_tables" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    Crear Tablas
                </button>
            </form>
        </div>
        <?php endif; ?>

        <?php if ($step == 3): ?>
        <!-- Paso 3: Configuración del Sistema -->
        <div class="setup-card">
            <h2 class="card-title">
                <i class="fas fa-cog"></i>
                Configuración del Sistema
            </h2>
            
            <form method="POST">
                <div class="form-group">
                    <label class="form-label">Email del Administrador</label>
                    <input type="email" name="admin_email" class="form-input" value="<EMAIL>" required>
                </div>
                
                <button type="submit" name="create_config" class="btn btn-success">
                    <i class="fas fa-save"></i>
                    Crear Configuración
                </button>
            </form>
        </div>
        <?php endif; ?>

        <?php if ($step == 4): ?>
        <!-- Paso 4: Finalizado -->
        <div class="setup-card">
            <h2 class="card-title">
                <i class="fas fa-check-circle"></i>
                ¡Configuración Completada!
            </h2>
            
            <p style="margin-bottom: 2rem; color: var(--text-secondary);">
                El sistema ha sido configurado exitosamente. Ya puedes comenzar a usar el panel de administración.
            </p>
            
            <div class="alert alert-success">
                <i class="fas fa-info-circle"></i>
                <strong>Credenciales por defecto:</strong> Usuario: <code>admin</code> | Contraseña: <code>admin123</code>
            </div>
            
            <div class="final-links">
                <a href="admin_login.php" class="final-link">
                    <i class="fas fa-sign-in-alt"></i>
                    <strong>Iniciar Sesión</strong>
                    <small>Panel de Administración</small>
                </a>
                
                <a href="admin2.php" class="final-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <strong>Dashboard</strong>
                    <small>Panel de Soporte</small>
                </a>
                
                <a href="system_status.php" class="final-link">
                    <i class="fas fa-heartbeat"></i>
                    <strong>Estado del Sistema</strong>
                    <small>Monitoreo</small>
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Información adicional -->
        <div class="setup-card">
            <h3 style="color: var(--text-primary); margin-bottom: 1rem;">
                <i class="fas fa-info-circle"></i>
                Información Importante
            </h3>
            
            <ul class="requirements-list">
                <li>
                    <i class="fas fa-check check"></i>
                    <span>PHP 7.4 o superior requerido</span>
                </li>
                <li>
                    <i class="fas fa-check check"></i>
                    <span>MySQL 5.7 o superior requerido</span>
                </li>
                <li>
                    <i class="fas fa-check check"></i>
                    <span>Extensiones PDO y cURL habilitadas</span>
                </li>
                <li>
                    <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                    <span>Cambiar credenciales por defecto en producción</span>
                </li>
                <li>
                    <i class="fas fa-shield-alt" style="color: var(--primary-color);"></i>
                    <span>Configurar HTTPS para mayor seguridad</span>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-focus en el primer input
        const firstInput = document.querySelector('.form-input');
        if (firstInput) {
            firstInput.focus();
        }

        // Validación básica del formulario
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const button = this.querySelector('button[type="submit"]');
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Procesando...';
            });
        });
    </script>
</body>
</html>
