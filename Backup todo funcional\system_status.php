<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Función para verificar el estado de un servicio
function checkServiceStatus($url, $timeout = 5) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'RGS System Monitor');
    
    $start_time = microtime(true);
    $result = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    curl_close($ch);
    
    return [
        'status' => ($http_code >= 200 && $http_code < 400) ? 'online' : 'offline',
        'http_code' => $http_code,
        'response_time' => $response_time,
        'error' => ($result === false) ? 'Connection failed' : null
    ];
}

// Función para obtener información del sistema
function getSystemInfo() {
    return [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'disk_free_space' => round(disk_free_space('.') / 1024 / 1024 / 1024, 2),
        'disk_total_space' => round(disk_total_space('.') / 1024 / 1024 / 1024, 2)
    ];
}

// Función para verificar la base de datos
function checkDatabaseStatus($pdo) {
    try {
        $stmt = $pdo->query("SELECT 1");
        $start_time = microtime(true);
        $stmt->fetch();
        $end_time = microtime(true);
        
        return [
            'status' => 'online',
            'response_time' => round(($end_time - $start_time) * 1000, 2)
        ];
    } catch (Exception $e) {
        return [
            'status' => 'offline',
            'error' => $e->getMessage()
        ];
    }
}

// Obtener estadísticas del sistema
$system_info = getSystemInfo();
$db_status = checkDatabaseStatus($pdo);

// Servicios a monitorear
$services = [
    [
        'name' => 'Sitio Web Principal',
        'url' => 'http://' . $_SERVER['HTTP_HOST'],
        'description' => 'Página principal del sistema'
    ],
    [
        'name' => 'Panel de Administración',
        'url' => 'http://' . $_SERVER['HTTP_HOST'] . '/admin.php',
        'description' => 'Panel de control administrativo'
    ],
    [
        'name' => 'API de Soporte',
        'url' => 'http://' . $_SERVER['HTTP_HOST'] . '/api_support_stats.php',
        'description' => 'API para estadísticas de soporte'
    ]
];

// Verificar estado de servicios
foreach ($services as &$service) {
    $service['status_info'] = checkServiceStatus($service['url']);
}

// Obtener estadísticas de la base de datos
try {
    $db_stats = [
        'total_users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'total_tickets' => $pdo->query("SELECT COUNT(*) FROM support_tickets")->fetchColumn(),
        'active_chats' => $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'")->fetchColumn(),
        'total_apps' => $pdo->query("SELECT COUNT(*) FROM support_apps")->fetchColumn(),
        'total_activations' => $pdo->query("SELECT COUNT(*) FROM list_activations")->fetchColumn()
    ];
} catch (Exception $e) {
    $db_stats = null;
}

// Calcular uptime (simulado)
$uptime_seconds = time() - strtotime('2024-01-01 00:00:00');
$uptime_days = floor($uptime_seconds / 86400);
$uptime_hours = floor(($uptime_seconds % 86400) / 3600);
$uptime_minutes = floor(($uptime_seconds % 3600) / 60);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💚 Estado del Sistema - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 500;
            margin-top: 1rem;
        }

        .status-online {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-offline {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .stat-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .stat-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-content {
            padding: 1.5rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .stat-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .services-section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .service-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 1.5rem;
            transition: var(--transition);
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
        }

        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .service-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.3rem;
        }

        .service-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .service-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .service-online {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }

        .service-offline {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
        }

        .service-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .service-detail {
            text-align: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
        }

        .service-detail-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--accent-color);
            margin-bottom: 0.3rem;
        }

        .service-detail-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .uptime-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .uptime-value {
            font-size: 3rem;
            font-weight: 700;
            color: var(--success-color);
            margin-bottom: 0.5rem;
        }

        .uptime-label {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0,0,0,0.4);
            transition: var(--transition);
        }

        .refresh-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        .refresh-btn.spinning {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .service-details {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="system_status.php" class="logo">
                <i class="fas fa-heartbeat"></i>
                <span>Estado del Sistema</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-heartbeat pulse"></i>
                Estado del Sistema
            </h1>
            <p class="page-subtitle">
                Monitoreo en tiempo real de servicios y recursos del sistema
            </p>
            
            <div class="status-indicator status-online">
                <i class="fas fa-check-circle"></i>
                Todos los servicios operativos
            </div>
        </div>

        <!-- Uptime -->
        <div class="uptime-section">
            <div class="uptime-value"><?php echo $uptime_days; ?> días</div>
            <div class="uptime-label">
                Tiempo de actividad: <?php echo $uptime_days; ?> días, <?php echo $uptime_hours; ?> horas, <?php echo $uptime_minutes; ?> minutos
            </div>
        </div>

        <!-- Estadísticas del Sistema -->
        <div class="stats-grid">
            <!-- Información del Sistema -->
            <div class="stat-section">
                <div class="stat-header">
                    <div class="stat-title">
                        <i class="fas fa-server"></i>
                        Información del Sistema
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-item">
                        <span class="stat-label">Versión PHP</span>
                        <span class="stat-value"><?php echo $system_info['php_version']; ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Servidor Web</span>
                        <span class="stat-value"><?php echo $system_info['server_software']; ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Uso de Memoria</span>
                        <span class="stat-value"><?php echo $system_info['memory_usage']; ?> MB</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Límite de Memoria</span>
                        <span class="stat-value"><?php echo $system_info['memory_limit']; ?></span>
                    </div>
                </div>
            </div>

            <!-- Base de Datos -->
            <div class="stat-section">
                <div class="stat-header">
                    <div class="stat-title">
                        <i class="fas fa-database"></i>
                        Base de Datos
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-item">
                        <span class="stat-label">Estado</span>
                        <span class="stat-value" style="color: <?php echo $db_status['status'] === 'online' ? 'var(--success-color)' : 'var(--error-color)'; ?>">
                            <?php echo $db_status['status'] === 'online' ? 'Conectada' : 'Desconectada'; ?>
                        </span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Tiempo de Respuesta</span>
                        <span class="stat-value"><?php echo $db_status['response_time'] ?? 'N/A'; ?> ms</span>
                    </div>
                    <?php if ($db_stats): ?>
                    <div class="stat-item">
                        <span class="stat-label">Total Usuarios</span>
                        <span class="stat-value"><?php echo number_format($db_stats['total_users']); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Tickets Activos</span>
                        <span class="stat-value"><?php echo number_format($db_stats['total_tickets']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Almacenamiento -->
            <div class="stat-section">
                <div class="stat-header">
                    <div class="stat-title">
                        <i class="fas fa-hdd"></i>
                        Almacenamiento
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-item">
                        <span class="stat-label">Espacio Libre</span>
                        <span class="stat-value"><?php echo $system_info['disk_free_space']; ?> GB</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Espacio Total</span>
                        <span class="stat-value"><?php echo $system_info['disk_total_space']; ?> GB</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Uso del Disco</span>
                        <span class="stat-value">
                            <?php
                            $usage_percent = round((($system_info['disk_total_space'] - $system_info['disk_free_space']) / $system_info['disk_total_space']) * 100, 1);
                            echo $usage_percent . '%';
                            ?>
                        </span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Tamaño Máximo de Subida</span>
                        <span class="stat-value"><?php echo $system_info['upload_max_filesize']; ?></span>
                    </div>
                </div>
            </div>

            <!-- Estadísticas de Soporte -->
            <?php if ($db_stats): ?>
            <div class="stat-section">
                <div class="stat-header">
                    <div class="stat-title">
                        <i class="fas fa-headset"></i>
                        Soporte Técnico
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-item">
                        <span class="stat-label">Chats Activos</span>
                        <span class="stat-value"><?php echo number_format($db_stats['active_chats']); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Aplicaciones</span>
                        <span class="stat-value"><?php echo number_format($db_stats['total_apps']); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Activaciones</span>
                        <span class="stat-value"><?php echo number_format($db_stats['total_activations']); ?></span>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Estado de Servicios -->
        <div class="services-section">
            <h2 class="section-title">
                <i class="fas fa-globe"></i>
                Estado de Servicios
            </h2>

            <div class="services-grid">
                <?php foreach ($services as $service): ?>
                <div class="service-card">
                    <div class="service-header">
                        <div>
                            <div class="service-name"><?php echo htmlspecialchars($service['name']); ?></div>
                            <div class="service-description"><?php echo htmlspecialchars($service['description']); ?></div>
                        </div>
                        <div class="service-status service-<?php echo $service['status_info']['status']; ?>">
                            <i class="fas fa-<?php echo $service['status_info']['status'] === 'online' ? 'check-circle' : 'times-circle'; ?>"></i>
                            <?php echo $service['status_info']['status'] === 'online' ? 'Online' : 'Offline'; ?>
                        </div>
                    </div>

                    <div class="service-details">
                        <div class="service-detail">
                            <div class="service-detail-value">
                                <?php echo $service['status_info']['response_time']; ?>ms
                            </div>
                            <div class="service-detail-label">Tiempo de Respuesta</div>
                        </div>
                        <div class="service-detail">
                            <div class="service-detail-value">
                                <?php echo $service['status_info']['http_code']; ?>
                            </div>
                            <div class="service-detail-label">Código HTTP</div>
                        </div>
                    </div>

                    <?php if ($service['status_info']['error']): ?>
                    <div style="margin-top: 1rem; padding: 0.75rem; background: rgba(239, 68, 68, 0.1); border-radius: var(--border-radius); color: var(--error-color); font-size: 0.9rem;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($service['status_info']['error']); ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Información Adicional -->
        <div class="stats-grid">
            <div class="stat-section">
                <div class="stat-header">
                    <div class="stat-title">
                        <i class="fas fa-clock"></i>
                        Información de Tiempo
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-item">
                        <span class="stat-label">Hora del Servidor</span>
                        <span class="stat-value"><?php echo date('d/m/Y H:i:s'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Zona Horaria</span>
                        <span class="stat-value"><?php echo date_default_timezone_get(); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Última Actualización</span>
                        <span class="stat-value" id="lastUpdate"><?php echo date('H:i:s'); ?></span>
                    </div>
                </div>
            </div>

            <div class="stat-section">
                <div class="stat-header">
                    <div class="stat-title">
                        <i class="fas fa-cogs"></i>
                        Configuración PHP
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-item">
                        <span class="stat-label">Tiempo Máximo de Ejecución</span>
                        <span class="stat-value"><?php echo $system_info['max_execution_time']; ?>s</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Extensiones Cargadas</span>
                        <span class="stat-value"><?php echo count(get_loaded_extensions()); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">cURL Disponible</span>
                        <span class="stat-value" style="color: <?php echo extension_loaded('curl') ? 'var(--success-color)' : 'var(--error-color)'; ?>">
                            <?php echo extension_loaded('curl') ? 'Sí' : 'No'; ?>
                        </span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PDO Disponible</span>
                        <span class="stat-value" style="color: <?php echo extension_loaded('pdo') ? 'var(--success-color)' : 'var(--error-color)'; ?>">
                            <?php echo extension_loaded('pdo') ? 'Sí' : 'No'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Botón de Actualización -->
    <button class="refresh-btn" onclick="refreshStatus()" title="Actualizar Estado">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script>
        // Auto-refresh cada 30 segundos
        setInterval(function() {
            if (!document.hidden) {
                refreshStatus();
            }
        }, 30000);

        function refreshStatus() {
            const btn = document.querySelector('.refresh-btn');
            btn.classList.add('spinning');

            // Actualizar timestamp
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();

            // Simular actualización (en una implementación real, harías una llamada AJAX)
            setTimeout(() => {
                btn.classList.remove('spinning');
                location.reload();
            }, 1000);
        }

        // Actualizar reloj cada segundo
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleString('es-ES', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const serverTimeElement = document.querySelector('.stat-item .stat-value');
            if (serverTimeElement && serverTimeElement.textContent.includes('/')) {
                serverTimeElement.textContent = timeString;
            }
        }, 1000);

        // Mostrar notificación si hay servicios offline
        const offlineServices = document.querySelectorAll('.service-offline');
        if (offlineServices.length > 0) {
            console.warn(`${offlineServices.length} servicio(s) offline detectado(s)`);

            // Cambiar el indicador de estado general
            const statusIndicator = document.querySelector('.status-indicator');
            statusIndicator.className = 'status-indicator status-offline';
            statusIndicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Algunos servicios no están disponibles';
        }

        // Resaltar valores críticos
        document.querySelectorAll('.stat-value').forEach(element => {
            const text = element.textContent;

            // Resaltar uso de disco alto
            if (text.includes('%')) {
                const percentage = parseFloat(text);
                if (percentage > 90) {
                    element.style.color = 'var(--error-color)';
                } else if (percentage > 75) {
                    element.style.color = 'var(--warning-color)';
                }
            }

            // Resaltar memoria alta
            if (text.includes('MB') && element.parentElement.querySelector('.stat-label').textContent.includes('Memoria')) {
                const mb = parseFloat(text);
                if (mb > 500) {
                    element.style.color = 'var(--warning-color)';
                }
            }
        });

        // Animación de entrada para las tarjetas
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.stat-section, .service-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
