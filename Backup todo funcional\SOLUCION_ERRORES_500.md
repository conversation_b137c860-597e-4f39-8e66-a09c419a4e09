# 🚨 Solución para Errores 500 en el Sistema RGS

## ⚡ **SOLUCIÓN RÁPIDA (Recomendada)**

### 1. **Ejecutar Configuración Automática**
```
http://tu-dominio.com/quick_setup.php
```
Este archivo:
- ✅ Crea la base de datos automáticamente
- ✅ Crea todas las tablas necesarias
- ✅ Inserta datos de ejemplo
- ✅ Configura usuarios demo

### 2. **Verificar Estado del Sistema**
```
http://tu-dominio.com/debug.php
```
Este archivo diagnostica:
- ✅ Conexión a base de datos
- ✅ Existencia de tablas
- ✅ Permisos de archivos
- ✅ Configuración PHP

---

## 🔧 **SOLUCIÓN MANUAL**

### Paso 1: Verificar MySQL
```bash
# Verificar que MySQL esté ejecutándose
mysql -u root -p
```

### Paso 2: Crear Base de Datos
```sql
CREATE DATABASE rgs_support_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE rgs_support_system;
```

### Paso 3: Importar Tablas
```bash
mysql -u root -p rgs_support_system < support_system_tables.sql
```

### Paso 4: Verificar Configuración
Editar `config.php` con las credenciales correctas:
```php
$db_host = 'localhost';
$db_name = 'rgs_support_system';
$db_user = 'root';
$db_pass = 'tu_contraseña';
```

---

## 🐛 **ERRORES COMUNES Y SOLUCIONES**

### Error: "Base de datos no encontrada"
**Solución:**
1. Ejecutar `quick_setup.php`
2. O crear manualmente la BD

### Error: "Tabla no existe"
**Solución:**
1. Importar `support_system_tables.sql`
2. O ejecutar `quick_setup.php`

### Error: "Access denied for user"
**Solución:**
1. Verificar credenciales en `config.php`
2. Crear usuario MySQL:
```sql
CREATE USER 'rgs_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON rgs_support_system.* TO 'rgs_user'@'localhost';
FLUSH PRIVILEGES;
```

### Error: "Permission denied"
**Solución:**
```bash
chmod 755 uploads/
chmod 755 uploads/apps/
chmod 755 uploads/tickets/
```

---

## 📋 **CHECKLIST DE VERIFICACIÓN**

### ✅ **Requisitos del Sistema**
- [ ] PHP 7.4 o superior
- [ ] MySQL 5.7 o superior
- [ ] Extensión PDO habilitada
- [ ] Extensión PDO_MySQL habilitada

### ✅ **Archivos Necesarios**
- [ ] `config.php` existe y es accesible
- [ ] `support_system_tables.sql` importado
- [ ] Directorio `uploads/` con permisos de escritura
- [ ] Todos los archivos `user_*.php` presentes

### ✅ **Base de Datos**
- [ ] Base de datos `rgs_support_system` creada
- [ ] Tabla `users` existe
- [ ] Tabla `support_tickets` existe
- [ ] Tabla `support_apps` existe
- [ ] Tabla `help_articles` existe
- [ ] Usuario demo creado

---

## 🚀 **URLS DE PRUEBA**

Una vez solucionado, probar estas URLs:

### **Para Usuarios:**
- `index2.php` - Página principal de servicios
- `user_tickets.php` - Gestión de tickets
- `user_chat.php` - Chat en vivo
- `user_apps.php` - Aplicaciones
- `user_help.php` - Centro de ayuda
- `user_channels.php` - Solicitar canales
- `user_activation.php` - Activar códigos

### **Para Administradores:**
- `admin_login.php` - Login de admin
- `admin2.php` - Panel de administración

### **Herramientas de Diagnóstico:**
- `debug.php` - Diagnóstico completo
- `quick_setup.php` - Configuración rápida
- `setup.php` - Instalador completo

---

## 🔑 **CREDENCIALES DE DEMO**

### **Usuario Normal:**
- Usuario: `usuario`
- Contraseña: `demo123`

### **Administrador:**
- Usuario: `admin`
- Contraseña: `admin123`

---

## 📞 **SI PERSISTEN LOS ERRORES**

### 1. **Activar Debug Completo**
Agregar al inicio de cualquier archivo PHP:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### 2. **Verificar Logs del Servidor**
```bash
# Apache
tail -f /var/log/apache2/error.log

# Nginx
tail -f /var/log/nginx/error.log
```

### 3. **Usar Configuración Simplificada**
Renombrar `config.php` a `config_backup.php` y renombrar `config_simple.php` a `config.php`

### 4. **Verificar Versión PHP**
```bash
php -v
php -m | grep pdo
```

---

## ⚡ **SOLUCIÓN EXPRESS (1 MINUTO)**

```bash
# 1. Acceder al directorio del proyecto
cd /ruta/del/proyecto

# 2. Ejecutar configuración rápida
curl http://localhost/quick_setup.php

# 3. Probar el sistema
curl http://localhost/index2.php
```

---

## 🎯 **RESULTADO ESPERADO**

Después de aplicar las soluciones:
- ✅ `index2.php` carga sin errores
- ✅ Todas las opciones del menú funcionan
- ✅ Se pueden crear tickets, usar chat, etc.
- ✅ Panel de administración accesible
- ✅ No más errores 500

---

**¿Necesitas ayuda adicional?** Ejecuta `debug.php` para un diagnóstico completo del sistema.
