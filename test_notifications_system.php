<?php
session_start();
require_once 'config.php';
require_once 'notification_triggers.php';

// Verificar que sea un administrador
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$message = '';
$error = '';

// Procesar acciones de prueba
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_chat':
            $result = notifyNewChatMessage(1, "Ho<PERSON>, necesito ayuda con mi lista M3U. No puedo reproducir los canales.", 123);
            $message = $result ? "✅ Notificación de chat creada (ID: $result)" : "❌ Error al crear notificación de chat";
            break;
            
        case 'test_ticket':
            $result = notifyNewSupportTicket(456, 2, "Problema con activación de lista", 'high');
            $message = $result ? "✅ Notificación de ticket creada (ID: $result)" : "❌ Error al crear notificación de ticket";
            break;
            
        case 'test_channel':
            $result = notifyNewChannelRequest(789, 3, "ESPN Deportes", "https://example.com/espn");
            $message = $result ? "✅ Notificación de canal creada (ID: $result)" : "❌ Error al crear notificación de canal";
            break;
            
        case 'test_app':
            $result = notifyAppDownload("RGS Player v2.1", 4, "*************");
            $message = $result ? "✅ Notificación de descarga creada (ID: $result)" : "❌ Error al crear notificación de descarga";
            break;
            
        case 'test_security':
            $result = notifySecurityAlert("Login sospechoso", "Múltiples intentos fallidos desde IP extranjera", "************", 5);
            $message = $result ? "✅ Alerta de seguridad creada (ID: $result)" : "❌ Error al crear alerta de seguridad";
            break;
            
        case 'test_error':
            $result = notifySystemError("Database Error", "Connection timeout to MySQL server", "config.php", 25);
            $message = $result ? "✅ Error del sistema reportado (ID: $result)" : "❌ Error al reportar error del sistema";
            break;
            
        case 'cleanup':
            $trigger = createNotificationTrigger();
            $deleted = $trigger->cleanupOldNotifications();
            $message = $deleted !== false ? "✅ Limpieza completada: $deleted notificaciones eliminadas" : "❌ Error en la limpieza";
            break;
            
        case 'mark_all_read':
            try {
                $stmt = $pdo->prepare("UPDATE admin_notifications SET is_read = TRUE, read_at = NOW() WHERE is_read = FALSE");
                $stmt->execute();
                $affected = $stmt->rowCount();
                $message = "✅ $affected notificaciones marcadas como leídas";
            } catch (Exception $e) {
                $error = "❌ Error: " . $e->getMessage();
            }
            break;
    }
}

// Obtener estadísticas
$trigger = createNotificationTrigger();
$stats = $trigger->getNotificationStats();

// Obtener notificaciones recientes
try {
    $stmt = $pdo->query("
        SELECT * FROM admin_notifications 
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    $recent_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $recent_notifications = [];
    $error = "Error al obtener notificaciones: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Sistema de Notificaciones - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --border-color: #334155;
            --border-radius: 12px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .test-section {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 2rem 0;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .btn {
            padding: 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary { background: var(--primary-color); color: white; }
        .btn-success { background: var(--success-color); color: white; }
        .btn-warning { background: var(--warning-color); color: white; }
        .btn-danger { background: var(--error-color); color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .message.success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .message.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .notifications-list {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .notification-item {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            border-left: 4px solid var(--primary-color);
        }

        .notification-item.unread {
            border-left-color: var(--warning-color);
            background: rgba(245, 158, 11, 0.05);
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .notification-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .notification-message {
            color: var(--text-secondary);
            font-size: 0.9rem;
            white-space: pre-line;
        }

        .priority-urgent { border-left-color: var(--error-color) !important; }
        .priority-high { border-left-color: var(--warning-color) !important; }
        .priority-normal { border-left-color: var(--primary-color) !important; }
        .priority-low { border-left-color: var(--text-secondary) !important; }

        .nav-buttons {
            text-align: center;
            margin-bottom: 2rem;
        }

        .nav-buttons a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-buttons a:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Sistema de Notificaciones en Tiempo Real</h1>

        <div class="nav-buttons">
            <a href="admin.php">🏠 Admin Principal</a>
            <a href="admin2.php">🎧 Admin Soporte</a>
            <a href="realtime_notifications.php" target="_blank">📡 Stream SSE</a>
        </div>

        <?php if ($message): ?>
            <div class="message success"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Notificaciones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['unread']; ?></div>
                <div class="stat-label">No Leídas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['urgent']; ?></div>
                <div class="stat-label">Urgentes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['high']; ?></div>
                <div class="stat-label">Alta Prioridad</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today']; ?></div>
                <div class="stat-label">Hoy</div>
            </div>
        </div>

        <!-- Botones de Prueba -->
        <div class="test-section">
            <h3 style="color: var(--primary-color); margin-bottom: 1rem;">🧪 Generar Notificaciones de Prueba</h3>
            
            <form method="POST" style="display: contents;">
                <div class="test-buttons">
                    <button type="submit" name="action" value="test_chat" class="btn btn-primary">
                        <i class="fas fa-comments"></i>
                        Simular Chat Nuevo
                    </button>
                    
                    <button type="submit" name="action" value="test_ticket" class="btn btn-warning">
                        <i class="fas fa-ticket-alt"></i>
                        Simular Ticket Nuevo
                    </button>
                    
                    <button type="submit" name="action" value="test_channel" class="btn btn-success">
                        <i class="fas fa-tv"></i>
                        Simular Solicitud Canal
                    </button>
                    
                    <button type="submit" name="action" value="test_app" class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        Simular Descarga App
                    </button>
                    
                    <button type="submit" name="action" value="test_security" class="btn btn-danger">
                        <i class="fas fa-shield-alt"></i>
                        Simular Alerta Seguridad
                    </button>
                    
                    <button type="submit" name="action" value="test_error" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Simular Error Sistema
                    </button>
                </div>
            </form>
        </div>

        <!-- Acciones de Gestión -->
        <div class="test-section">
            <h3 style="color: var(--warning-color); margin-bottom: 1rem;">⚙️ Gestión de Notificaciones</h3>
            
            <form method="POST" style="display: contents;">
                <div class="test-buttons">
                    <button type="submit" name="action" value="mark_all_read" class="btn btn-warning">
                        <i class="fas fa-check-double"></i>
                        Marcar Todas como Leídas
                    </button>
                    
                    <button type="submit" name="action" value="cleanup" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Limpiar Notificaciones Antiguas
                    </button>
                </div>
            </form>
        </div>

        <!-- Lista de Notificaciones Recientes -->
        <div class="notifications-list">
            <h3 style="color: var(--primary-color); margin-bottom: 1rem;">📋 Notificaciones Recientes</h3>
            
            <?php if (empty($recent_notifications)): ?>
                <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                    <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>No hay notificaciones</p>
                </div>
            <?php else: ?>
                <?php foreach ($recent_notifications as $notification): ?>
                    <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?> priority-<?php echo $notification['priority']; ?>">
                        <div class="notification-header">
                            <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                            <div class="notification-time">
                                <?php echo date('d/m/Y H:i', strtotime($notification['created_at'])); ?>
                                <?php if (!$notification['is_read']): ?>
                                    <span style="color: var(--warning-color); margin-left: 0.5rem;">●</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                        <div style="margin-top: 0.5rem; font-size: 0.8rem; color: var(--text-secondary);">
                            Tipo: <?php echo $notification['type']; ?> | 
                            Prioridad: <?php echo $notification['priority']; ?>
                            <?php if ($notification['reference_id']): ?>
                                | Ref: #<?php echo $notification['reference_id']; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Información del Sistema -->
        <div style="margin-top: 2rem; padding: 1rem; background: var(--dark-bg); border-radius: 8px; font-family: monospace; font-size: 0.9rem;">
            <strong>Información del Sistema:</strong><br>
            Timestamp: <?php echo date('Y-m-d H:i:s'); ?><br>
            Admin: <?php echo $_SESSION['admin_username'] ?? 'N/A'; ?><br>
            SSE Stream: <a href="realtime_notifications.php" target="_blank" style="color: var(--primary-color);">realtime_notifications.php</a><br>
            API: <a href="api_notifications.php?action=get_unread" target="_blank" style="color: var(--primary-color);">api_notifications.php</a>
        </div>
    </div>

    <!-- Sistema de Notificaciones en Tiempo Real -->
    <script src="realtime_notifications.js"></script>
    <script>
        // Configuración específica para la página de test
        document.addEventListener('DOMContentLoaded', function() {
            if (window.realtimeNotifications) {
                window.realtimeNotifications.onNotificationCallback = function(notification) {
                    console.log('🧪 Test - Nueva notificación:', notification);
                    
                    // Recargar la página para mostrar la nueva notificación
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                };
            }
        });
        
        // Auto-refresh cada 30 segundos
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
