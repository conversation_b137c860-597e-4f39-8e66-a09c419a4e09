-- Script SQL para crear las tablas del sistema de soporte
-- Base de datos: u170528143_php

-- Tabla para tickets de soporte
CREATE TABLE IF NOT EXISTS support_tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category ENUM('technical', 'billing', 'general', 'bug_report', 'feature_request') DEFAULT 'general',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('open', 'in_progress', 'waiting_user', 'resolved', 'closed') DEFAULT 'open',
    assigned_to INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_created_at (created_at)
);

-- Tabla para respuestas de tickets
CREATE TABLE IF NOT EXISTS ticket_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    is_admin_response BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_created_at (created_at)
);

-- Tabla para sesiones de chat en vivo
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT NULL,
    status ENUM('waiting', 'active', 'ended') DEFAULT 'waiting',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    rating INT NULL CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
);

-- Tabla para mensajes de chat
CREATE TABLE IF NOT EXISTS chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    sender_id INT NOT NULL,
    message TEXT NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_sent_at (sent_at)
);

-- Tabla para aplicaciones disponibles
CREATE TABLE IF NOT EXISTS support_apps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL,
    platform ENUM('android', 'ios', 'windows', 'mac', 'linux', 'web') NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    download_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_platform (platform),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
);

-- Tabla para descargas de aplicaciones
CREATE TABLE IF NOT EXISTS app_downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    app_id INT NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (app_id) REFERENCES support_apps(id) ON DELETE CASCADE,
    INDEX idx_app_id (app_id),
    INDEX idx_user_id (user_id),
    INDEX idx_downloaded_at (downloaded_at)
);

-- Tabla para contenido del centro de ayuda
CREATE TABLE IF NOT EXISTS help_content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category ENUM('faq', 'tutorial', 'guide', 'troubleshooting') NOT NULL,
    subcategory VARCHAR(100),
    is_featured BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    is_published BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_is_published (is_published),
    INDEX idx_is_featured (is_featured),
    INDEX idx_view_count (view_count)
);

-- Tabla para solicitudes de canales
CREATE TABLE IF NOT EXISTS channel_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    channel_name VARCHAR(255) NOT NULL,
    channel_description TEXT,
    channel_url VARCHAR(500),
    category VARCHAR(100),
    country VARCHAR(100),
    language VARCHAR(50),
    status ENUM('pending', 'approved', 'rejected', 'investigating') DEFAULT 'pending',
    admin_notes TEXT,
    processed_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_created_at (created_at)
);

-- Tabla para activaciones de listas M3U
CREATE TABLE IF NOT EXISTS list_activations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activation_code VARCHAR(100) UNIQUE NOT NULL,
    list_name VARCHAR(255),
    list_url VARCHAR(500),
    status ENUM('pending', 'active', 'expired', 'suspended') DEFAULT 'pending',
    expires_at TIMESTAMP NULL,
    activated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    INDEX idx_user_id (user_id),
    INDEX idx_activation_code (activation_code),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);

-- Tabla para notificaciones del sistema
CREATE TABLE IF NOT EXISTS system_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
    target_users ENUM('all', 'admins', 'specific') DEFAULT 'all',
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    INDEX idx_type (type),
    INDEX idx_target_users (target_users),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
);

-- Tabla para logs de seguridad
CREATE TABLE IF NOT EXISTS security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    details JSON,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at)
);

-- Insertar datos de ejemplo para testing

-- Contenido de ayuda de ejemplo
INSERT INTO help_content (title, content, category, subcategory, is_featured) VALUES
('¿Cómo configurar IPTV?', 'Para configurar IPTV en tu dispositivo, sigue estos pasos:\n1. Descarga la aplicación compatible\n2. Ingresa la URL de tu lista M3U\n3. Configura usuario y contraseña si es necesario\n4. Guarda la configuración', 'faq', 'configuracion', TRUE),
('Solución de problemas de buffering', 'Si experimentas buffering:\n1. Verifica tu conexión a internet\n2. Cambia la calidad de video\n3. Reinicia la aplicación\n4. Contacta soporte si persiste', 'troubleshooting', 'streaming', TRUE),
('Tutorial: Instalación en Android', 'Guía paso a paso para instalar la aplicación en Android...', 'tutorial', 'android', FALSE);

-- Aplicaciones de ejemplo
INSERT INTO support_apps (name, description, version, platform, file_path, file_size) VALUES
('IPTV Player Android', 'Reproductor IPTV optimizado para Android', '2.1.0', 'android', '/apps/iptv-player-android-2.1.0.apk', 15728640),
('IPTV Player iOS', 'Reproductor IPTV para dispositivos iOS', '2.0.5', 'ios', '/apps/iptv-player-ios-2.0.5.ipa', 18874368),
('IPTV Desktop', 'Cliente de escritorio para Windows y Mac', '1.5.2', 'windows', '/apps/iptv-desktop-1.5.2.exe', 45678912);

-- Tabla para usuarios (simplificada para demo)
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100),
    password_hash VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla para respuestas de tickets
CREATE TABLE IF NOT EXISTS ticket_responses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_id INT NOT NULL,
    user_id INT,
    message TEXT NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE
);

-- Tabla para activaciones de usuarios
CREATE TABLE IF NOT EXISTS user_activations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    activation_code_id INT NOT NULL,
    activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activation_code_id) REFERENCES activation_codes(id) ON DELETE CASCADE
);

-- Tabla para logs de actividad
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insertar usuario de demo
INSERT IGNORE INTO users (id, username, email, password_hash) VALUES
(1, 'usuario', '<EMAIL>', 'demo_hash'),
(2, 'admin', '<EMAIL>', 'admin_hash');

-- Insertar datos de ejemplo para tickets
INSERT INTO support_tickets (user_id, subject, description, priority, category, status) VALUES
(1, 'Problema con la reproducción', 'Los canales se cortan constantemente', 'high', 'technical', 'open'),
(1, 'Solicitud de canal', 'Me gustaría que agreguen CNN en español', 'medium', 'content', 'in_progress'),
(1, 'Error en la aplicación', 'La app se cierra al abrir ciertos canales', 'urgent', 'app', 'resolved');

-- Notificaciones de ejemplo
INSERT INTO system_notifications (title, message, type, target_users, created_by) VALUES
('Mantenimiento programado', 'El sistema estará en mantenimiento el domingo de 2:00 AM a 4:00 AM', 'warning', 'all', 1),
('Nueva versión disponible', 'Ya está disponible la nueva versión 2.1.0 de la aplicación Android', 'info', 'all', 1);
