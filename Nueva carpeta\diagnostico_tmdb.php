<?php
/**
 * Diagnóstico Completo del Sistema TMDB
 * RGS TOOL - Diagnóstico TMDB
 */

session_start();

// Verificar autenticación de admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnóstico TMDB - RGS TOOL</title>
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --error-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 2rem;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            color: var(--accent-color);
        }

        .diagnostic-section {
            background: var(--secondary-color);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #404040;
        }

        .diagnostic-section h2 {
            color: var(--accent-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--primary-color);
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid transparent;
        }

        .status-item.success {
            border-left-color: var(--success-color);
        }

        .status-item.warning {
            border-left-color: var(--warning-color);
        }

        .status-item.error {
            border-left-color: var(--error-color);
        }

        .status-label {
            font-weight: 500;
        }

        .status-value {
            font-family: monospace;
            background: rgba(255,255,255,0.1);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--accent-color);
            color: var(--primary-color);
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background: #3bc55a;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-primary);
            border: 1px solid #404040;
        }

        .test-result {
            background: var(--primary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
            border: 1px solid #404040;
        }

        .code-block {
            background: #0a0a0a;
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            border: 1px solid #404040;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Diagnóstico TMDB</h1>
            <p>Verificación completa del sistema TMDB API</p>
        </div>

        <!-- Configuración Básica -->
        <div class="diagnostic-section">
            <h2><i class="fas fa-cog"></i> Configuración Básica</h2>
            
            <div class="status-item <?php echo defined('TMDB_API_KEY') ? 'success' : 'error'; ?>">
                <span class="status-label">API Key Definida</span>
                <span class="status-value"><?php echo defined('TMDB_API_KEY') ? '✅ SÍ' : '❌ NO'; ?></span>
            </div>

            <?php if (defined('TMDB_API_KEY')): ?>
            <div class="status-item <?php echo (TMDB_API_KEY !== 'tu_api_key_aqui') ? 'success' : 'error'; ?>">
                <span class="status-label">API Key Configurada</span>
                <span class="status-value"><?php echo (TMDB_API_KEY !== 'tu_api_key_aqui') ? '✅ SÍ' : '❌ NO (placeholder)'; ?></span>
            </div>

            <div class="status-item success">
                <span class="status-label">API Key (parcial)</span>
                <span class="status-value"><?php echo substr(TMDB_API_KEY, 0, 8) . '...' . substr(TMDB_API_KEY, -4); ?></span>
            </div>

            <div class="status-item success">
                <span class="status-label">Longitud API Key</span>
                <span class="status-value"><?php echo strlen(TMDB_API_KEY); ?> caracteres</span>
            </div>
            <?php endif; ?>

            <div class="status-item <?php echo isTMDBConfigured() ? 'success' : 'error'; ?>">
                <span class="status-label">Función isTMDBConfigured()</span>
                <span class="status-value"><?php echo isTMDBConfigured() ? '✅ TRUE' : '❌ FALSE'; ?></span>
            </div>

            <div class="status-item success">
                <span class="status-label">URL Base TMDB</span>
                <span class="status-value"><?php echo TMDB_BASE_URL; ?></span>
            </div>

            <div class="status-item success">
                <span class="status-label">Idioma</span>
                <span class="status-value"><?php echo TMDB_LANGUAGE; ?></span>
            </div>
        </div>

        <!-- Prueba de Conectividad -->
        <div class="diagnostic-section">
            <h2><i class="fas fa-wifi"></i> Prueba de Conectividad</h2>
            
            <?php if (isTMDBConfigured()): ?>
                <?php
                echo "<p>🔄 Realizando prueba de conexión con TMDB API...</p>";
                
                $start_time = microtime(true);
                $test_result = searchTMDBContent('Avatar');
                $end_time = microtime(true);
                $response_time = round(($end_time - $start_time) * 1000, 2);
                ?>

                <div class="test-result">
                    <div class="status-item <?php echo ($test_result !== null) ? 'success' : 'error'; ?>">
                        <span class="status-label">Conexión TMDB</span>
                        <span class="status-value"><?php echo ($test_result !== null) ? '✅ EXITOSA' : '❌ FALLIDA'; ?></span>
                    </div>

                    <div class="status-item success">
                        <span class="status-label">Tiempo de Respuesta</span>
                        <span class="status-value"><?php echo $response_time; ?> ms</span>
                    </div>

                    <?php if ($test_result !== null): ?>
                        <?php if (isset($test_result['results'])): ?>
                        <div class="status-item success">
                            <span class="status-label">Resultados Encontrados</span>
                            <span class="status-value"><?php echo count($test_result['results']); ?></span>
                        </div>

                        <?php if (!empty($test_result['results'])): ?>
                            <?php $first = $test_result['results'][0]; ?>
                            <div class="status-item success">
                                <span class="status-label">Primer Resultado</span>
                                <span class="status-value"><?php echo htmlspecialchars($first['title'] ?? $first['name'] ?? 'N/A'); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php else: ?>
                        <div class="status-item error">
                            <span class="status-label">Error API</span>
                            <span class="status-value"><?php echo htmlspecialchars($test_result['status_message'] ?? 'Error desconocido'); ?></span>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>

                <!-- Mostrar respuesta completa -->
                <details style="margin-top: 1rem;">
                    <summary style="cursor: pointer; color: var(--accent-color); font-weight: 500;">
                        📋 Ver Respuesta Completa de la API
                    </summary>
                    <div class="code-block" style="margin-top: 1rem;">
                        <?php echo htmlspecialchars(json_encode($test_result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?>
                    </div>
                </details>

            <?php else: ?>
                <div class="status-item error">
                    <span class="status-label">Estado</span>
                    <span class="status-value">❌ TMDB no configurado</span>
                </div>
                
                <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid var(--error-color); border-radius: 8px; padding: 1rem; margin-top: 1rem;">
                    <h4 style="color: var(--error-color); margin-bottom: 1rem;">🔧 Para Solucionar:</h4>
                    <ol style="color: var(--text-secondary); line-height: 1.8;">
                        <li>Verifica que el archivo <code>tmdb_config.php</code> existe</li>
                        <li>Asegúrate de que la API key esté correctamente configurada</li>
                        <li>La API key debe tener 32 caracteres hexadecimales</li>
                        <li>Verifica que no haya espacios extra o caracteres especiales</li>
                    </ol>
                </div>
            <?php endif; ?>
        </div>

        <!-- Información del Sistema -->
        <div class="diagnostic-section">
            <h2><i class="fas fa-server"></i> Información del Sistema</h2>
            
            <div class="status-item success">
                <span class="status-label">PHP Version</span>
                <span class="status-value"><?php echo PHP_VERSION; ?></span>
            </div>

            <div class="status-item <?php echo function_exists('file_get_contents') ? 'success' : 'error'; ?>">
                <span class="status-label">file_get_contents()</span>
                <span class="status-value"><?php echo function_exists('file_get_contents') ? '✅ Disponible' : '❌ No disponible'; ?></span>
            </div>

            <div class="status-item <?php echo function_exists('json_decode') ? 'success' : 'error'; ?>">
                <span class="status-label">JSON Support</span>
                <span class="status-value"><?php echo function_exists('json_decode') ? '✅ Disponible' : '❌ No disponible'; ?></span>
            </div>

            <div class="status-item <?php echo ini_get('allow_url_fopen') ? 'success' : 'error'; ?>">
                <span class="status-label">allow_url_fopen</span>
                <span class="status-value"><?php echo ini_get('allow_url_fopen') ? '✅ Habilitado' : '❌ Deshabilitado'; ?></span>
            </div>
        </div>

        <!-- Acciones -->
        <div style="text-align: center; margin-top: 3rem;">
            <a href="m3u_search_tmdb.php" class="btn">
                <i class="fas fa-search"></i>
                Ir a Búsqueda M3U
            </a>
            <a href="admin.php" class="btn btn-secondary">
                <i class="fas fa-home"></i>
                Volver al Admin
            </a>
            <button onclick="location.reload()" class="btn btn-secondary">
                <i class="fas fa-sync-alt"></i>
                Actualizar Diagnóstico
            </button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
