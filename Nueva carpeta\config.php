<?php
// Configuración de la base de datos
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

// Configuración del sistema
define('SITE_URL', 'http://localhost');
define('ADMIN_EMAIL', '<EMAIL>');
define('SYSTEM_NAME', 'RGS Support System');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB

// Configuración de sesión
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Cambiar a 1 si usas HTTPS

// Conexión a la base de datos
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // En producción, no mostrar detalles del error
    if (isset($_GET['debug'])) {
        die("Error de conexión: " . $e->getMessage());
    } else {
        die("Error de conexión a la base de datos. Contacte al administrador.");
    }
}

// Función para limpiar datos de entrada
function clean_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Función para generar token CSRF
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Función para verificar token CSRF
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Función para formatear fechas
function format_date($date, $format = 'd/m/Y H:i') {
    return date($format, strtotime($date));
}

// Función para formatear tamaños de archivo
function format_file_size($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Función para registrar actividad
function log_activity($pdo, $user_id, $action, $details = null) {
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            $user_id,
            $action,
            $details ? json_encode($details) : null,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // Log error but don't break the application
        error_log("Error logging activity: " . $e->getMessage());
    }
}

// Función para enviar notificaciones por email (básica)
function send_notification_email($to, $subject, $message) {
    $headers = "From: " . ADMIN_EMAIL . "\r\n";
    $headers .= "Reply-To: " . ADMIN_EMAIL . "\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}

// Función para verificar permisos de archivo
function check_upload_permissions() {
    $upload_dir = UPLOAD_PATH;
    
    if (!file_exists($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            return false;
        }
    }
    
    return is_writable($upload_dir);
}

// Función para limpiar nombre de archivo
function clean_filename($filename) {
    // Remover caracteres especiales y espacios
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    // Evitar nombres de archivo muy largos
    if (strlen($filename) > 100) {
        $ext = pathinfo($filename, PATHINFO_EXTENSION);
        $name = substr(pathinfo($filename, PATHINFO_FILENAME), 0, 90);
        $filename = $name . '.' . $ext;
    }
    return $filename;
}

// Función para validar tipos de archivo
function validate_file_type($filename, $allowed_types = []) {
    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    if (empty($allowed_types)) {
        // Tipos permitidos por defecto
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'zip', 'rar', 'apk', 'ipa'];
    }
    
    return in_array($ext, $allowed_types);
}

// Función para generar código único
function generate_unique_code($length = 8) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

// Función para verificar si es una solicitud AJAX
function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// Función para respuesta JSON
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Función para verificar si el usuario es admin
function is_admin() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// Función para redirigir
function redirect($url) {
    header("Location: $url");
    exit;
}

// Configuración de zona horaria
date_default_timezone_set('Europe/Madrid');

// Configuración de errores (solo en desarrollo)
if (isset($_GET['debug']) || (defined('ENVIRONMENT') && ENVIRONMENT === 'development')) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Crear directorio de uploads si no existe
if (!file_exists(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}

// Crear subdirectorios necesarios
$subdirs = ['apps', 'tickets', 'temp', 'avatars'];
foreach ($subdirs as $subdir) {
    $path = UPLOAD_PATH . $subdir;
    if (!file_exists($path)) {
        mkdir($path, 0755, true);
    }
}

// Configuración de límites de memoria y tiempo
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);

// Headers de seguridad básicos
if (!headers_sent()) {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
}

// Función para obtener IP real del usuario
function get_real_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}

// Función para verificar rate limiting básico
function check_rate_limit($identifier, $max_attempts = 5, $time_window = 300) {
    // En una implementación real, esto usaría Redis o una tabla de BD
    // Por ahora, usamos sesión para demostración
    $key = 'rate_limit_' . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
    }
    
    $data = $_SESSION[$key];
    
    // Resetear si ha pasado el tiempo
    if (time() - $data['first_attempt'] > $time_window) {
        $_SESSION[$key] = ['count' => 1, 'first_attempt' => time()];
        return true;
    }
    
    // Verificar límite
    if ($data['count'] >= $max_attempts) {
        return false;
    }
    
    $_SESSION[$key]['count']++;
    return true;
}

// Función para limpiar sesiones antiguas (llamar periódicamente)
function cleanup_old_sessions($pdo) {
    try {
        // Limpiar sesiones de chat antiguas
        $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'ended' WHERE status = 'active' AND started_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stmt->execute();
        
        // Limpiar logs antiguos (más de 90 días)
        $stmt = $pdo->prepare("DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)");
        $stmt->execute();
        
        // Limpiar archivos temporales
        $temp_dir = UPLOAD_PATH . 'temp/';
        if (is_dir($temp_dir)) {
            $files = glob($temp_dir . '*');
            foreach ($files as $file) {
                if (is_file($file) && time() - filemtime($file) > 86400) { // 24 horas
                    unlink($file);
                }
            }
        }
    } catch (Exception $e) {
        error_log("Error in cleanup: " . $e->getMessage());
    }
}

// Ejecutar limpieza ocasionalmente (1% de probabilidad)
if (rand(1, 100) === 1) {
    cleanup_old_sessions($pdo);
}

// Constantes para estados
define('TICKET_STATUS_OPEN', 'open');
define('TICKET_STATUS_IN_PROGRESS', 'in_progress');
define('TICKET_STATUS_RESOLVED', 'resolved');
define('TICKET_STATUS_CLOSED', 'closed');

define('TICKET_PRIORITY_LOW', 'low');
define('TICKET_PRIORITY_MEDIUM', 'medium');
define('TICKET_PRIORITY_HIGH', 'high');
define('TICKET_PRIORITY_URGENT', 'urgent');

define('CHAT_STATUS_WAITING', 'waiting');
define('CHAT_STATUS_ACTIVE', 'active');
define('CHAT_STATUS_ENDED', 'ended');

// Configuración de notificaciones
define('NOTIFICATION_TYPE_INFO', 'info');
define('NOTIFICATION_TYPE_WARNING', 'warning');
define('NOTIFICATION_TYPE_ERROR', 'error');
define('NOTIFICATION_TYPE_SUCCESS', 'success');

// Función para debug (solo en desarrollo)
function debug_log($message, $data = null) {
    if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
        $log_message = "[" . date('Y-m-d H:i:s') . "] " . $message;
        if ($data !== null) {
            $log_message .= " | Data: " . print_r($data, true);
        }
        error_log($log_message);
    }
}

// Verificar si las tablas existen
function check_database_setup($pdo) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'support_tickets'");
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Función para verificar si el usuario está logueado
function require_user_login() {
    if (!isset($_SESSION['user_id'])) {
        $current_url = $_SERVER['REQUEST_URI'];
        header("Location: login.php?redirect=" . urlencode($current_url));
        exit;
    }
}

// Función para verificar si el admin está logueado
function require_admin_login() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: admin_login.php');
        exit;
    }
}

// Función para crear usuario demo si no existe
function ensure_demo_user($pdo) {
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'usuario'");
        $stmt->execute();
        if (!$stmt->fetch()) {
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash) VALUES ('usuario', '<EMAIL>', 'demo_hash')");
            $stmt->execute();
        }
    } catch (Exception $e) {
        // Tabla users no existe, se creará con el SQL
    }
}

// Verificar configuración inicial
if (!check_database_setup($pdo)) {
    if (isset($_GET['setup'])) {
        echo "<h2>Base de datos no configurada</h2>";
        echo "<p>Por favor, ejecuta el archivo <code>support_system_tables.sql</code> en tu base de datos MySQL.</p>";
        echo "<p>Luego accede a cualquier página sin el parámetro ?setup</p>";
        exit;
    }
} else {
    // Asegurar que existe el usuario demo
    ensure_demo_user($pdo);
}
?>
