<?php
// Analizador M3U con debug mejorado
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$message = '';
$message_type = '';
$debug_info = [];

// Función para log de debug
function debugLog($message) {
    global $debug_info;
    $debug_info[] = date('H:i:s') . " - " . $message;
    echo "<script>console.log('" . addslashes($message) . "');</script>";
    flush();
    ob_flush();
}

// Función simplificada para analizar M3U
function analyzeM3uSimple($url, $username = null, $password = null) {
    debugLog("Iniciando descarga de: $url");
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    if ($username && $password) {
        debugLog("Usando autenticación básica");
        $auth = base64_encode("$username:$password");
        $context = stream_context_create([
            'http' => [
                'header' => "Authorization: Basic $auth\r\n",
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
    }
    
    $content = @file_get_contents($url, false, $context);
    
    if ($content === false) {
        $error = error_get_last();
        debugLog("Error descargando: " . ($error['message'] ?? 'Error desconocido'));
        throw new Exception("No se pudo descargar la lista M3U. Error: " . ($error['message'] ?? 'Desconocido'));
    }
    
    debugLog("Descarga exitosa. Tamaño: " . strlen($content) . " bytes");
    
    if (strlen($content) < 10) {
        throw new Exception("El archivo descargado está vacío o es muy pequeño");
    }
    
    // Verificar que sea un archivo M3U válido
    if (strpos($content, '#EXTM3U') === false && strpos($content, '#EXTINF') === false) {
        debugLog("Contenido no parece ser M3U válido. Primeras 200 chars: " . substr($content, 0, 200));
        throw new Exception("El archivo no parece ser un M3U válido");
    }
    
    debugLog("Archivo M3U válido detectado");
    
    $lines = explode("\n", $content);
    $items = [];
    $current_item = null;
    $processed = 0;
    
    debugLog("Procesando " . count($lines) . " líneas");
    
    foreach ($lines as $line_num => $line) {
        $line = trim($line);
        
        if (strpos($line, '#EXTINF:') === 0) {
            $current_item = [];
            
            // Extraer título básico
            if (preg_match('/#EXTINF:[^,]*,(.*)/', $line, $matches)) {
                $current_item['title'] = trim($matches[1]);
            } else {
                $current_item['title'] = 'Sin título';
            }
            
            // Extraer duración
            if (preg_match('/#EXTINF:([^,]+)/', $line, $matches)) {
                $current_item['duration'] = (float)$matches[1];
            }
            
        } elseif ($line && !empty($current_item) && strpos($line, '#') !== 0) {
            // Esta es la URL del stream
            $current_item['url'] = $line;
            
            if (!empty($current_item['title'])) {
                $title = $current_item['title'];
                $clean_title = strtolower(preg_replace('/[^\w\s]/', '', $title));
                
                // Detectar tipo básico
                $media_type = 'unknown';
                if (preg_match('/s\d+e\d+|season|temporada|\d+x\d+/i', $title)) {
                    $media_type = 'tv';
                } elseif (preg_match('/\(\d{4}\)|\d{4}|movie|film|película/i', $title)) {
                    $media_type = 'movie';
                }
                
                // Extraer año
                $year = null;
                if (preg_match('/\((\d{4})\)/', $title, $matches)) {
                    $year = (int)$matches[1];
                } elseif (preg_match('/(\d{4})/', $title, $matches)) {
                    $test_year = (int)$matches[1];
                    if ($test_year >= 1900 && $test_year <= date('Y') + 2) {
                        $year = $test_year;
                    }
                }
                
                $items[] = [
                    'title' => $title,
                    'clean_title' => $clean_title,
                    'url' => $current_item['url'],
                    'media_type' => $media_type,
                    'year' => $year,
                    'season' => null,
                    'episode' => null,
                    'duration' => $current_item['duration'] ?? null
                ];
                
                $processed++;
                if ($processed % 100 == 0) {
                    debugLog("Procesados $processed elementos...");
                }
            }
            
            $current_item = null;
        }
    }
    
    debugLog("Análisis completado. Total elementos: " . count($items));
    return $items;
}

// Procesar análisis
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['analyze'])) {
    $list_id = $_POST['list_id'];
    
    debugLog("=== INICIANDO ANÁLISIS ===");
    
    try {
        // Obtener datos de la lista
        $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
        $stmt->execute([$list_id]);
        $list = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$list) {
            throw new Exception("Lista no encontrada");
        }
        
        debugLog("Lista encontrada: " . $list['name']);
        debugLog("URL: " . $list['url']);
        
        // Analizar contenido
        $items = analyzeM3uSimple($list['url'], $list['username'], $list['password']);
        
        debugLog("Limpiando contenido anterior...");
        
        // Limpiar contenido anterior
        $stmt = $pdo->prepare("DELETE FROM m3u_content WHERE list_id = ?");
        $stmt->execute([$list_id]);
        
        debugLog("Insertando nuevo contenido...");
        
        // Insertar nuevo contenido
        $stmt = $pdo->prepare("
            INSERT INTO m3u_content 
            (list_id, title, clean_title, media_type, year, season, episode, url, duration) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $inserted = 0;
        foreach ($items as $item) {
            $stmt->execute([
                $list_id,
                $item['title'],
                $item['clean_title'],
                $item['media_type'],
                $item['year'],
                $item['season'],
                $item['episode'],
                $item['url'],
                $item['duration']
            ]);
            $inserted++;
        }
        
        debugLog("Insertados $inserted elementos");
        
        // Actualizar estadísticas
        $stmt = $pdo->prepare("
            UPDATE m3u_lists 
            SET last_scan = NOW(), total_items = ?, last_updated = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$inserted, $list_id]);
        
        debugLog("Estadísticas actualizadas");
        
        $analysis_results = [
            'total_items' => $inserted,
            'movies' => count(array_filter($items, fn($i) => $i['media_type'] === 'movie')),
            'tv_shows' => count(array_filter($items, fn($i) => $i['media_type'] === 'tv')),
            'unknown' => count(array_filter($items, fn($i) => $i['media_type'] === 'unknown')),
            'with_year' => count(array_filter($items, fn($i) => $i['year'] !== null))
        ];
        
        $message = "Análisis completado exitosamente. Se procesaron $inserted elementos.";
        $message_type = "success";
        
        debugLog("=== ANÁLISIS COMPLETADO ===");
        
    } catch (Exception $e) {
        debugLog("ERROR: " . $e->getMessage());
        $message = "Error durante el análisis: " . $e->getMessage();
        $message_type = "error";
    }
}

// Obtener listas
$stmt = $pdo->query("SELECT id, name, is_active, last_scan, total_items FROM m3u_lists ORDER BY name");
$all_lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Analizador M3U Debug - RGS TOOL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; }
        .container { max-width: 1000px; margin: 0 auto; }
        .success { color: #28a745; background: rgba(40, 167, 69, 0.2); padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: rgba(220, 53, 69, 0.2); padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { background: #2d2d2d; padding: 20px; border-radius: 10px; margin: 20px 0; border: 1px solid #404040; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; color: #b0b0b0; }
        .form-input { width: 100%; padding: 10px; border: 1px solid #404040; border-radius: 5px; background: #1a1a1a; color: white; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn-primary { background: #46d347; color: #1a1a1a; }
        .back-link { color: #46d347; text-decoration: none; margin-bottom: 20px; display: inline-block; }
        .debug-log { background: #1a1a1a; padding: 15px; border-radius: 8px; margin: 20px 0; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .results { background: rgba(40, 167, 69, 0.1); padding: 20px; border-radius: 10px; margin: 20px 0; }
        .result-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px; }
        .result-item { text-align: center; }
        .result-number { font-size: 1.5em; color: #46d347; font-weight: bold; }
        .result-label { color: #b0b0b0; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <a href="m3u_manager.php" class="back-link">← Volver al Gestor M3U</a>
        
        <h1>🔍 Analizador M3U - Versión Debug</h1>

        <?php if ($message): ?>
        <div class="<?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <div class="section">
            <h2>🔧 Análisis de Lista M3U</h2>
            <form method="POST" id="analyzeForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista M3U</label>
                    <select name="list_id" id="list_id" class="form-input" required>
                        <option value="">Seleccionar lista...</option>
                        <?php foreach ($all_lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?>
                            (<?php echo $list['total_items']; ?> elementos)
                            <?php if (!$list['is_active']): ?>(Inactiva)<?php endif; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="submit" name="analyze" class="btn btn-primary" onclick="showProgress()">
                    🔍 Iniciar Análisis
                </button>
            </form>
            
            <div id="progress" style="display: none; margin-top: 20px;">
                <p>⏳ Analizando lista M3U...</p>
                <p><small>Este proceso puede tomar varios minutos. Revisa la consola del navegador para más detalles.</small></p>
            </div>
        </div>

        <?php if (!empty($debug_info)): ?>
        <div class="section">
            <h2>📋 Log de Debug</h2>
            <div class="debug-log">
                <?php foreach ($debug_info as $log): ?>
                <div><?php echo htmlspecialchars($log); ?></div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isset($analysis_results)): ?>
        <div class="results">
            <h2>📊 Resultados del Análisis</h2>
            <div class="result-grid">
                <div class="result-item">
                    <div class="result-number"><?php echo $analysis_results['total_items']; ?></div>
                    <div class="result-label">Total</div>
                </div>
                <div class="result-item">
                    <div class="result-number"><?php echo $analysis_results['movies']; ?></div>
                    <div class="result-label">Películas</div>
                </div>
                <div class="result-item">
                    <div class="result-number"><?php echo $analysis_results['tv_shows']; ?></div>
                    <div class="result-label">Series</div>
                </div>
                <div class="result-item">
                    <div class="result-number"><?php echo $analysis_results['unknown']; ?></div>
                    <div class="result-label">Sin clasificar</div>
                </div>
                <div class="result-item">
                    <div class="result-number"><?php echo $analysis_results['with_year']; ?></div>
                    <div class="result-label">Con año</div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="section">
            <h2>🛠️ Herramientas de Debug</h2>
            <p><a href="m3u_analyzer.php" style="color: #46d347;">📊 Analizador Normal</a></p>
            <p><a href="m3u_manager.php" style="color: #46d347;">📡 Gestor M3U</a></p>
            <p><a href="admin.php" style="color: #46d347;">🏠 Panel Admin</a></p>
        </div>
    </div>

    <script>
        function showProgress() {
            document.getElementById('progress').style.display = 'block';
            console.log('Iniciando análisis M3U...');
        }
        
        // Log adicional
        console.log('Analizador M3U Debug cargado');
    </script>
</body>
</html>
