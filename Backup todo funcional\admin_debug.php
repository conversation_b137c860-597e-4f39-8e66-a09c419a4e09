<?php
// Versión de debug del admin.php para encontrar el error exacto
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "DEBUG: Iniciando admin_debug.php<br>";

session_start();
echo "DEBUG: Session iniciada<br>";

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo "DEBUG: No es admin, redirigiendo<br>";
    header('Location: admin_login.php');
    exit;
}

echo "DEBUG: Usuario es admin<br>";

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "DEBUG: Variables de BD definidas<br>";

try {
    echo "DEBUG: Intentando conectar a BD<br>";
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "DEBUG: Conexión a BD exitosa<br>";
} catch(PDOException $e) {
    echo "DEBUG: Error de conexión: " . $e->getMessage() . "<br>";
    die('Error de conexión: ' . $e->getMessage());
}

echo "DEBUG: Intentando obtener estadísticas<br>";

// Obtener estadísticas una por una para identificar cuál falla
try {
    echo "DEBUG: Obteniendo total_orders<br>";
    $stats['total_orders'] = $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn();
    echo "DEBUG: total_orders = " . $stats['total_orders'] . "<br>";
    
    echo "DEBUG: Obteniendo pending_orders<br>";
    $stats['pending_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status IN ('Recibido', 'En Cola')")->fetchColumn();
    echo "DEBUG: pending_orders = " . $stats['pending_orders'] . "<br>";
    
    echo "DEBUG: Obteniendo processing_orders<br>";
    $stats['processing_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'Procesando'")->fetchColumn();
    echo "DEBUG: processing_orders = " . $stats['processing_orders'] . "<br>";
    
    echo "DEBUG: Obteniendo completed_orders<br>";
    $stats['completed_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'Listo'")->fetchColumn();
    echo "DEBUG: completed_orders = " . $stats['completed_orders'] . "<br>";
    
    echo "DEBUG: Obteniendo unavailable_orders<br>";
    $stats['unavailable_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'No disponible'")->fetchColumn();
    echo "DEBUG: unavailable_orders = " . $stats['unavailable_orders'] . "<br>";
    
    echo "DEBUG: Obteniendo new_orders<br>";
    $stats['new_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE notif_seen = 0")->fetchColumn();
    echo "DEBUG: new_orders = " . $stats['new_orders'] . "<br>";
    
} catch(PDOException $e) {
    echo "DEBUG: Error en estadísticas: " . $e->getMessage() . "<br>";
    die('Error en estadísticas: ' . $e->getMessage());
}

echo "DEBUG: Estadísticas obtenidas exitosamente<br>";

// Obtener pedidos recientes
try {
    echo "DEBUG: Intentando obtener pedidos recientes<br>";
    $stmt = $pdo->query("
        SELECT o.*, u.username, u.is_cliente_actual, u.is_mavistv, u.is_tvdigital, u.is_limites507
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "DEBUG: Pedidos obtenidos: " . count($orders) . "<br>";
} catch(PDOException $e) {
    echo "DEBUG: Error en pedidos: " . $e->getMessage() . "<br>";
    die('Error en pedidos: ' . $e->getMessage());
}

echo "DEBUG: Todo funcionó correctamente hasta aquí<br>";
echo "DEBUG: El problema debe estar en el HTML o CSS<br>";

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Debug Admin - RogsMediaTV</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #141414; 
            color: white; 
        }
        .debug { 
            background: #333; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>🔐 Panel de Administración - DEBUG</h1>
    
    <div class="debug">
        <h2>Estadísticas:</h2>
        <p>Total de pedidos: <?php echo $stats['total_orders']; ?></p>
        <p>Pedidos pendientes: <?php echo $stats['pending_orders']; ?></p>
        <p>Pedidos procesando: <?php echo $stats['processing_orders']; ?></p>
        <p>Pedidos completados: <?php echo $stats['completed_orders']; ?></p>
        <p>Pedidos no disponibles: <?php echo $stats['unavailable_orders']; ?></p>
        <p>Pedidos nuevos: <?php echo $stats['new_orders']; ?></p>
    </div>
    
    <div class="debug">
        <h2>Últimos 5 Pedidos:</h2>
        <?php if (!empty($orders)): ?>
            <ul>
                <?php foreach ($orders as $order): ?>
                    <li>
                        #<?php echo $order['id']; ?> - 
                        <?php echo htmlspecialchars($order['title']); ?> - 
                        <?php echo $order['status']; ?> - 
                        Usuario: <?php echo htmlspecialchars($order['username'] ?? 'N/A'); ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php else: ?>
            <p>No hay pedidos</p>
        <?php endif; ?>
    </div>
    
    <div class="debug">
        <h2>Enlaces:</h2>
        <p><a href="admin.php" style="color: #ffc107;">🔄 Probar admin.php original</a></p>
        <p><a href="index.php" style="color: #28a745;">🏠 Volver al inicio</a></p>
        <p><a href="admin_logout.php" style="color: #dc3545;">🚪 Cerrar sesión</a></p>
    </div>
    
    <script>
        console.log('Admin debug page loaded successfully');
    </script>
</body>
</html>
