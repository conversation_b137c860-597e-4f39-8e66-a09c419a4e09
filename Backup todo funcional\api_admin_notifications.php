<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Acceso denegado']);
    exit;
}

header('Content-Type: application/json');

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_notifications':
            // Obtener notificaciones no leídas
            $stmt = $pdo->prepare("
                SELECT an.*, 
                       CASE 
                           WHEN an.type = 'ticket' THEN CONCAT('Ticket #', an.reference_id)
                           WHEN an.type = 'chat' THEN CONCAT('Chat #', an.reference_id)
                           WHEN an.type = 'channel_request' THEN CONCAT('Canal #', an.reference_id)
                           WHEN an.type = 'app_download' THEN CONCAT('App #', an.reference_id)
                           ELSE 'Sistema'
                       END as reference_label
                FROM admin_notifications an
                WHERE an.is_read = 0
                ORDER BY an.created_at DESC
                LIMIT 20
            ");
            $stmt->execute();
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Obtener contadores por tipo
            $stmt = $pdo->prepare("
                SELECT 
                    type,
                    COUNT(*) as count
                FROM admin_notifications 
                WHERE is_read = 0 
                GROUP BY type
            ");
            $stmt->execute();
            $counters = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $counters_array = [];
            foreach ($counters as $counter) {
                $counters_array[$counter['type']] = $counter['count'];
            }
            
            echo json_encode([
                'success' => true, 
                'notifications' => $notifications,
                'counters' => $counters_array,
                'total_unread' => array_sum($counters_array)
            ]);
            break;
            
        case 'mark_read':
            $notification_id = (int)($_POST['notification_id'] ?? 0);
            
            if ($notification_id) {
                $stmt = $pdo->prepare("UPDATE admin_notifications SET is_read = 1 WHERE id = ?");
                $stmt->execute([$notification_id]);
            }
            
            echo json_encode(['success' => true]);
            break;
            
        case 'mark_all_read':
            $type = $_POST['type'] ?? '';
            
            if ($type) {
                $stmt = $pdo->prepare("UPDATE admin_notifications SET is_read = 1 WHERE type = ? AND is_read = 0");
                $stmt->execute([$type]);
            } else {
                $stmt = $pdo->prepare("UPDATE admin_notifications SET is_read = 1 WHERE is_read = 0");
                $stmt->execute();
            }
            
            echo json_encode(['success' => true]);
            break;
            
        case 'get_stats':
            // Estadísticas para el dashboard
            $stats = [];
            
            // Tickets pendientes
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM support_tickets WHERE status IN ('open', 'in_progress')");
            $stats['pending_tickets'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Chats activos
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_sessions WHERE status IN ('waiting', 'active')");
            $stats['active_chats'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Solicitudes de canales pendientes
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM channel_requests WHERE status = 'pending'");
            $stats['pending_channels'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Descargas de apps hoy
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_downloads WHERE DATE(downloaded_at) = CURDATE()");
            $stats['today_downloads'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Nuevos tickets hoy
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM support_tickets WHERE DATE(created_at) = CURDATE()");
            $stats['today_tickets'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Chats finalizados hoy
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM chat_sessions WHERE DATE(ended_at) = CURDATE() AND status = 'ended'");
            $stats['today_chats'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            echo json_encode(['success' => true, 'stats' => $stats]);
            break;
            
        case 'create_notification':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Método no permitido');
            }
            
            $type = $_POST['type'] ?? '';
            $title = $_POST['title'] ?? '';
            $message = $_POST['message'] ?? '';
            $reference_id = (int)($_POST['reference_id'] ?? 0);
            $reference_type = $_POST['reference_type'] ?? '';
            
            if (empty($type) || empty($title) || empty($message)) {
                throw new Exception('Datos incompletos');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$type, $title, $message, $reference_id, $reference_type]);
            
            echo json_encode(['success' => true, 'notification_id' => $pdo->lastInsertId()]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
