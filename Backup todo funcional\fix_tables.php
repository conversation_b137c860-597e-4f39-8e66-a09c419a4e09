<?php
// Corregir estructura de tablas
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🔧 Corrigiendo Estructura de Tablas</h1>";

try {
    // 1. Verificar y corregir tabla support_tickets
    echo "<h2>🎫 Corrigiendo tabla support_tickets</h2>";
    
    // Verificar si la tabla existe y su estructura
    $stmt = $pdo->query("SHOW TABLES LIKE 'support_tickets'");
    if ($stmt->rowCount() > 0) {
        // Verificar columnas
        $stmt = $pdo->query("DESCRIBE support_tickets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('subject', $columns)) {
            echo "❌ Tabla support_tickets tiene estructura incorrecta<br>";
            echo "Recreando tabla...<br>";
            
            // Eliminar y recrear
            $pdo->exec("DROP TABLE IF EXISTS support_tickets");
            $pdo->exec("
                CREATE TABLE support_tickets (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    subject VARCHAR(255) NOT NULL,
                    description TEXT NOT NULL,
                    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                    category VARCHAR(50) NOT NULL,
                    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
                    assigned_to INT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            echo "✅ Tabla support_tickets recreada correctamente<br>";
        } else {
            echo "✅ Tabla support_tickets ya tiene la estructura correcta<br>";
        }
    } else {
        // Crear tabla desde cero
        $pdo->exec("
            CREATE TABLE support_tickets (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                subject VARCHAR(255) NOT NULL,
                description TEXT NOT NULL,
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                category VARCHAR(50) NOT NULL,
                status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
                assigned_to INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "✅ Tabla support_tickets creada<br>";
    }
    
    // 2. Verificar y corregir otras tablas importantes
    echo "<h2>📋 Verificando otras tablas</h2>";
    
    $tables_to_check = [
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100),
                password_hash VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'ticket_responses' => "
            CREATE TABLE IF NOT EXISTS ticket_responses (
                id INT PRIMARY KEY AUTO_INCREMENT,
                ticket_id INT NOT NULL,
                user_id INT,
                message TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        
        'chat_sessions' => "
            CREATE TABLE IF NOT EXISTS chat_sessions (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                status ENUM('waiting', 'active', 'ended') DEFAULT 'waiting',
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ended_at TIMESTAMP NULL,
                rating INT NULL
            )",
        
        'chat_messages' => "
            CREATE TABLE IF NOT EXISTS chat_messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                session_id INT NOT NULL,
                sender_id INT NOT NULL,
                message TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        
        'support_apps' => "
            CREATE TABLE IF NOT EXISTS support_apps (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                version VARCHAR(50) NOT NULL,
                platform ENUM('android', 'ios', 'windows', 'macos', 'smart_tv', 'firestick', 'web', 'other') NOT NULL,
                description TEXT,
                features TEXT,
                file_path VARCHAR(500),
                file_size BIGINT,
                download_url VARCHAR(500),
                external_url VARCHAR(500),
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'help_articles' => "
            CREATE TABLE IF NOT EXISTS help_articles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                category ENUM('setup', 'troubleshooting', 'apps', 'channels', 'billing', 'account', 'general') NOT NULL,
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                is_featured BOOLEAN DEFAULT FALSE,
                views INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'channel_requests' => "
            CREATE TABLE IF NOT EXISTS channel_requests (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                channel_name VARCHAR(255) NOT NULL,
                channel_url VARCHAR(500),
                country VARCHAR(10) NOT NULL,
                language VARCHAR(50) NOT NULL,
                category VARCHAR(50) NOT NULL,
                description TEXT,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'activation_codes' => "
            CREATE TABLE IF NOT EXISTS activation_codes (
                id INT PRIMARY KEY AUTO_INCREMENT,
                code VARCHAR(50) UNIQUE NOT NULL,
                list_name VARCHAR(255) NOT NULL,
                description TEXT,
                status ENUM('active', 'used', 'expired', 'disabled') DEFAULT 'active',
                expires_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'user_activations' => "
            CREATE TABLE IF NOT EXISTS user_activations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                activation_code_id INT NOT NULL,
                activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        
        'activity_logs' => "
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT,
                action VARCHAR(100) NOT NULL,
                details TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"
    ];
    
    foreach ($tables_to_check as $table_name => $sql) {
        try {
            $pdo->exec($sql);
            echo "✅ Tabla '$table_name' verificada/creada<br>";
        } catch (Exception $e) {
            echo "❌ Error con tabla '$table_name': " . $e->getMessage() . "<br>";
        }
    }
    
    // 3. Insertar usuarios básicos
    echo "<h2>👥 Creando usuarios básicos</h2>";
    try {
        $pdo->exec("INSERT IGNORE INTO users (id, username, email, password_hash) VALUES (1, 'usuario', '<EMAIL>', 'demo_hash')");
        $pdo->exec("INSERT IGNORE INTO users (id, username, email, password_hash) VALUES (2, 'admin', '<EMAIL>', 'admin_hash')");
        echo "✅ Usuarios básicos creados<br>";
    } catch (Exception $e) {
        echo "⚠️ Usuarios ya existen o error: " . $e->getMessage() . "<br>";
    }
    
    // 4. Insertar algunos datos básicos
    echo "<h2>📊 Insertando datos básicos</h2>";
    
    // Apps básicas
    try {
        $pdo->exec("
            INSERT IGNORE INTO support_apps (id, name, version, platform, description, status) VALUES
            (1, 'RGS IPTV Android', '2.1.0', 'android', 'Aplicación oficial para Android', 'published'),
            (2, 'RGS IPTV iOS', '2.0.5', 'ios', 'Aplicación oficial para iOS', 'published'),
            (3, 'RGS Player Windows', '1.8.2', 'windows', 'Reproductor para Windows', 'published')
        ");
        echo "✅ Aplicaciones básicas insertadas<br>";
    } catch (Exception $e) {
        echo "⚠️ Error insertando apps: " . $e->getMessage() . "<br>";
    }
    
    // Artículos básicos
    try {
        $pdo->exec("
            INSERT IGNORE INTO help_articles (id, title, content, category, status, is_featured) VALUES
            (1, 'Configuración básica', 'Guía para configurar tu servicio IPTV', 'setup', 'published', 1),
            (2, 'Solución de problemas', 'Cómo resolver problemas comunes', 'troubleshooting', 'published', 1),
            (3, 'Instalación de apps', 'Cómo instalar las aplicaciones', 'apps', 'published', 0)
        ");
        echo "✅ Artículos básicos insertados<br>";
    } catch (Exception $e) {
        echo "⚠️ Error insertando artículos: " . $e->getMessage() . "<br>";
    }
    
    // Códigos básicos
    try {
        $pdo->exec("
            INSERT IGNORE INTO activation_codes (id, code, list_name, description, status) VALUES
            (1, 'DEMO2024', 'Lista Demo', 'Código de demostración', 'active'),
            (2, 'TEST123', 'Lista Prueba', 'Lista de prueba', 'active')
        ");
        echo "✅ Códigos básicos insertados<br>";
    } catch (Exception $e) {
        echo "⚠️ Error insertando códigos: " . $e->getMessage() . "<br>";
    }
    
    // 5. Probar inserción de ticket
    echo "<h2>🧪 Probando inserción de ticket</h2>";
    try {
        $stmt = $pdo->prepare("INSERT INTO support_tickets (user_id, subject, description, priority, category, status) VALUES (?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([1, 'Ticket de prueba', 'Descripción de prueba', 'medium', 'technical', 'open']);
        if ($result) {
            echo "✅ Ticket de prueba insertado correctamente<br>";
            $ticket_id = $pdo->lastInsertId();
            echo "ID del ticket: $ticket_id<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error insertando ticket de prueba: " . $e->getMessage() . "<br>";
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; margin-top: 2rem;'>";
    echo "<h2>🎉 ¡Corrección Completada!</h2>";
    echo "<p>Todas las tablas han sido verificadas y corregidas.</p>";
    echo "<br><strong>Ahora puedes probar:</strong><br>";
    echo "• <a href='user_tickets.php'>🎫 Crear Tickets</a><br>";
    echo "• <a href='user_chat_simple.php'>💬 Chat en Vivo</a><br>";
    echo "• <a href='user_apps.php'>📱 Ver Aplicaciones</a><br>";
    echo "• <a href='user_help.php'>❓ Centro de Ayuda</a><br>";
    echo "• <a href='user_channels.php'>📺 Solicitar Canales</a><br>";
    echo "• <a href='insert_test_data.php'>📊 Insertar más datos de prueba</a><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
