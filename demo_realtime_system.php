<?php
session_start();
require_once 'config.php';
require_once 'notification_triggers.php';

// Verificar que sea un administrador
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$message = '';

// Procesar acciones de demo
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'simulate_chat':
            $result = notifyNewChatMessage(1, "Hola, necesito ayuda urgente con mi lista M3U", 123);
            $message = $result ? "✅ Chat simulado - Notificación enviada" : "❌ Error al simular chat";
            break;
            
        case 'simulate_ticket':
            $result = notifyNewSupportTicket(456, 2, "No puedo activar mi cuenta", 'high');
            $message = $result ? "✅ Ticket simulado - Notificación enviada" : "❌ Error al simular ticket";
            break;
            
        case 'simulate_channel':
            $result = notifyNewChannelRequest(789, 3, "Discovery Channel HD", "https://example.com/discovery");
            $message = $result ? "✅ Canal simulado - Notificación enviada" : "❌ Error al simular canal";
            break;
            
        case 'simulate_download':
            $result = notifyAppDownload("RGS Player Pro", 4, "192.168.1.100");
            $message = $result ? "✅ Descarga simulada - Notificación enviada" : "❌ Error al simular descarga";
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Demo Sistema Tiempo Real - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --border-color: #334155;
            --border-radius: 12px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .demo-section {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .demo-buttons {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary { background: var(--primary-color); color: white; }
        .btn-success { background: var(--success-color); color: white; }
        .btn-warning { background: var(--warning-color); color: white; }
        .btn-danger { background: var(--error-color); color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .iframe-container {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin: 2rem 0;
            border: 1px solid var(--border-color);
        }

        .iframe-container iframe {
            width: 100%;
            height: 400px;
            border: none;
            border-radius: 8px;
            background: white;
        }

        .nav-buttons {
            text-align: center;
            margin-bottom: 2rem;
        }

        .nav-buttons a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-buttons a:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .stats-display {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin: 1rem 0;
            border: 1px solid var(--border-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--secondary-color);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .instructions {
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .instructions h3 {
            color: var(--primary-color);
            margin-top: 0;
        }

        .instructions ol {
            color: var(--text-secondary);
        }

        .instructions li {
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Demo Sistema de Notificaciones en Tiempo Real</h1>

        <div class="nav-buttons">
            <a href="admin.php" target="_blank">🏠 Admin Principal</a>
            <a href="admin2_fast.php" target="_blank">🎧 Admin Soporte (Rápido)</a>
            <a href="test_notifications_system.php">🧪 Test Completo</a>
        </div>

        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>

        <div class="instructions">
            <h3>📋 Instrucciones para la Demo</h3>
            <ol>
                <li><strong>Abre admin.php en una pestaña</strong> - Verás las cartas de soporte con números en tiempo real</li>
                <li><strong>Abre admin2.php en otra pestaña</strong> - Panel de soporte optimizado</li>
                <li><strong>Usa los botones de abajo</strong> para simular eventos</li>
                <li><strong>Observa las cartas en admin.php</strong> - Los números cambiarán automáticamente</li>
                <li><strong>Las cartas se animarán</strong> cuando lleguen nuevas notificaciones</li>
            </ol>
        </div>

        <div class="demo-grid">
            <div class="demo-section">
                <h3 style="color: var(--primary-color); margin-bottom: 1rem;">🎮 Simulador de Eventos</h3>
                
                <form method="POST" style="display: contents;">
                    <div class="demo-buttons">
                        <button type="submit" name="action" value="simulate_chat" class="btn btn-primary">
                            <i class="fas fa-comments"></i>
                            Simular Nuevo Chat
                        </button>
                        
                        <button type="submit" name="action" value="simulate_ticket" class="btn btn-warning">
                            <i class="fas fa-ticket-alt"></i>
                            Simular Nuevo Ticket
                        </button>
                        
                        <button type="submit" name="action" value="simulate_channel" class="btn btn-success">
                            <i class="fas fa-tv"></i>
                            Simular Solicitud Canal
                        </button>
                        
                        <button type="submit" name="action" value="simulate_download" class="btn btn-danger">
                            <i class="fas fa-download"></i>
                            Simular Descarga App
                        </button>
                    </div>
                </form>
            </div>

            <div class="demo-section">
                <h3 style="color: var(--success-color); margin-bottom: 1rem;">📊 Estadísticas en Tiempo Real</h3>
                
                <div class="stats-display">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" id="demo-chats">0</div>
                            <div class="stat-label">Chats Activos</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="demo-tickets">0</div>
                            <div class="stat-label">Tickets Abiertos</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="demo-channels">0</div>
                            <div class="stat-label">Canales Pendientes</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="demo-downloads">0</div>
                            <div class="stat-label">Descargas Hoy</div>
                        </div>
                    </div>
                </div>
                
                <button onclick="loadDemoStats()" class="btn btn-primary" style="width: 100%; margin-top: 1rem;">
                    <i class="fas fa-sync-alt"></i>
                    Actualizar Estadísticas
                </button>
            </div>
        </div>

        <div class="iframe-container">
            <h3 style="color: var(--primary-color); margin-bottom: 1rem;">🖥️ Vista Previa Admin Principal</h3>
            <iframe src="admin.php" title="Admin Principal"></iframe>
        </div>

        <div class="iframe-container">
            <h3 style="color: var(--warning-color); margin-bottom: 1rem;">🎧 Vista Previa Admin Soporte (Optimizado)</h3>
            <iframe src="admin2_fast.php" title="Admin Soporte Rápido"></iframe>
        </div>

        <!-- Información del Sistema -->
        <div style="margin-top: 2rem; padding: 1rem; background: var(--dark-bg); border-radius: 8px; font-family: monospace; font-size: 0.9rem;">
            <strong>🔧 Información Técnica:</strong><br>
            Sistema: Notificaciones en tiempo real sin Firebase<br>
            Tecnología: Server-Sent Events (SSE) + JavaScript<br>
            Velocidad: Actualizaciones cada 2-15 segundos<br>
            Compatibilidad: Todos los navegadores modernos<br>
            Estado: <span style="color: var(--success-color);">✅ Funcionando</span>
        </div>
    </div>

    <!-- Sistema de Notificaciones en Tiempo Real -->
    <script src="realtime_notifications.js"></script>
    <script>
        // Función para cargar estadísticas de demo
        async function loadDemoStats() {
            try {
                const response = await fetch('api_support_stats.php');
                const data = await response.json();
                
                if (data.success && data.stats) {
                    document.getElementById('demo-chats').textContent = data.stats.chat_active || 0;
                    document.getElementById('demo-tickets').textContent = data.stats.tickets_open || 0;
                    document.getElementById('demo-channels').textContent = data.stats.channels_pending || 0;
                    document.getElementById('demo-downloads').textContent = data.stats.downloads_today || 0;
                }
            } catch (error) {
                console.error('Error loading demo stats:', error);
            }
        }
        
        // Configuración específica para la demo
        document.addEventListener('DOMContentLoaded', function() {
            // Cargar estadísticas iniciales
            loadDemoStats();
            
            // Actualizar cada 10 segundos
            setInterval(loadDemoStats, 10000);
            
            // Configurar notificaciones en tiempo real
            if (window.realtimeNotifications) {
                window.realtimeNotifications.onNotificationCallback = function(notification) {
                    console.log('🎮 Demo - Nueva notificación:', notification);
                    
                    // Actualizar estadísticas inmediatamente
                    setTimeout(loadDemoStats, 1000);
                    
                    // Mostrar mensaje en la página
                    const message = document.createElement('div');
                    message.className = 'message';
                    message.innerHTML = `🔔 ${notification.title} - ${notification.message.substring(0, 50)}...`;
                    document.querySelector('.container').insertBefore(message, document.querySelector('.demo-grid'));
                    
                    // Remover mensaje después de 5 segundos
                    setTimeout(() => message.remove(), 5000);
                };
            }
        });
        
        // Recargar iframes cada 30 segundos para mostrar cambios
        setInterval(() => {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(iframe => {
                const src = iframe.src;
                iframe.src = '';
                setTimeout(() => iframe.src = src, 100);
            });
        }, 30000);
    </script>
</body>
</html>
