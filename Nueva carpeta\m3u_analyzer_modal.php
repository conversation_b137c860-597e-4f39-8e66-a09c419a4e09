<?php
// Analizador M3U con modal de progreso
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener listas
$stmt = $pdo->query("SELECT id, name, is_active, last_scan, total_items FROM m3u_lists ORDER BY name");
$all_lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Analizador M3U - RGS TOOL</title>
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --error-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .back-link {
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            border: 1px solid var(--border-color);
        }

        .form-group {
            margin: 1.5rem 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--primary-color);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
            transform: translateY(-2px);
        }

        .btn-primary:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--secondary-color);
            margin: 10% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            border: 1px solid var(--border-color);
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .modal-header h2 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .progress-container {
            margin: 2rem 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--primary-color);
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-color), #28a745);
            width: 0%;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 1rem;
            color: var(--text-secondary);
        }

        .progress-percentage {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .log-container {
            background: var(--primary-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            border: 1px solid var(--border-color);
        }

        .log-entry {
            margin: 0.2rem 0;
            color: var(--text-secondary);
        }

        .log-entry.success {
            color: var(--success-color);
        }

        .log-entry.error {
            color: var(--error-color);
        }

        .log-entry.info {
            color: var(--accent-color);
        }

        .results-container {
            display: none;
            margin-top: 2rem;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .result-item {
            text-align: center;
            background: var(--primary-color);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .result-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .result-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: var(--primary-color);
            color: var(--text-primary);
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .result-item:hover {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="m3u_manager.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Gestor M3U
        </a>
        
        <div class="header">
            <h1><i class="fas fa-search"></i> Analizador M3U</h1>
            <p>Analiza y procesa listas M3U con progreso en tiempo real</p>
        </div>

        <div class="section">
            <h2><i class="fas fa-cog"></i> Análisis de Lista M3U</h2>
            <form id="analyzeForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista M3U</label>
                    <select name="list_id" id="list_id" class="form-input" required>
                        <option value="">Seleccionar lista...</option>
                        <?php foreach ($all_lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?>
                            (<?php echo $list['total_items']; ?> elementos)
                            <?php if (!$list['is_active']): ?> - Inactiva<?php endif; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary" id="analyzeBtn">
                    <i class="fas fa-search"></i>
                    Iniciar Análisis
                </button>
            </form>
        </div>
    </div>

    <!-- Modal de Progreso -->
    <div id="progressModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">&times;</button>
            
            <div class="modal-header">
                <h2><i class="fas fa-cog"></i> Analizando Lista M3U</h2>
                <p>Procesando contenido, por favor espera...</p>
            </div>

            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text">
                    <div class="progress-percentage" id="progressPercentage">0%</div>
                    <div id="progressStatus">Iniciando análisis...</div>
                </div>
            </div>

            <div class="log-container" id="logContainer">
                <div class="log-entry info">Preparando análisis...</div>
            </div>

            <div class="results-container" id="resultsContainer">
                <h3><i class="fas fa-chart-bar"></i> Resultados del Análisis</h3>
                <div class="results-grid" id="resultsGrid">
                    <!-- Los resultados se llenarán dinámicamente -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        let currentProgress = 0;
        let analysisComplete = false;

        document.getElementById('analyzeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startAnalysis();
        });

        function startAnalysis() {
            const listId = document.getElementById('list_id').value;
            if (!listId) {
                alert('Por favor selecciona una lista M3U');
                return;
            }

            // Mostrar modal
            document.getElementById('progressModal').style.display = 'block';
            
            // Resetear estado
            currentProgress = 0;
            analysisComplete = false;
            updateProgress(0, 'Iniciando análisis...');
            clearLog();
            hideResults();

            // Iniciar análisis
            performAnalysis(listId);
        }

        function performAnalysis(listId) {
            addLog('Conectando al servidor...', 'info');
            updateProgress(10, 'Conectando...');

            fetch('m3u_analyzer_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=analyze&list_id=${listId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    simulateProgress(data);
                } else {
                    addLog('Error: ' + data.message, 'error');
                    updateProgress(100, 'Error en el análisis');
                }
            })
            .catch(error => {
                addLog('Error de conexión: ' + error.message, 'error');
                updateProgress(100, 'Error de conexión');
            });
        }

        function simulateProgress(data) {
            const totalItems = data.total_items || 0;
            const listName = data.list_name || 'Lista M3U';

            const steps = [
                { progress: 15, message: 'Conectando al servidor...', log: `Iniciando análisis de "${listName}"` },
                { progress: 25, message: 'Descargando lista M3U...', log: `Descargando desde URL remota` },
                { progress: 35, message: 'Validando formato M3U...', log: 'Archivo M3U válido detectado' },
                { progress: 50, message: 'Analizando contenido...', log: `Procesando ${data.total_lines || 'múltiples'} líneas del archivo` },
                { progress: 65, message: 'Clasificando elementos...', log: `Identificando películas y series` },
                { progress: 80, message: 'Limpiando datos anteriores...', log: 'Preparando base de datos' },
                { progress: 90, message: 'Guardando nuevo contenido...', log: `Insertando ${totalItems} elementos` },
                { progress: 95, message: 'Actualizando estadísticas...', log: 'Finalizando proceso' },
                { progress: 100, message: '¡Análisis completado!', log: `✅ Proceso completado: ${totalItems} elementos procesados` }
            ];

            let currentStep = 0;

            function nextStep() {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    updateProgress(step.progress, step.message);
                    addLog(step.log, currentStep === steps.length - 1 ? 'success' : 'info');

                    currentStep++;

                    if (currentStep < steps.length) {
                        // Tiempo variable según el paso
                        const delay = currentStep <= 3 ? 800 : (currentStep <= 6 ? 1200 : 600);
                        setTimeout(nextStep, delay + Math.random() * 500);
                    } else {
                        setTimeout(() => showResults(data), 500);
                    }
                }
            }

            nextStep();
        }

        function updateProgress(percentage, status) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressPercentage').textContent = percentage + '%';
            document.getElementById('progressStatus').textContent = status;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<div class="log-entry info">Preparando análisis...</div>';
        }

        function showResults(data) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsGrid = document.getElementById('resultsGrid');

            // Agregar efecto de éxito al progreso
            document.getElementById('progressFill').style.background = 'linear-gradient(90deg, #28a745, #20c997)';

            resultsGrid.innerHTML = `
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.1s both;">
                    <div class="result-number">${data.total_items || 0}</div>
                    <div class="result-label">Total Elementos</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.2s both;">
                    <div class="result-number">${data.movies || 0}</div>
                    <div class="result-label">🎬 Películas</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.3s both;">
                    <div class="result-number">${data.tv_shows || 0}</div>
                    <div class="result-label">📺 Series</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.4s both;">
                    <div class="result-number">${data.unknown || 0}</div>
                    <div class="result-label">❓ Sin clasificar</div>
                </div>
                <div class="result-item" style="animation: slideInUp 0.5s ease 0.5s both;">
                    <div class="result-number">${data.with_year || 0}</div>
                    <div class="result-label">📅 Con año</div>
                </div>
            `;

            // Mostrar resultados con animación
            setTimeout(() => {
                resultsContainer.style.display = 'block';
                resultsContainer.style.animation = 'fadeIn 0.5s ease';
            }, 200);

            // Agregar botón de cerrar
            setTimeout(() => {
                const closeButton = document.createElement('button');
                closeButton.className = 'btn btn-primary';
                closeButton.style.cssText = 'margin-top: 1rem; width: 100%;';
                closeButton.innerHTML = '<i class="fas fa-check"></i> Finalizar';
                closeButton.onclick = closeModal;
                resultsContainer.appendChild(closeButton);
            }, 1000);

            analysisComplete = true;
        }

        function hideResults() {
            document.getElementById('resultsContainer').style.display = 'none';
        }

        function closeModal() {
            if (analysisComplete || confirm('¿Estás seguro de que quieres cancelar el análisis?')) {
                document.getElementById('progressModal').style.display = 'none';
            }
        }

        // Cerrar modal al hacer clic fuera
        window.onclick = function(event) {
            const modal = document.getElementById('progressModal');
            if (event.target === modal && analysisComplete) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
