<?php
session_start();
require_once 'config.php';

// Obtener ID del artículo
$article_id = (int)($_GET['id'] ?? 0);

if (!$article_id) {
    header('Location: user_help.php');
    exit;
}

// Obtener artículo directamente de la base de datos
$article = null;
try {
    $stmt = $pdo->prepare("
        SELECT * FROM help_articles 
        WHERE id = ? AND status = 'published'
    ");
    $stmt->execute([$article_id]);
    $article = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($article) {
        // Incrementar contador de vistas
        $stmt = $pdo->prepare("UPDATE help_articles SET view_count = view_count + 1 WHERE id = ?");
        $stmt->execute([$article_id]);
    }
} catch (Exception $e) {
    // Error de base de datos
    $error = $e->getMessage();
}

if (!$article) {
    header('Location: user_help.php?error=article_not_found');
    exit;
}

// Obtener artículos relacionados
$related_articles = [];
try {
    $stmt = $pdo->prepare("
        SELECT id, title, excerpt, view_count
        FROM help_articles 
        WHERE category = ? AND id != ? AND status = 'published'
        ORDER BY view_count DESC, created_at DESC
        LIMIT 3
    ");
    $stmt->execute([$article['category'], $article_id]);
    $related_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Sin artículos relacionados
}

// Procesar feedback si se envía
$feedback_sent = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_feedback'])) {
    $rating = (int)($_POST['rating'] ?? 0);
    $feedback_text = htmlspecialchars(strip_tags(trim($_POST['feedback'] ?? '')));
    
    if ($rating >= 1 && $rating <= 5) {
        try {
            // Crear tabla de feedback si no existe
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS help_feedback (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    article_id INT NOT NULL,
                    rating INT NOT NULL,
                    feedback TEXT,
                    user_id INT NULL,
                    ip_address VARCHAR(45),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            $user_id = $_SESSION['user_id'] ?? null;
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
            
            $stmt = $pdo->prepare("INSERT INTO help_feedback (article_id, rating, feedback, user_id, ip_address) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$article_id, $rating, $feedback_text, $user_id, $ip_address]);
            
            $feedback_sent = true;
        } catch (Exception $e) {
            // Error enviando feedback
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['title']); ?> - Centro de Ayuda RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        .back-button {
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .back-button:hover {
            background: #1d4ed8;
        }
        .article-title {
            font-size: 2rem;
            color: #f8fafc;
            margin-bottom: 1rem;
        }
        .article-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        .badge {
            background: rgba(37, 99, 235, 0.2);
            color: #2563eb;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        .content {
            background: #0f172a;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .content-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #cbd5e1;
            white-space: pre-wrap;
        }
        .feedback-section {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .stars {
            display: flex;
            gap: 0.2rem;
            margin: 1rem 0;
        }
        .star {
            font-size: 1.5rem;
            color: #6b7280;
            cursor: pointer;
            transition: color 0.2s;
        }
        .star.active, .star:hover {
            color: #f59e0b;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-label {
            display: block;
            color: #f8fafc;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            background: #1e293b;
            color: #f8fafc;
            border: 1px solid #334155;
            border-radius: 8px;
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }
        .btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .success-message {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            color: #10b981;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
        }
        .related-articles {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
        }
        .related-item {
            padding: 1rem;
            border-bottom: 1px solid #334155;
        }
        .related-item:last-child {
            border-bottom: none;
        }
        .related-link {
            color: #f8fafc;
            text-decoration: none;
            font-weight: 500;
            display: block;
            margin-bottom: 0.5rem;
        }
        .related-link:hover {
            color: #2563eb;
        }
        .related-excerpt {
            color: #cbd5e1;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="user_help.php" class="back-button">
            <i class="fas fa-arrow-left"></i> Volver al Centro de Ayuda
        </a>
        
        <div class="article-meta">
            <span class="badge"><?php echo ucfirst($article['category']); ?></span>
            <?php if (!empty($article['subcategory'])): ?>
            <span class="badge"><?php echo ucfirst($article['subcategory']); ?></span>
            <?php endif; ?>
            <span style="color: #cbd5e1;">
                <i class="fas fa-eye"></i> <?php echo number_format($article['view_count']); ?> vistas
            </span>
            <span style="color: #cbd5e1;">
                <i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($article['created_at'])); ?>
            </span>
        </div>
        
        <h1 class="article-title"><?php echo htmlspecialchars($article['title']); ?></h1>
        
        <div class="content">
            <div class="content-text">
                <?php echo nl2br(htmlspecialchars($article['content'])); ?>
            </div>
        </div>

        <!-- Sección de Feedback -->
        <div class="feedback-section">
            <h3 style="color: #f8fafc; margin-bottom: 1rem;">¿Te fue útil este artículo?</h3>
            
            <?php if ($feedback_sent): ?>
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                ¡Gracias por tu feedback! Nos ayuda a mejorar.
            </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label class="form-label">Tu calificación:</label>
                    <div class="stars" id="rating-stars">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <span class="star" data-rating="<?php echo $i; ?>">★</span>
                        <?php endfor; ?>
                    </div>
                    <input type="hidden" name="rating" id="rating-input" required>
                </div>
                
                <div class="form-group">
                    <label for="feedback" class="form-label">Comentarios (opcional):</label>
                    <textarea name="feedback" id="feedback" class="form-textarea" 
                              placeholder="Comparte tu experiencia o sugerencias..."></textarea>
                </div>
                
                <button type="submit" name="submit_feedback" class="btn">
                    <i class="fas fa-paper-plane"></i> Enviar Feedback
                </button>
            </form>
        </div>

        <!-- Artículos Relacionados -->
        <?php if (!empty($related_articles)): ?>
        <div class="related-articles">
            <h3 style="color: #f8fafc; margin-bottom: 1rem;">Artículos Relacionados</h3>
            <?php foreach ($related_articles as $related): ?>
            <div class="related-item">
                <a href="user_help_article.php?id=<?php echo $related['id']; ?>" class="related-link">
                    <?php echo htmlspecialchars($related['title']); ?>
                </a>
                <?php if (!empty($related['excerpt'])): ?>
                <p class="related-excerpt"><?php echo htmlspecialchars($related['excerpt']); ?></p>
                <?php endif; ?>
                <div style="color: #cbd5e1; font-size: 0.9rem;">
                    <i class="fas fa-eye"></i> <?php echo number_format($related['view_count']); ?> vistas
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // Manejo de calificación con estrellas
        const stars = document.querySelectorAll('#rating-stars .star');
        const ratingInput = document.getElementById('rating-input');
        
        stars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                ratingInput.value = rating;
                
                stars.forEach((s, index) => {
                    s.classList.toggle('active', index < rating);
                });
            });
            
            star.addEventListener('mouseover', function() {
                const rating = parseInt(this.dataset.rating);
                stars.forEach((s, index) => {
                    s.style.color = index < rating ? '#f59e0b' : '#6b7280';
                });
            });
        });
        
        document.getElementById('rating-stars').addEventListener('mouseleave', function() {
            const currentRating = parseInt(ratingInput.value) || 0;
            stars.forEach((s, index) => {
                s.style.color = index < currentRating ? '#f59e0b' : '#6b7280';
            });
        });
    </script>
</body>
</html>
