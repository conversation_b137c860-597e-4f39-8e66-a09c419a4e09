<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_app':
                $name = $_POST['name'];
                $version = $_POST['version'];
                $platform = $_POST['platform'];
                $description = $_POST['description'];
                $features = $_POST['features'];
                $download_url = $_POST['download_url'];
                $external_url = $_POST['external_url'];
                $status = $_POST['status'];
                
                $stmt = $pdo->prepare("INSERT INTO support_apps (name, version, platform, description, features, download_url, external_url, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$name, $version, $platform, $description, $features, $download_url, $external_url, $status])) {
                    $message = "Aplicación agregada correctamente";
                    $message_type = "success";
                } else {
                    $message = "Error al agregar la aplicación";
                    $message_type = "error";
                }
                break;
                
            case 'update_app':
                $app_id = (int)$_POST['app_id'];
                $name = $_POST['name'];
                $version = $_POST['version'];
                $platform = $_POST['platform'];
                $description = $_POST['description'];
                $features = $_POST['features'];
                $download_url = $_POST['download_url'];
                $external_url = $_POST['external_url'];
                $status = $_POST['status'];
                
                $stmt = $pdo->prepare("UPDATE support_apps SET name = ?, version = ?, platform = ?, description = ?, features = ?, download_url = ?, external_url = ?, status = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$name, $version, $platform, $description, $features, $download_url, $external_url, $status, $app_id])) {
                    $message = "Aplicación actualizada correctamente";
                    $message_type = "success";
                } else {
                    $message = "Error al actualizar la aplicación";
                    $message_type = "error";
                }
                break;
                
            case 'delete_app':
                $app_id = (int)$_POST['app_id'];
                
                $stmt = $pdo->prepare("DELETE FROM support_apps WHERE id = ?");
                if ($stmt->execute([$app_id])) {
                    $message = "Aplicación eliminada correctamente";
                    $message_type = "success";
                } else {
                    $message = "Error al eliminar la aplicación";
                    $message_type = "error";
                }
                break;
        }
    }
}

// Obtener filtros
$platform_filter = $_GET['platform'] ?? 'all';
$status_filter = $_GET['status'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($platform_filter !== 'all') {
    $where_conditions[] = "platform = ?";
    $params[] = $platform_filter;
}

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener aplicaciones
$stmt = $pdo->prepare("SELECT * FROM support_apps $where_clause ORDER BY created_at DESC");
$stmt->execute($params);
$apps = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE status = 'published'");
$published_count = $stmt->fetchColumn() ?: 0;

$stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE status = 'draft'");
$draft_count = $stmt->fetchColumn() ?: 0;

$stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE status = 'archived'");
$archived_count = $stmt->fetchColumn() ?: 0;

$stmt = $pdo->query("SELECT COUNT(DISTINCT platform) FROM support_apps WHERE status = 'published'");
$platforms_count = $stmt->fetchColumn() ?: 0;
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Gestión de Aplicaciones Profesional - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #f59e0b;
            --primary-light: #fbbf24;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --light-bg: #f8fafc;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #f8fafc;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            --gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            --gradient-error: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .header-nav {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--light-bg);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        .nav-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .nav-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Main Content */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .stat-card.published::before {
            background: var(--gradient-success);
        }

        .stat-card.draft::before {
            background: var(--gradient-primary);
        }

        .stat-card.archived::before {
            background: var(--gradient-error);
        }

        .stat-card.platforms::before {
            background: var(--gradient-info);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .stat-icon.published {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-icon.draft {
            background: rgba(245, 158, 11, 0.1);
            color: var(--primary-color);
        }

        .stat-icon.archived {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .stat-icon.platforms {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        /* Filters and Actions */
        .controls-section {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .filters-group {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: white;
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--light-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        /* Apps Grid */
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .app-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
        }

        .app-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .app-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
        }

        .app-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }

        .app-icon {
            width: 50px;
            height: 50px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .app-icon.android {
            background: linear-gradient(135deg, #3ddc84 0%, #2e7d32 100%);
        }

        .app-icon.ios {
            background: linear-gradient(135deg, #007aff 0%, #0056b3 100%);
        }

        .app-icon.windows {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
        }

        .app-icon.macos {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
        }

        .app-icon.smart_tv {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .app-icon.web {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .app-icon.other {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }

        .app-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .app-version {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .app-platform {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background: rgba(245, 158, 11, 0.1);
            color: var(--primary-color);
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 0.5rem;
        }

        .app-content {
            padding: 1.5rem;
        }

        .app-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .app-features {
            margin-bottom: 1.5rem;
        }

        .features-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .features-list {
            color: var(--text-secondary);
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .app-status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 1rem;
        }

        .app-status.published {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .app-status.draft {
            background: rgba(245, 158, 11, 0.1);
            color: var(--primary-color);
        }

        .app-status.archived {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .app-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .action-btn.edit {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info-color);
        }

        .action-btn.delete {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .action-btn.download {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .action-btn:hover {
            transform: translateY(-1px);
            opacity: 0.8;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .modal-close:hover {
            background: var(--light-bg);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        /* Message */
        .message {
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .message.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .apps-grid {
                grid-template-columns: 1fr;
            }

            .controls-section {
                flex-direction: column;
                align-items: stretch;
            }

            .filters-group {
                justify-content: center;
            }
        }

        /* Loading Animation */
        .loading {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
