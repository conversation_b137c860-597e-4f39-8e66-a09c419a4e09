<?php
// API para estadísticas del panel de administración profesional
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    $stats = [];
    
    // Estadísticas de tickets
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
    $stats['tickets_open'] = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status IN ('open', 'in_progress')");
    $stats['tickets_pending'] = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de chat
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
    $stats['chat_active'] = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'waiting'");
    $stats['chat_waiting'] = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de aplicaciones
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE status = 'published'");
    $stats['apps_published'] = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de ayuda
    $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles WHERE status = 'published'");
    $stats['help_articles'] = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de canales
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
    $stats['channels_pending'] = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de activaciones
    $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE status = 'active'");
    $stats['codes_active'] = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas adicionales
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE DATE(created_at) = CURDATE()");
    $stats['tickets_today'] = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE DATE(started_at) = CURDATE()");
    $stats['chat_today'] = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE DATE(created_at) = CURDATE()");
    $stats['channels_today'] = $stmt->fetchColumn() ?: 0;
    
    // Actividad reciente
    $stmt = $pdo->query("
        SELECT 'ticket' as type, subject as title, created_at, status 
        FROM support_tickets 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        UNION ALL
        SELECT 'chat' as type, CONCAT('Chat con usuario ', user_id) as title, started_at as created_at, status 
        FROM chat_sessions 
        WHERE started_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        UNION ALL
        SELECT 'channel' as type, channel_name as title, created_at, status 
        FROM channel_requests 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stats['recent_activities'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Estadísticas por hora (últimas 24 horas)
    $stmt = $pdo->query("
        SELECT 
            HOUR(created_at) as hour,
            COUNT(*) as count
        FROM support_tickets 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ");
    $stats['tickets_by_hour'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Top categorías de tickets
    $stmt = $pdo->query("
        SELECT 
            category,
            COUNT(*) as count
        FROM support_tickets 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAYS)
        GROUP BY category
        ORDER BY count DESC
        LIMIT 5
    ");
    $stats['top_ticket_categories'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Tiempo promedio de respuesta (en minutos)
    $stmt = $pdo->query("
        SELECT 
            AVG(TIMESTAMPDIFF(MINUTE, st.created_at, tr.created_at)) as avg_response_time
        FROM support_tickets st
        JOIN ticket_responses tr ON st.id = tr.ticket_id
        WHERE tr.is_admin = 1 
        AND st.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAYS)
        AND tr.created_at = (
            SELECT MIN(created_at) 
            FROM ticket_responses 
            WHERE ticket_id = st.id AND is_admin = 1
        )
    ");
    $avg_response = $stmt->fetchColumn();
    $stats['avg_response_time_minutes'] = $avg_response ? round($avg_response, 1) : 0;
    
    // Satisfacción del cliente (si existe sistema de rating)
    $stmt = $pdo->query("
        SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings
        FROM chat_sessions 
        WHERE rating IS NOT NULL 
        AND ended_at >= DATE_SUB(NOW(), INTERVAL 30 DAYS)
    ");
    $rating_data = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['avg_rating'] = $rating_data['avg_rating'] ? round($rating_data['avg_rating'], 1) : 0;
    $stats['total_ratings'] = $rating_data['total_ratings'] ?: 0;
    
    // Estado del sistema
    $stats['system_status'] = [
        'database' => 'online',
        'last_update' => date('Y-m-d H:i:s'),
        'uptime' => '99.9%'
    ];
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
