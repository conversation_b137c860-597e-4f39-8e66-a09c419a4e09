<?php
// Configuración rápida para solucionar errores 500
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    // Conectar directamente a la base de datos (hosting compartido)
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Crear tabla users si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100),
            password_hash VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Crear tabla support_tickets si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS support_tickets (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            subject VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
            category VARCHAR(50) NOT NULL,
            status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
            assigned_to INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Crear tabla ticket_responses si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS ticket_responses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            ticket_id INT NOT NULL,
            user_id INT,
            message TEXT NOT NULL,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE
        )
    ");
    
    // Crear tabla chat_sessions si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_sessions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            status ENUM('waiting', 'active', 'ended') DEFAULT 'waiting',
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ended_at TIMESTAMP NULL,
            rating INT NULL
        )
    ");
    
    // Crear tabla chat_messages si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            session_id INT NOT NULL,
            sender_id INT NOT NULL,
            message TEXT NOT NULL,
            is_admin BOOLEAN DEFAULT FALSE,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
        )
    ");
    
    // Crear tabla support_apps si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS support_apps (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            version VARCHAR(50) NOT NULL,
            platform ENUM('android', 'ios', 'windows', 'macos', 'smart_tv', 'firestick', 'web', 'other') NOT NULL,
            description TEXT,
            features TEXT,
            file_path VARCHAR(500),
            file_size BIGINT,
            download_url VARCHAR(500),
            external_url VARCHAR(500),
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Crear tabla help_articles si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS help_articles (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            category ENUM('setup', 'troubleshooting', 'apps', 'channels', 'billing', 'account', 'general') NOT NULL,
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            is_featured BOOLEAN DEFAULT FALSE,
            views INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Crear tabla channel_requests si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS channel_requests (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            channel_name VARCHAR(255) NOT NULL,
            channel_url VARCHAR(500),
            country VARCHAR(10) NOT NULL,
            language VARCHAR(50) NOT NULL,
            category VARCHAR(50) NOT NULL,
            description TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            admin_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Crear tabla activation_codes si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activation_codes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) UNIQUE NOT NULL,
            list_name VARCHAR(255) NOT NULL,
            description TEXT,
            status ENUM('active', 'used', 'expired', 'disabled') DEFAULT 'active',
            expires_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Crear tabla user_activations si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_activations (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            activation_code_id INT NOT NULL,
            activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (activation_code_id) REFERENCES activation_codes(id) ON DELETE CASCADE
        )
    ");
    
    // Crear tabla activity_logs si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            details JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // Insertar usuario demo si no existe
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'usuario'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("INSERT INTO users (username, email, password_hash) VALUES ('usuario', '<EMAIL>', 'demo_hash')");
    }
    
    // Insertar admin demo si no existe
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'admin'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        $pdo->exec("INSERT INTO users (username, email, password_hash) VALUES ('admin', '<EMAIL>', 'admin_hash')");
    }
    
    // Insertar algunos datos de ejemplo
    $pdo->exec("
        INSERT IGNORE INTO support_apps (name, version, platform, description, status) VALUES
        ('RGS IPTV Android', '2.1.0', 'android', 'Aplicación oficial para dispositivos Android', 'published'),
        ('RGS IPTV iOS', '2.0.5', 'ios', 'Aplicación oficial para iPhone y iPad', 'published'),
        ('RGS Player Windows', '1.8.2', 'windows', 'Reproductor para Windows 10/11', 'published')
    ");
    
    $pdo->exec("
        INSERT IGNORE INTO help_articles (title, content, category, status, is_featured) VALUES
        ('Cómo configurar tu lista M3U', 'Guía paso a paso para configurar tu lista M3U en diferentes dispositivos...', 'setup', 'published', 1),
        ('Solución a problemas de buffering', 'Si experimentas cortes o buffering, sigue estos pasos...', 'troubleshooting', 'published', 1),
        ('Instalación en Smart TV', 'Instrucciones para instalar la aplicación en tu Smart TV...', 'apps', 'published', 0)
    ");
    
    $pdo->exec("
        INSERT IGNORE INTO activation_codes (code, list_name, description, status) VALUES
        ('DEMO2024', 'Lista Premium Demo', 'Código de demostración para acceso premium', 'active'),
        ('TEST123', 'Lista de Prueba', 'Lista de prueba con canales básicos', 'active')
    ");
    
    echo "✅ Base de datos configurada correctamente!<br>";
    echo "✅ Tablas creadas<br>";
    echo "✅ Datos de ejemplo insertados<br>";
    echo "<br><strong>Credenciales de demo:</strong><br>";
    echo "Usuario: <code>usuario</code> / Contraseña: <code>demo123</code><br>";
    echo "Admin: <code>admin</code> / Contraseña: <code>admin123</code><br>";
    echo "<br><a href='index2.php'>🚀 Ir a Servicios de Soporte</a><br>";
    echo "<a href='admin2.php'>🛠️ Ir al Panel de Administración</a>";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "<br><strong>Verifica:</strong><br>";
    echo "1. Que MySQL esté ejecutándose<br>";
    echo "2. Las credenciales de conexión en este archivo<br>";
    echo "3. Que el usuario tenga permisos para crear bases de datos<br>";
}
?>
