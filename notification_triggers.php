<?php
/**
 * Sistema de Triggers para Notificaciones Automáticas
 * Se ejecuta cuando ocurren eventos importantes en el sistema
 */

require_once 'config.php';

class NotificationTriggers {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Crear notificación cuando llega un nuevo mensaje de chat
     */
    public function onNewChatMessage($user_id, $message, $chat_session_id = null) {
        try {
            // Obtener información del usuario
            $stmt = $this->pdo->prepare("SELECT username FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $username = $user ? $user['username'] : "Usuario #$user_id";
            $message_preview = substr($message, 0, 100);
            
            $title = "💬 Nuevo mensaje de chat";
            $notification_message = "Usuario: $username\nMensaje: $message_preview" . (strlen($message) > 100 ? "..." : "");
            
            return $this->createNotification(
                'chat_message',
                $title,
                $notification_message,
                $chat_session_id,
                'chat',
                'high'
            );
            
        } catch (Exception $e) {
            error_log("Error creating chat notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Crear notificación cuando se crea un nuevo ticket
     */
    public function onNewSupportTicket($ticket_id, $user_id, $subject, $priority = 'normal') {
        try {
            // Obtener información del usuario
            $stmt = $this->pdo->prepare("SELECT username FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $username = $user ? $user['username'] : "Usuario #$user_id";
            
            $title = "🎫 Nuevo ticket de soporte";
            $notification_message = "Ticket #$ticket_id\nUsuario: $username\nAsunto: $subject";
            
            return $this->createNotification(
                'support_ticket',
                $title,
                $notification_message,
                $ticket_id,
                'ticket',
                $priority
            );
            
        } catch (Exception $e) {
            error_log("Error creating ticket notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Crear notificación cuando se solicita un nuevo canal
     */
    public function onNewChannelRequest($request_id, $user_id, $channel_name, $channel_url = null) {
        try {
            // Obtener información del usuario
            $stmt = $this->pdo->prepare("SELECT username FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $username = $user ? $user['username'] : "Usuario #$user_id";
            
            $title = "📺 Nueva solicitud de canal";
            $notification_message = "Solicitud #$request_id\nUsuario: $username\nCanal: $channel_name";
            if ($channel_url) {
                $notification_message .= "\nURL: $channel_url";
            }
            
            return $this->createNotification(
                'channel_request',
                $title,
                $notification_message,
                $request_id,
                'channel',
                'normal'
            );
            
        } catch (Exception $e) {
            error_log("Error creating channel request notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Crear notificación cuando se descarga una aplicación
     */
    public function onAppDownload($app_name, $user_id = null, $ip_address = null) {
        try {
            $username = 'Anónimo';
            if ($user_id) {
                $stmt = $this->pdo->prepare("SELECT username FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                $username = $user ? $user['username'] : "Usuario #$user_id";
            }
            
            $title = "📱 Descarga de aplicación";
            $notification_message = "App: $app_name\nUsuario: $username";
            if ($ip_address) {
                $notification_message .= "\nIP: $ip_address";
            }
            
            return $this->createNotification(
                'app_download',
                $title,
                $notification_message,
                null,
                'download',
                'low'
            );
            
        } catch (Exception $e) {
            error_log("Error creating app download notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Crear notificación cuando se actualiza el estado de un ticket
     */
    public function onTicketStatusUpdate($ticket_id, $old_status, $new_status, $admin_user = null) {
        try {
            $title = "🔄 Ticket actualizado";
            $notification_message = "Ticket #$ticket_id\nEstado: $old_status → $new_status";
            if ($admin_user) {
                $notification_message .= "\nPor: $admin_user";
            }
            
            return $this->createNotification(
                'ticket_update',
                $title,
                $notification_message,
                $ticket_id,
                'ticket',
                'normal'
            );
            
        } catch (Exception $e) {
            error_log("Error creating ticket update notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Crear notificación cuando hay actividad sospechosa
     */
    public function onSecurityAlert($alert_type, $description, $ip_address = null, $user_id = null) {
        try {
            $title = "🚨 Alerta de seguridad";
            $notification_message = "Tipo: $alert_type\nDescripción: $description";
            if ($ip_address) {
                $notification_message .= "\nIP: $ip_address";
            }
            if ($user_id) {
                $notification_message .= "\nUsuario ID: $user_id";
            }
            
            return $this->createNotification(
                'security_alert',
                $title,
                $notification_message,
                null,
                'security',
                'urgent'
            );
            
        } catch (Exception $e) {
            error_log("Error creating security alert notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Crear notificación cuando hay errores del sistema
     */
    public function onSystemError($error_type, $error_message, $file = null, $line = null) {
        try {
            $title = "⚠️ Error del sistema";
            $notification_message = "Tipo: $error_type\nError: $error_message";
            if ($file) {
                $notification_message .= "\nArchivo: $file";
            }
            if ($line) {
                $notification_message .= "\nLínea: $line";
            }
            
            return $this->createNotification(
                'system_error',
                $title,
                $notification_message,
                null,
                'system',
                'high'
            );
            
        } catch (Exception $e) {
            error_log("Error creating system error notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Función base para crear notificaciones
     */
    private function createNotification($type, $title, $message, $reference_id = null, $reference_type = null, $priority = 'normal') {
        try {
            // Crear tabla si no existe
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS admin_notifications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    type VARCHAR(50) NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    reference_id INT,
                    reference_type VARCHAR(50),
                    source_admin VARCHAR(50) DEFAULT 'system',
                    target_admin VARCHAR(50) DEFAULT 'all',
                    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                    is_read BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    read_at TIMESTAMP NULL,
                    INDEX idx_type (type),
                    INDEX idx_read (is_read),
                    INDEX idx_created (created_at),
                    INDEX idx_target (target_admin)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            $stmt = $this->pdo->prepare("
                INSERT INTO admin_notifications 
                (type, title, message, reference_id, reference_type, priority, target_admin, source_admin) 
                VALUES (?, ?, ?, ?, ?, ?, 'all', 'system')
            ");
            
            $result = $stmt->execute([
                $type, 
                $title, 
                $message, 
                $reference_id, 
                $reference_type, 
                $priority
            ]);
            
            if ($result) {
                $notification_id = $this->pdo->lastInsertId();
                error_log("Notification created: ID=$notification_id, Type=$type, Title=$title");
                return $notification_id;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Error creating notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Limpiar notificaciones antiguas (más de 30 días)
     */
    public function cleanupOldNotifications() {
        try {
            $stmt = $this->pdo->prepare("
                DELETE FROM admin_notifications 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");
            $stmt->execute();
            
            $deleted = $stmt->rowCount();
            if ($deleted > 0) {
                error_log("Cleaned up $deleted old notifications");
            }
            
            return $deleted;
            
        } catch (Exception $e) {
            error_log("Error cleaning up notifications: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtener estadísticas de notificaciones
     */
    public function getNotificationStats() {
        try {
            $stmt = $this->pdo->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN priority = 'urgent' AND is_read = FALSE THEN 1 ELSE 0 END) as urgent,
                    SUM(CASE WHEN priority = 'high' AND is_read = FALSE THEN 1 ELSE 0 END) as high,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today
                FROM admin_notifications
            ");
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting notification stats: " . $e->getMessage());
            return [
                'total' => 0,
                'unread' => 0,
                'urgent' => 0,
                'high' => 0,
                'today' => 0
            ];
        }
    }
}

// Función global para crear triggers fácilmente
function createNotificationTrigger() {
    global $pdo;
    return new NotificationTriggers($pdo);
}

// Funciones de conveniencia para usar en otros archivos
function notifyNewChatMessage($user_id, $message, $chat_session_id = null) {
    $trigger = createNotificationTrigger();
    return $trigger->onNewChatMessage($user_id, $message, $chat_session_id);
}

function notifyNewSupportTicket($ticket_id, $user_id, $subject, $priority = 'normal') {
    $trigger = createNotificationTrigger();
    return $trigger->onNewSupportTicket($ticket_id, $user_id, $subject, $priority);
}

function notifyNewChannelRequest($request_id, $user_id, $channel_name, $channel_url = null) {
    $trigger = createNotificationTrigger();
    return $trigger->onNewChannelRequest($request_id, $user_id, $channel_name, $channel_url);
}

function notifyAppDownload($app_name, $user_id = null, $ip_address = null) {
    $trigger = createNotificationTrigger();
    return $trigger->onAppDownload($app_name, $user_id, $ip_address);
}

function notifyTicketStatusUpdate($ticket_id, $old_status, $new_status, $admin_user = null) {
    $trigger = createNotificationTrigger();
    return $trigger->onTicketStatusUpdate($ticket_id, $old_status, $new_status, $admin_user);
}

function notifySecurityAlert($alert_type, $description, $ip_address = null, $user_id = null) {
    $trigger = createNotificationTrigger();
    return $trigger->onSecurityAlert($alert_type, $description, $ip_address, $user_id);
}

function notifySystemError($error_type, $error_message, $file = null, $line = null) {
    $trigger = createNotificationTrigger();
    return $trigger->onSystemError($error_type, $error_message, $file, $line);
}
?>
