<?php
// API para chat en tiempo real
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_messages':
            $session_id = (int)($_GET['session_id'] ?? 0);
            if ($session_id) {
                $stmt = $pdo->prepare("
                    SELECT cm.*, u.username 
                    FROM chat_messages cm 
                    LEFT JOIN users u ON cm.sender_id = u.id 
                    WHERE cm.session_id = ? 
                    ORDER BY cm.sent_at ASC
                ");
                $stmt->execute([$session_id]);
                $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo json_encode(['success' => true, 'messages' => $messages]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Session ID required']);
            }
            break;
            
        case 'send_message':
            $session_id = (int)($_POST['session_id'] ?? 0);
            $message = trim($_POST['message'] ?? '');
            $is_admin = isset($_SESSION['admin_logged_in']) ? 1 : 0;
            
            if ($session_id && $message) {
                $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, ?)");
                $result = $stmt->execute([$session_id, $user_id, $message, $is_admin]);
                
                if ($result) {
                    // Si es admin, actualizar estado de sesión a activa
                    if ($is_admin) {
                        $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'active' WHERE id = ?");
                        $stmt->execute([$session_id]);
                    }
                    
                    echo json_encode(['success' => true, 'message' => 'Message sent']);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Failed to send message']);
                }
            } else {
                echo json_encode(['success' => false, 'error' => 'Session ID and message required']);
            }
            break;
            
        case 'get_sessions':
            // Solo para admin
            if (!isset($_SESSION['admin_logged_in'])) {
                echo json_encode(['success' => false, 'error' => 'Admin access required']);
                break;
            }
            
            $stmt = $pdo->prepare("
                SELECT cs.*, u.username 
                FROM chat_sessions cs 
                LEFT JOIN users u ON cs.user_id = u.id 
                WHERE cs.status IN ('waiting', 'active')
                ORDER BY 
                    CASE cs.status 
                        WHEN 'waiting' THEN 1 
                        WHEN 'active' THEN 2 
                    END,
                    cs.started_at DESC
            ");
            $stmt->execute();
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'sessions' => $sessions]);
            break;
            
        case 'get_user_session':
            $stmt = $pdo->prepare("
                SELECT * FROM chat_sessions 
                WHERE user_id = ? AND status IN ('waiting', 'active') 
                ORDER BY started_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$user_id]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'session' => $session]);
            break;
            
        case 'start_chat':
            $initial_message = trim($_POST['initial_message'] ?? 'Hola, necesito ayuda');
            
            // Verificar si ya tiene una sesión activa
            $stmt = $pdo->prepare("SELECT id FROM chat_sessions WHERE user_id = ? AND status IN ('waiting', 'active')");
            $stmt->execute([$user_id]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                echo json_encode(['success' => true, 'session_id' => $existing['id'], 'message' => 'Session already exists']);
            } else {
                // Crear nueva sesión
                $stmt = $pdo->prepare("INSERT INTO chat_sessions (user_id, status) VALUES (?, 'waiting')");
                $stmt->execute([$user_id]);
                $session_id = $pdo->lastInsertId();
                
                // Mensaje inicial
                $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, 0)");
                $stmt->execute([$session_id, $user_id, $initial_message]);
                
                echo json_encode(['success' => true, 'session_id' => $session_id, 'message' => 'Chat started']);
            }
            break;
            
        case 'end_chat':
            $session_id = (int)($_POST['session_id'] ?? 0);
            
            if ($session_id) {
                $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'ended', ended_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$session_id]);
                
                echo json_encode(['success' => $result, 'message' => 'Chat ended']);
            } else {
                echo json_encode(['success' => false, 'error' => 'Session ID required']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
