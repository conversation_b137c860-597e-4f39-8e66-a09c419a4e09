<?php
/**
 * API rápida para estadísticas de soporte
 * Optimizada para carga rápida y tiempo real
 */

session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Verificar que sea un administrador
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

try {
    // Usar una sola consulta optimizada para obtener todas las estadísticas
    $stats = [];
    
    // Estadísticas de tickets (con verificación de tabla)
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'support_tickets'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved
                FROM support_tickets
            ");
            $ticket_data = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['tickets_open'] = (int)$ticket_data['open'];
            $stats['tickets_in_progress'] = (int)$ticket_data['in_progress'];
            $stats['tickets_resolved'] = (int)$ticket_data['resolved'];
            $stats['tickets_total'] = (int)$ticket_data['total'];
        } else {
            $stats['tickets_open'] = 0;
            $stats['tickets_in_progress'] = 0;
            $stats['tickets_resolved'] = 0;
            $stats['tickets_total'] = 0;
        }
    } catch (Exception $e) {
        $stats['tickets_open'] = 0;
        $stats['tickets_in_progress'] = 0;
        $stats['tickets_resolved'] = 0;
        $stats['tickets_total'] = 0;
    }
    
    // Estadísticas de chat (con verificación de tabla)
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'chat_sessions'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT 
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting,
                    SUM(CASE WHEN DATE(started_at) = CURDATE() THEN 1 ELSE 0 END) as today
                FROM chat_sessions
            ");
            $chat_data = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['chat_active'] = (int)$chat_data['active'];
            $stats['chat_waiting'] = (int)$chat_data['waiting'];
            $stats['chat_today'] = (int)$chat_data['today'];
        } else {
            $stats['chat_active'] = 0;
            $stats['chat_waiting'] = 0;
            $stats['chat_today'] = 0;
        }
    } catch (Exception $e) {
        $stats['chat_active'] = 0;
        $stats['chat_waiting'] = 0;
        $stats['chat_today'] = 0;
    }
    
    // Estadísticas de canales (con verificación de tabla)
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'channel_requests'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT 
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' AND DATE(updated_at) = CURDATE() THEN 1 ELSE 0 END) as approved_today
                FROM channel_requests
            ");
            $channel_data = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['channels_pending'] = (int)$channel_data['pending'];
            $stats['channels_approved_today'] = (int)$channel_data['approved_today'];
        } else {
            $stats['channels_pending'] = 0;
            $stats['channels_approved_today'] = 0;
        }
    } catch (Exception $e) {
        $stats['channels_pending'] = 0;
        $stats['channels_approved_today'] = 0;
    }
    
    // Estadísticas de activaciones (con verificación de tabla)
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'activation_codes'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT COUNT(*) as pending
                FROM activation_codes 
                WHERE status = 'active'
            ");
            $activations_pending = $stmt->fetchColumn();
            $stats['activations_pending'] = (int)$activations_pending;
        } else {
            $stats['activations_pending'] = 0;
        }
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_activations'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT COUNT(*) as today
                FROM user_activations 
                WHERE DATE(activated_at) = CURDATE()
            ");
            $activations_today = $stmt->fetchColumn();
            $stats['activations_today'] = (int)$activations_today;
        } else {
            $stats['activations_today'] = 0;
        }
    } catch (Exception $e) {
        $stats['activations_pending'] = 0;
        $stats['activations_today'] = 0;
    }
    
    // Estadísticas de aplicaciones (simuladas por ahora)
    $stats['downloads_today'] = 0;
    $stats['total_downloads'] = 0;
    
    // Estadísticas de notificaciones
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total_notifications,
                    SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread_notifications,
                    SUM(CASE WHEN priority = 'urgent' AND is_read = FALSE THEN 1 ELSE 0 END) as urgent_notifications,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_notifications
                FROM admin_notifications
            ");
            $notif_data = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['total_notifications'] = (int)$notif_data['total_notifications'];
            $stats['unread_notifications'] = (int)$notif_data['unread_notifications'];
            $stats['urgent_notifications'] = (int)$notif_data['urgent_notifications'];
            $stats['today_notifications'] = (int)$notif_data['today_notifications'];
        } else {
            $stats['total_notifications'] = 0;
            $stats['unread_notifications'] = 0;
            $stats['urgent_notifications'] = 0;
            $stats['today_notifications'] = 0;
        }
    } catch (Exception $e) {
        $stats['total_notifications'] = 0;
        $stats['unread_notifications'] = 0;
        $stats['urgent_notifications'] = 0;
        $stats['today_notifications'] = 0;
    }
    
    // Estadísticas por tipo de notificación
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("
                SELECT 
                    type,
                    COUNT(*) as count
                FROM admin_notifications 
                WHERE is_read = FALSE
                GROUP BY type
            ");
            $notification_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $notification_counters = [
                'chat_message' => 0,
                'support_ticket' => 0,
                'channel_request' => 0,
                'app_download' => 0,
                'security_alert' => 0,
                'system_error' => 0
            ];
            
            foreach ($notification_types as $type_data) {
                if (isset($notification_counters[$type_data['type']])) {
                    $notification_counters[$type_data['type']] = (int)$type_data['count'];
                }
            }
            
            $stats['notification_counters'] = $notification_counters;
        } else {
            $stats['notification_counters'] = [
                'chat_message' => 0,
                'support_ticket' => 0,
                'channel_request' => 0,
                'app_download' => 0,
                'security_alert' => 0,
                'system_error' => 0
            ];
        }
    } catch (Exception $e) {
        $stats['notification_counters'] = [
            'chat_message' => 0,
            'support_ticket' => 0,
            'channel_request' => 0,
            'app_download' => 0,
            'security_alert' => 0,
            'system_error' => 0
        ];
    }
    
    // Agregar timestamp para cache busting
    $stats['timestamp'] = time();
    $stats['datetime'] = date('Y-m-d H:i:s');
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'tickets_open' => $stats['tickets_open'],
        'chat_active' => $stats['chat_active'],
        'downloads_today' => $stats['downloads_today'],
        'channels_pending' => $stats['channels_pending'],
        'activations_pending' => $stats['activations_pending'],
        'unread_notifications' => $stats['unread_notifications']
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'stats' => [
            'tickets_open' => 0,
            'tickets_in_progress' => 0,
            'tickets_resolved' => 0,
            'tickets_total' => 0,
            'chat_active' => 0,
            'chat_waiting' => 0,
            'chat_today' => 0,
            'channels_pending' => 0,
            'channels_approved_today' => 0,
            'activations_pending' => 0,
            'activations_today' => 0,
            'downloads_today' => 0,
            'total_downloads' => 0,
            'total_notifications' => 0,
            'unread_notifications' => 0,
            'urgent_notifications' => 0,
            'today_notifications' => 0,
            'notification_counters' => [
                'chat_message' => 0,
                'support_ticket' => 0,
                'channel_request' => 0,
                'app_download' => 0,
                'security_alert' => 0,
                'system_error' => 0
            ]
        ]
    ]);
}
?>
