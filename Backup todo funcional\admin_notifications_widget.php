<!-- Widget de Notificaciones para Admin -->
<div id="notificationsWidget" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
    <!-- Botón de notificaciones -->
    <div id="notificationButton" style="
        background: #2563eb;
        color: white;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        position: relative;
    ">
        <i class="fas fa-bell" style="font-size: 24px;"></i>
        <span id="notificationBadge" style="
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        ">0</span>
    </div>
    
    <!-- Panel de notificaciones -->
    <div id="notificationPanel" style="
        position: absolute;
        top: 70px;
        right: 0;
        width: 400px;
        max-height: 500px;
        background: #1e293b;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.6);
        border: 1px solid #334155;
        display: none;
        overflow: hidden;
    ">
        <!-- Header del panel -->
        <div style="
            padding: 1rem;
            background: #0f172a;
            border-bottom: 1px solid #334155;
            display: flex;
            justify-content: space-between;
            align-items: center;
        ">
            <h3 style="margin: 0; color: #f8fafc; font-size: 1.1rem;">
                <i class="fas fa-bell"></i> Notificaciones
            </h3>
            <button id="markAllRead" style="
                background: #10b981;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.8rem;
            ">
                Marcar todas
            </button>
        </div>
        
        <!-- Lista de notificaciones -->
        <div id="notificationsList" style="
            max-height: 400px;
            overflow-y: auto;
            padding: 0;
        ">
            <div style="
                padding: 2rem;
                text-align: center;
                color: #cbd5e1;
            ">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                <p>Cargando notificaciones...</p>
            </div>
        </div>
        
        <!-- Footer del panel -->
        <div style="
            padding: 1rem;
            background: #0f172a;
            border-top: 1px solid #334155;
            text-align: center;
        ">
            <a href="admin2.php" style="
                color: #3b82f6;
                text-decoration: none;
                font-size: 0.9rem;
            ">
                <i class="fas fa-external-link-alt"></i>
                Ver panel de soporte completo
            </a>
        </div>
    </div>
</div>

<style>
#notificationsWidget .notification-item {
    padding: 1rem;
    border-bottom: 1px solid #334155;
    cursor: pointer;
    transition: background 0.2s ease;
}

#notificationsWidget .notification-item:hover {
    background: rgba(59, 130, 246, 0.1);
}

#notificationsWidget .notification-item.unread {
    background: rgba(16, 185, 129, 0.05);
    border-left: 3px solid #10b981;
}

#notificationsWidget .notification-title {
    font-weight: 600;
    color: #f8fafc;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

#notificationsWidget .notification-message {
    color: #cbd5e1;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

#notificationsWidget .notification-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.7rem;
    color: #64748b;
}

#notificationsWidget .notification-type {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

#notificationsWidget .type-ticket { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
#notificationsWidget .type-chat { background: rgba(16, 185, 129, 0.2); color: #10b981; }
#notificationsWidget .type-channel_request { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
#notificationsWidget .type-app_download { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; }
#notificationsWidget .type-system { background: rgba(239, 68, 68, 0.2); color: #ef4444; }

@media (max-width: 768px) {
    #notificationsWidget {
        position: fixed;
        top: 10px;
        right: 10px;
    }
    
    #notificationPanel {
        width: 320px;
        max-height: 400px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const notificationButton = document.getElementById('notificationButton');
    const notificationPanel = document.getElementById('notificationPanel');
    const notificationBadge = document.getElementById('notificationBadge');
    const notificationsList = document.getElementById('notificationsList');
    const markAllReadBtn = document.getElementById('markAllRead');
    
    let isOpen = false;
    let notifications = [];
    
    // Toggle panel
    notificationButton.addEventListener('click', function() {
        isOpen = !isOpen;
        notificationPanel.style.display = isOpen ? 'block' : 'none';
        
        if (isOpen) {
            loadNotifications();
        }
    });
    
    // Cerrar panel al hacer clic fuera
    document.addEventListener('click', function(e) {
        if (!document.getElementById('notificationsWidget').contains(e.target)) {
            isOpen = false;
            notificationPanel.style.display = 'none';
        }
    });
    
    // Marcar todas como leídas
    markAllReadBtn.addEventListener('click', function() {
        fetch('api_admin_notifications.php?action=mark_all_read', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications();
            }
        });
    });
    
    // Cargar notificaciones
    function loadNotifications() {
        fetch('api_admin_notifications.php?action=get_notifications')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    notifications = data.notifications;
                    updateNotificationsList(data.notifications);
                    updateBadge(data.total_unread);
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                notificationsList.innerHTML = `
                    <div style="padding: 2rem; text-align: center; color: #ef4444;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <p>Error al cargar notificaciones</p>
                    </div>
                `;
            });
    }
    
    // Actualizar lista de notificaciones
    function updateNotificationsList(notifications) {
        if (notifications.length === 0) {
            notificationsList.innerHTML = `
                <div style="padding: 2rem; text-align: center; color: #cbd5e1;">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem; color: #10b981;"></i>
                    <p>No hay notificaciones nuevas</p>
                </div>
            `;
            return;
        }
        
        notificationsList.innerHTML = notifications.map(notification => `
            <div class="notification-item ${notification.is_read == 0 ? 'unread' : ''}" 
                 onclick="handleNotificationClick(${notification.id}, '${notification.type}', ${notification.reference_id})">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-meta">
                    <span class="notification-type type-${notification.type}">${notification.type}</span>
                    <span>${formatDate(notification.created_at)}</span>
                </div>
            </div>
        `).join('');
    }
    
    // Actualizar badge
    function updateBadge(count) {
        if (count > 0) {
            notificationBadge.style.display = 'flex';
            notificationBadge.textContent = count > 99 ? '99+' : count;
        } else {
            notificationBadge.style.display = 'none';
        }
    }
    
    // Manejar clic en notificación
    window.handleNotificationClick = function(notificationId, type, referenceId) {
        // Marcar como leída
        fetch('api_admin_notifications.php?action=mark_read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `notification_id=${notificationId}`
        });
        
        // Redirigir según el tipo
        let url = 'admin2.php';
        switch (type) {
            case 'ticket':
                url = `ticket_detail.php?id=${referenceId}`;
                break;
            case 'chat':
                url = `admin_chat_real.php?session=${referenceId}`;
                break;
            case 'channel_request':
                url = `admin_channels_real.php?id=${referenceId}`;
                break;
            case 'app_download':
                url = `apps_admin.php`;
                break;
        }
        
        window.open(url, '_blank');
    };
    
    // Formatear fecha
    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'Ahora';
        if (diff < 3600000) return Math.floor(diff / 60000) + 'm';
        if (diff < 86400000) return Math.floor(diff / 3600000) + 'h';
        return Math.floor(diff / 86400000) + 'd';
    }
    
    // Auto-actualizar cada 30 segundos
    setInterval(function() {
        if (!isOpen) {
            fetch('api_admin_notifications.php?action=get_notifications')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateBadge(data.total_unread);
                    }
                });
        }
    }, 30000);
    
    // Cargar notificaciones inicial
    loadNotifications();
});
</script>
