<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Obtener estadísticas en tiempo real
try {
    // Estadísticas de tickets
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
    $tickets_open = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status IN ('open', 'in_progress')");
    $tickets_pending = $stmt->fetchColumn() ?: 0;

    // Estadísticas de chat
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
    $chat_active = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'waiting'");
    $chat_waiting = $stmt->fetchColumn() ?: 0;

    // Estadísticas de aplicaciones
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE status = 'published'");
    $apps_published = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE status = 'draft'");
    $apps_draft = $stmt->fetchColumn() ?: 0;

    // Estadísticas de ayuda
    $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles WHERE status = 'published'");
    $help_published = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles WHERE is_featured = 1");
    $help_featured = $stmt->fetchColumn() ?: 0;

    // Estadísticas de canales
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
    $channels_pending = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'approved'");
    $channels_approved = $stmt->fetchColumn() ?: 0;

    // Estadísticas de activaciones
    $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE status = 'active'");
    $codes_active = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM user_activations WHERE DATE(activated_at) = CURDATE()");
    $activations_today = $stmt->fetchColumn() ?: 0;

    // Actividad reciente
    $stmt = $pdo->query("
        SELECT 'ticket' as type, subject as title, created_at, status, 'high' as priority
        FROM support_tickets
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
        UNION ALL
        SELECT 'chat' as type, CONCAT('Chat con usuario ', user_id) as title, started_at as created_at, status, 'medium' as priority
        FROM chat_sessions
        WHERE started_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        UNION ALL
        SELECT 'channel' as type, channel_name as title, created_at, status, 'low' as priority
        FROM channel_requests
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 HOUR)
        ORDER BY created_at DESC
        LIMIT 8
    ");
    $recent_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $error_message = "Error al obtener estadísticas: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ Servicios de Soporte Profesional - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1e40af;
            --primary-light: #3b82f6;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --light-bg: #f8fafc;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #f8fafc;
            --accent-color: #10b981;
            --accent-light: #34d399;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
            --gradient-error: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .header-nav {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--light-bg);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        .nav-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .nav-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Main Content */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Stats Overview */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .stat-card.success::before {
            background: var(--gradient-success);
        }

        .stat-card.warning::before {
            background: var(--gradient-warning);
        }

        .stat-card.error::before {
            background: var(--gradient-error);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .stat-icon.primary {
            background: rgba(30, 64, 175, 0.1);
            color: var(--primary-color);
        }

        .stat-icon.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-icon.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: var(--text-secondary);
        }

        /* Services Grid */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-module {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
        }

        .service-module:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .service-header {
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
        }

        .service-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .service-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .service-icon.tickets {
            background: var(--gradient-primary);
        }

        .service-icon.chat {
            background: var(--gradient-success);
        }

        .service-icon.apps {
            background: var(--gradient-warning);
        }

        .service-icon.help {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        }

        .service-icon.channels {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .service-icon.activations {
            background: var(--gradient-error);
        }

        .service-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .service-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .service-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .service-stat {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-light);
        }

        .service-stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .service-stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .service-content {
            padding: 2rem;
        }

        .service-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .service-btn {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            background: var(--light-bg);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        .service-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateX(4px);
        }

        .service-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .service-btn.primary:hover {
            background: var(--primary-light);
        }

        .service-btn.success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .service-btn.warning {
            background: var(--warning-color);
            color: white;
            border-color: var(--warning-color);
        }

        .service-btn-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .service-btn-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* Activity Feed */
        .activity-section {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .activity-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
        }

        .activity-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .activity-content {
            padding: 1.5rem 2rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-light);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .activity-icon.ticket {
            background: rgba(30, 64, 175, 0.1);
            color: var(--primary-color);
        }

        .activity-icon.chat {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .activity-icon.channel {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
        }

        .activity-details {
            flex: 1;
        }

        .activity-title-text {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .activity-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .activity-status.open {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .activity-status.active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .activity-status.pending {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .service-stats {
                grid-template-columns: 1fr;
            }
        }

        /* Loading Animation */
        .loading {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Update Indicator */
        .update-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--gradient-success);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 1000;
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
            opacity: 0;
            transform: translateY(10px);
        }

        .update-indicator.show {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="admin2_professional.php" class="logo">
                <i class="fas fa-cogs"></i>
                <span>Servicios de Soporte</span>
            </a>

            <nav class="header-nav">
                <a href="admin_professional.php" class="nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="system_status.php" class="nav-btn">
                    <i class="fas fa-server"></i>
                    <span>Estado</span>
                </a>
                <a href="index.php" class="nav-btn primary">
                    <i class="fas fa-home"></i>
                    <span>Ver Sitio</span>
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-cogs" style="color: var(--primary-color);"></i>
                Panel de Servicios de Soporte
            </h1>
            <p class="page-subtitle">
                Gestión centralizada de tickets, chat en vivo, aplicaciones, ayuda y más
            </p>
        </div>

        <!-- Stats Overview -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Tickets Activos</div>
                    <div class="stat-icon primary">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                </div>
                <div class="stat-number" id="ticketsOpenCount"><?php echo $tickets_open; ?></div>
                <div class="stat-change">
                    <i class="fas fa-clock"></i>
                    <?php echo $tickets_pending; ?> pendientes
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-header">
                    <div class="stat-title">Chat en Vivo</div>
                    <div class="stat-icon success">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
                <div class="stat-number" id="chatActiveCount"><?php echo $chat_active; ?></div>
                <div class="stat-change">
                    <i class="fas fa-hourglass-half"></i>
                    <?php echo $chat_waiting; ?> esperando
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-header">
                    <div class="stat-title">Apps Publicadas</div>
                    <div class="stat-icon warning">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                </div>
                <div class="stat-number" id="appsCount"><?php echo $apps_published; ?></div>
                <div class="stat-change">
                    <i class="fas fa-edit"></i>
                    <?php echo $apps_draft; ?> borradores
                </div>
            </div>

            <div class="stat-card error">
                <div class="stat-header">
                    <div class="stat-title">Canales Pendientes</div>
                    <div class="stat-icon error">
                        <i class="fas fa-tv"></i>
                    </div>
                </div>
                <div class="stat-number" id="channelsCount"><?php echo $channels_pending; ?></div>
                <div class="stat-change">
                    <i class="fas fa-check"></i>
                    <?php echo $channels_approved; ?> aprobados
                </div>
            </div>
        </div>

        <!-- Services Grid -->
        <div class="services-grid">
            <!-- Tickets Module -->
            <div class="service-module">
                <div class="service-header">
                    <div class="service-title">
                        <div class="service-icon tickets">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div>
                            <div class="service-name">Sistema de Tickets</div>
                            <div class="service-description">
                                Gestión completa de tickets de soporte técnico con seguimiento en tiempo real
                            </div>
                        </div>
                    </div>

                    <div class="service-stats">
                        <div class="service-stat">
                            <div class="service-stat-number" id="ticketsOpenStat"><?php echo $tickets_open; ?></div>
                            <div class="service-stat-label">Abiertos</div>
                        </div>
                        <div class="service-stat">
                            <div class="service-stat-number" id="ticketsPendingStat"><?php echo $tickets_pending; ?></div>
                            <div class="service-stat-label">Pendientes</div>
                        </div>
                    </div>
                </div>

                <div class="service-content">
                    <div class="service-actions">
                        <a href="tickets_admin.php" class="service-btn primary">
                            <div class="service-btn-content">
                                <i class="fas fa-cog"></i>
                                <span>Gestionar Tickets</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="tickets_admin.php?status=pending" class="service-btn">
                            <div class="service-btn-content">
                                <i class="fas fa-clock"></i>
                                <span>Ver Pendientes</span>
                                <span class="service-btn-badge" id="pendingBadge"><?php echo $tickets_pending; ?></span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="user_tickets.php" class="service-btn">
                            <div class="service-btn-content">
                                <i class="fas fa-plus"></i>
                                <span>Crear Ticket</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Chat Module -->
            <div class="service-module">
                <div class="service-header">
                    <div class="service-title">
                        <div class="service-icon chat">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div>
                            <div class="service-name">Chat en Tiempo Real</div>
                            <div class="service-description">
                                Soporte instantáneo con chat en vivo y gestión de sesiones activas
                            </div>
                        </div>
                    </div>

                    <div class="service-stats">
                        <div class="service-stat">
                            <div class="service-stat-number" id="chatActiveStat"><?php echo $chat_active; ?></div>
                            <div class="service-stat-label">Activos</div>
                        </div>
                        <div class="service-stat">
                            <div class="service-stat-number" id="chatWaitingStat"><?php echo $chat_waiting; ?></div>
                            <div class="service-stat-label">Esperando</div>
                        </div>
                    </div>
                </div>

                <div class="service-content">
                    <div class="service-actions">
                        <a href="admin_chat_real.php" class="service-btn primary">
                            <div class="service-btn-content">
                                <i class="fas fa-comment-dots"></i>
                                <span>Panel de Chat</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <?php if ($chat_waiting > 0): ?>
                        <a href="admin_chat_real.php" class="service-btn warning">
                            <div class="service-btn-content">
                                <i class="fas fa-hourglass-half"></i>
                                <span>Chats Esperando</span>
                                <span class="service-btn-badge" id="waitingBadge"><?php echo $chat_waiting; ?></span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                        <?php endif; ?>

                        <a href="user_chat_realtime.php" class="service-btn">
                            <div class="service-btn-content">
                                <i class="fas fa-play"></i>
                                <span>Iniciar Chat</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Apps Module -->
            <div class="service-module">
                <div class="service-header">
                    <div class="service-title">
                        <div class="service-icon apps">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div>
                            <div class="service-name">Gestión de Aplicaciones</div>
                            <div class="service-description">
                                Administración de aplicaciones móviles y de escritorio para usuarios
                            </div>
                        </div>
                    </div>

                    <div class="service-stats">
                        <div class="service-stat">
                            <div class="service-stat-number" id="appsPublishedStat"><?php echo $apps_published; ?></div>
                            <div class="service-stat-label">Publicadas</div>
                        </div>
                        <div class="service-stat">
                            <div class="service-stat-number" id="appsDraftStat"><?php echo $apps_draft; ?></div>
                            <div class="service-stat-label">Borradores</div>
                        </div>
                    </div>
                </div>

                <div class="service-content">
                    <div class="service-actions">
                        <a href="apps_admin.php" class="service-btn primary">
                            <div class="service-btn-content">
                                <i class="fas fa-cog"></i>
                                <span>Gestionar Apps</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="apps_admin.php?action=new" class="service-btn success">
                            <div class="service-btn-content">
                                <i class="fas fa-plus"></i>
                                <span>Nueva Aplicación</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="user_apps.php" class="service-btn">
                            <div class="service-btn-content">
                                <i class="fas fa-download"></i>
                                <span>Ver Catálogo</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Help Module -->
            <div class="service-module">
                <div class="service-header">
                    <div class="service-title">
                        <div class="service-icon help">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div>
                            <div class="service-name">Centro de Ayuda</div>
                            <div class="service-description">
                                Gestión de artículos de ayuda, tutoriales y documentación
                            </div>
                        </div>
                    </div>

                    <div class="service-stats">
                        <div class="service-stat">
                            <div class="service-stat-number" id="helpPublishedStat"><?php echo $help_published; ?></div>
                            <div class="service-stat-label">Artículos</div>
                        </div>
                        <div class="service-stat">
                            <div class="service-stat-number" id="helpFeaturedStat"><?php echo $help_featured; ?></div>
                            <div class="service-stat-label">Destacados</div>
                        </div>
                    </div>
                </div>

                <div class="service-content">
                    <div class="service-actions">
                        <a href="help_admin.php" class="service-btn primary">
                            <div class="service-btn-content">
                                <i class="fas fa-cog"></i>
                                <span>Gestionar Ayuda</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="help_admin.php?action=new" class="service-btn success">
                            <div class="service-btn-content">
                                <i class="fas fa-plus"></i>
                                <span>Nuevo Artículo</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="user_help.php" class="service-btn">
                            <div class="service-btn-content">
                                <i class="fas fa-book"></i>
                                <span>Ver Centro</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Channels Module -->
            <div class="service-module">
                <div class="service-header">
                    <div class="service-title">
                        <div class="service-icon channels">
                            <i class="fas fa-tv"></i>
                        </div>
                        <div>
                            <div class="service-name">Gestión de Canales</div>
                            <div class="service-description">
                                Administración de solicitudes de canales y contenido IPTV
                            </div>
                        </div>
                    </div>

                    <div class="service-stats">
                        <div class="service-stat">
                            <div class="service-stat-number" id="channelsPendingStat"><?php echo $channels_pending; ?></div>
                            <div class="service-stat-label">Pendientes</div>
                        </div>
                        <div class="service-stat">
                            <div class="service-stat-number" id="channelsApprovedStat"><?php echo $channels_approved; ?></div>
                            <div class="service-stat-label">Aprobados</div>
                        </div>
                    </div>
                </div>

                <div class="service-content">
                    <div class="service-actions">
                        <a href="admin_channels_real.php" class="service-btn primary">
                            <div class="service-btn-content">
                                <i class="fas fa-cog"></i>
                                <span>Gestionar Canales</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <?php if ($channels_pending > 0): ?>
                        <a href="admin_channels_real.php?status=pending" class="service-btn warning">
                            <div class="service-btn-content">
                                <i class="fas fa-clock"></i>
                                <span>Revisar Pendientes</span>
                                <span class="service-btn-badge" id="channelsPendingBadge"><?php echo $channels_pending; ?></span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                        <?php endif; ?>

                        <a href="user_channels.php" class="service-btn">
                            <div class="service-btn-content">
                                <i class="fas fa-plus"></i>
                                <span>Solicitar Canal</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Activations Module -->
            <div class="service-module">
                <div class="service-header">
                    <div class="service-title">
                        <div class="service-icon activations">
                            <i class="fas fa-key"></i>
                        </div>
                        <div>
                            <div class="service-name">Sistema de Activaciones</div>
                            <div class="service-description">
                                Gestión de códigos de activación y listas de usuarios
                            </div>
                        </div>
                    </div>

                    <div class="service-stats">
                        <div class="service-stat">
                            <div class="service-stat-number" id="codesActiveStat"><?php echo $codes_active; ?></div>
                            <div class="service-stat-label">Códigos</div>
                        </div>
                        <div class="service-stat">
                            <div class="service-stat-number" id="activationsTodayStat"><?php echo $activations_today; ?></div>
                            <div class="service-stat-label">Hoy</div>
                        </div>
                    </div>
                </div>

                <div class="service-content">
                    <div class="service-actions">
                        <a href="activations_admin.php" class="service-btn primary">
                            <div class="service-btn-content">
                                <i class="fas fa-cog"></i>
                                <span>Gestionar Códigos</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="activations_admin.php?action=generate" class="service-btn success">
                            <div class="service-btn-content">
                                <i class="fas fa-plus"></i>
                                <span>Generar Código</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>

                        <a href="user_activation.php" class="service-btn">
                            <div class="service-btn-content">
                                <i class="fas fa-unlock"></i>
                                <span>Activar Lista</span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Feed -->
        <div class="activity-section">
            <div class="activity-header">
                <h2 class="activity-title">
                    <i class="fas fa-clock"></i>
                    Actividad Reciente
                </h2>
            </div>

            <div class="activity-content">
                <?php if (empty($recent_activities)): ?>
                <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                    <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <h3>No hay actividad reciente</h3>
                    <p>La actividad de las últimas horas aparecerá aquí</p>
                </div>
                <?php else: ?>
                    <?php foreach ($recent_activities as $activity): ?>
                    <div class="activity-item">
                        <div class="activity-icon <?php echo $activity['type']; ?>">
                            <i class="fas fa-<?php
                                echo $activity['type'] === 'ticket' ? 'ticket-alt' :
                                    ($activity['type'] === 'chat' ? 'comments' : 'tv');
                            ?>"></i>
                        </div>

                        <div class="activity-details">
                            <div class="activity-title-text">
                                <?php echo htmlspecialchars($activity['title']); ?>
                            </div>
                            <div class="activity-time">
                                <?php
                                $time_diff = time() - strtotime($activity['created_at']);
                                if ($time_diff < 60) {
                                    echo "Hace " . $time_diff . " segundos";
                                } elseif ($time_diff < 3600) {
                                    echo "Hace " . floor($time_diff / 60) . " minutos";
                                } else {
                                    echo "Hace " . floor($time_diff / 3600) . " horas";
                                }
                                ?>
                            </div>
                        </div>

                        <div class="activity-status <?php echo $activity['status']; ?>">
                            <?php
                            $status_labels = [
                                'open' => 'Abierto',
                                'active' => 'Activo',
                                'pending' => 'Pendiente',
                                'waiting' => 'Esperando',
                                'resolved' => 'Resuelto',
                                'approved' => 'Aprobado'
                            ];
                            echo $status_labels[$activity['status']] ?? ucfirst($activity['status']);
                            ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Update Indicator -->
    <div id="updateIndicator" class="update-indicator">
        <i class="fas fa-sync-alt"></i>
        <span>Actualizando...</span>
    </div>

    <script>
        // Auto-refresh stats every 15 seconds
        setInterval(async function() {
            try {
                const response = await fetch('api_admin_stats.php');
                const data = await response.json();

                if (data.success && data.stats) {
                    // Update main stats
                    updateElement('ticketsOpenCount', data.stats.tickets_open);
                    updateElement('chatActiveCount', data.stats.chat_active);
                    updateElement('appsCount', data.stats.apps_published);
                    updateElement('channelsCount', data.stats.channels_pending);

                    // Update service stats
                    updateElement('ticketsOpenStat', data.stats.tickets_open);
                    updateElement('ticketsPendingStat', data.stats.tickets_pending);
                    updateElement('chatActiveStat', data.stats.chat_active);
                    updateElement('chatWaitingStat', data.stats.chat_waiting);
                    updateElement('appsPublishedStat', data.stats.apps_published);
                    updateElement('channelsPendingStat', data.stats.channels_pending);
                    updateElement('channelsApprovedStat', data.stats.channels_approved);
                    updateElement('codesActiveStat', data.stats.codes_active);

                    // Update badges
                    updateElement('pendingBadge', data.stats.tickets_pending);
                    updateElement('waitingBadge', data.stats.chat_waiting);
                    updateElement('channelsPendingBadge', data.stats.channels_pending);

                    // Show update indicator
                    showUpdateIndicator();
                }
            } catch (error) {
                console.log('Stats update failed:', error);
            }
        }, 15000);

        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element && element.textContent != value) {
                element.textContent = value;

                // Add animation for changes
                element.style.transform = 'scale(1.1)';
                element.style.color = 'var(--accent-color)';

                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 300);
            }
        }

        function showUpdateIndicator() {
            const indicator = document.getElementById('updateIndicator');
            const now = new Date();
            const timeString = now.toLocaleTimeString('es-ES', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            indicator.innerHTML = `<i class="fas fa-sync-alt"></i> Actualizado: ${timeString}`;
            indicator.classList.add('show');

            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }

        // Add loading states to service buttons
        document.querySelectorAll('.service-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (!this.classList.contains('loading')) {
                    const icon = this.querySelector('i');
                    const originalClass = icon.className;

                    this.classList.add('loading');
                    icon.className = 'fas fa-spinner fa-spin';

                    setTimeout(() => {
                        icon.className = originalClass;
                        this.classList.remove('loading');
                    }, 1000);
                }
            });
        });

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-2px)';
            });
        });

        // Add click effects to service modules
        document.querySelectorAll('.service-module').forEach(module => {
            module.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-6px)';
            });

            module.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-4px)';
            });
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show initial update indicator
            setTimeout(showUpdateIndicator, 1000);

            // Add smooth scroll for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Add notification for urgent items
        function checkUrgentItems() {
            const waitingChats = parseInt(document.getElementById('chatWaitingStat').textContent);
            const pendingTickets = parseInt(document.getElementById('ticketsPendingStat').textContent);

            if (waitingChats > 0 || pendingTickets > 5) {
                if ('Notification' in window && Notification.permission === 'granted') {
                    let message = '';
                    if (waitingChats > 0) message += `${waitingChats} chat(s) esperando. `;
                    if (pendingTickets > 5) message += `${pendingTickets} tickets pendientes.`;

                    new Notification('Atención requerida', {
                        body: message,
                        icon: '/favicon.ico'
                    });
                }
            }
        }

        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Check for urgent items every 30 seconds
        setInterval(checkUrgentItems, 30000);
    </script>
</body>
</html>