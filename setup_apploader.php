<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>📱 Configurando Sistema de Aplicaciones</h1>";

try {
    // 1. Crear directorio apploader
    echo "<h2>📁 Creando directorio apploader...</h2>";
    
    $apploader_dir = __DIR__ . '/apploader';
    if (!is_dir($apploader_dir)) {
        if (mkdir($apploader_dir, 0755, true)) {
            $success_messages[] = "✅ Directorio apploader creado: $apploader_dir";
        } else {
            $error_messages[] = "❌ Error creando directorio apploader";
        }
    } else {
        $success_messages[] = "✅ Directorio apploader ya existe";
    }
    
    // 2. Crear archivo .htaccess para seguridad
    echo "<h2>🔒 Configurando seguridad...</h2>";
    
    $htaccess_content = '# Configuración de seguridad para apploader
# Permitir solo descarga de archivos específicos
<Files "*.apk">
    Header set Content-Type "application/vnd.android.package-archive"
    Header set Content-Disposition "attachment"
</Files>

<Files "*.ipa">
    Header set Content-Type "application/octet-stream"
    Header set Content-Disposition "attachment"
</Files>

<Files "*.exe">
    Header set Content-Type "application/octet-stream"
    Header set Content-Disposition "attachment"
</Files>

# Denegar acceso a archivos peligrosos
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Denegar listado de directorios
Options -Indexes

# Limitar tamaño de subida
LimitRequestBody 104857600

# Headers de seguridad
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
';
    
    $htaccess_file = $apploader_dir . '/.htaccess';
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        $success_messages[] = "✅ Archivo .htaccess creado para seguridad";
    } else {
        $error_messages[] = "❌ Error creando archivo .htaccess";
    }
    
    // 3. Crear directorio uploads/apps si no existe
    echo "<h2>📂 Verificando directorio uploads...</h2>";
    
    $uploads_dir = __DIR__ . '/uploads/apps';
    if (!is_dir($uploads_dir)) {
        if (mkdir($uploads_dir, 0755, true)) {
            $success_messages[] = "✅ Directorio uploads/apps creado";
        } else {
            $error_messages[] = "❌ Error creando directorio uploads/apps";
        }
    } else {
        $success_messages[] = "✅ Directorio uploads/apps ya existe";
    }
    
    // 4. Verificar permisos
    echo "<h2>🔐 Verificando permisos...</h2>";
    
    if (is_writable($apploader_dir)) {
        $success_messages[] = "✅ Directorio apploader tiene permisos de escritura";
    } else {
        $error_messages[] = "❌ Directorio apploader no tiene permisos de escritura";
    }
    
    if (is_writable($uploads_dir)) {
        $success_messages[] = "✅ Directorio uploads/apps tiene permisos de escritura";
    } else {
        $error_messages[] = "❌ Directorio uploads/apps no tiene permisos de escritura";
    }
    
    // 5. Crear tabla support_apps si no existe
    echo "<h2>🗄️ Configurando base de datos...</h2>";
    
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS support_apps (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                version VARCHAR(50) NOT NULL,
                platform ENUM('android', 'ios', 'windows', 'mac', 'linux', 'universal') NOT NULL,
                file_path VARCHAR(500),
                file_size BIGINT DEFAULT 0,
                features TEXT,
                download_url VARCHAR(500),
                external_url VARCHAR(500),
                status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
                download_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_platform (platform),
                INDEX idx_status (status),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $success_messages[] = "✅ Tabla support_apps creada/verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando tabla: " . $e->getMessage();
    }
    
    // 6. Insertar aplicaciones de ejemplo
    echo "<h2>📱 Insertando aplicaciones de ejemplo...</h2>";
    
    try {
        // Verificar si ya hay aplicaciones
        $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps");
        $app_count = $stmt->fetchColumn();
        
        if ($app_count == 0) {
            $sample_apps = [
                [
                    'name' => 'IPTV Smarters Pro',
                    'description' => 'Aplicación profesional para ver IPTV con interfaz moderna y funciones avanzadas',
                    'version' => '3.0.9',
                    'platform' => 'android',
                    'download_url' => 'https://play.google.com/store/apps/details?id=com.nst.iptvsmartersPro',
                    'external_url' => 'https://www.iptvsmarters.com/',
                    'features' => 'EPG, Catch-up TV, Series, VOD, Multi-idioma',
                    'status' => 'active'
                ],
                [
                    'name' => 'TiviMate IPTV Player',
                    'description' => 'Reproductor IPTV premium con diseño elegante y funciones profesionales',
                    'version' => '4.6.0',
                    'platform' => 'android',
                    'download_url' => 'https://play.google.com/store/apps/details?id=ar.tvplayer.tv',
                    'external_url' => 'https://tivimate.com/',
                    'features' => 'EPG avanzado, Grabación, Múltiples listas, Favoritos',
                    'status' => 'active'
                ],
                [
                    'name' => 'GSE Smart IPTV',
                    'description' => 'Reproductor IPTV gratuito con soporte para múltiples formatos',
                    'version' => '7.6',
                    'platform' => 'ios',
                    'download_url' => 'https://apps.apple.com/app/gse-smart-iptv/id1028734023',
                    'external_url' => 'https://gseiptv.com/',
                    'features' => 'Chromecast, AirPlay, EPG, Subtítulos',
                    'status' => 'active'
                ],
                [
                    'name' => 'VLC Media Player',
                    'description' => 'Reproductor multimedia universal compatible con IPTV',
                    'version' => '3.0.18',
                    'platform' => 'universal',
                    'download_url' => 'https://www.videolan.org/vlc/',
                    'external_url' => 'https://www.videolan.org/',
                    'features' => 'Multiplataforma, Códecs integrados, Streaming',
                    'status' => 'active'
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO support_apps (name, description, version, platform, download_url, external_url, features, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $inserted = 0;
            foreach ($sample_apps as $app) {
                try {
                    $stmt->execute([
                        $app['name'],
                        $app['description'],
                        $app['version'],
                        $app['platform'],
                        $app['download_url'],
                        $app['external_url'],
                        $app['features'],
                        $app['status']
                    ]);
                    $inserted++;
                } catch (Exception $e) {
                    $error_messages[] = "❌ Error insertando app '{$app['name']}': " . $e->getMessage();
                }
            }
            
            $success_messages[] = "✅ Insertadas $inserted aplicaciones de ejemplo";
        } else {
            $success_messages[] = "ℹ️ Ya existen $app_count aplicaciones en la base de datos";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error insertando aplicaciones: " . $e->getMessage();
    }
    
    // 7. Crear archivo index.php en apploader para seguridad
    echo "<h2>🛡️ Creando protección adicional...</h2>";
    
    $index_content = '<?php
// Archivo de protección - No permitir listado de directorio
header("HTTP/1.1 403 Forbidden");
exit("Acceso denegado");
?>';
    
    $index_file = $apploader_dir . '/index.php';
    if (file_put_contents($index_file, $index_content)) {
        $success_messages[] = "✅ Archivo de protección index.php creado";
    } else {
        $error_messages[] = "❌ Error creando archivo de protección";
    }
    
    // 8. Verificar configuración PHP
    echo "<h2>⚙️ Verificando configuración PHP...</h2>";
    
    $upload_max = ini_get('upload_max_filesize');
    $post_max = ini_get('post_max_size');
    $memory_limit = ini_get('memory_limit');
    
    $success_messages[] = "ℹ️ upload_max_filesize: $upload_max";
    $success_messages[] = "ℹ️ post_max_size: $post_max";
    $success_messages[] = "ℹ️ memory_limit: $memory_limit";
    
    // Convertir a bytes para comparar
    function convertToBytes($value) {
        $value = trim($value);
        $last = strtolower($value[strlen($value)-1]);
        $value = (int)$value;
        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }
        return $value;
    }
    
    $upload_bytes = convertToBytes($upload_max);
    $target_bytes = 100 * 1024 * 1024; // 100MB
    
    if ($upload_bytes >= $target_bytes) {
        $success_messages[] = "✅ Configuración PHP permite archivos de 100MB";
    } else {
        $error_messages[] = "⚠️ Configuración PHP limita archivos a $upload_max (recomendado: 100M)";
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Setup Apploader - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Sistema de Aplicaciones Configurado</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Configuración Exitosa</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="apps_admin.php" class="btn">📱 Gestión de Apps</a>
            <a href="user_apps_display.php" class="btn">👥 Ver Apps Usuario</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
        
        <?php if (count($error_messages) == 0): ?>
        <div style="background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; border-radius: 12px; padding: 2rem; margin: 2rem 0; text-align: center;">
            <h2 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Sistema Configurado!</h2>
            <p style="color: #10b981; font-size: 1.1rem;">
                El directorio apploader está configurado y listo para recibir archivos APK.
                Los usuarios podrán descargar las aplicaciones desde el centro de aplicaciones.
            </p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
