<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';

// Procesar activación de código
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['activate_code'])) {
    $activation_code = strtoupper(clean_input($_POST['activation_code']));
    
    // Verificar si el código existe y está disponible
    $stmt = $pdo->prepare("SELECT * FROM activation_codes WHERE code = ? AND status = 'active' AND (expires_at IS NULL OR expires_at > NOW())");
    $stmt->execute([$activation_code]);
    $code = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($code) {
        // Verificar si el usuario ya tiene este código activado
        $stmt = $pdo->prepare("SELECT id FROM user_activations WHERE user_id = ? AND activation_code_id = ?");
        $stmt->execute([$user_id, $code['id']]);
        $existing = $stmt->fetch();
        
        if (!$existing) {
            // Activar el código para el usuario
            $stmt = $pdo->prepare("INSERT INTO user_activations (user_id, activation_code_id, activated_at) VALUES (?, ?, NOW())");
            $stmt->execute([$user_id, $code['id']]);
            
            $success_message = "¡Código activado correctamente! Ya tienes acceso a: " . htmlspecialchars($code['list_name']);
        } else {
            $error_message = "Este código ya está activado en tu cuenta.";
        }
    } else {
        $error_message = "Código inválido, expirado o ya utilizado.";
    }
}

// Obtener activaciones del usuario
$stmt = $pdo->prepare("
    SELECT ua.*, ac.code, ac.list_name, ac.description, ac.expires_at
    FROM user_activations ua
    JOIN activation_codes ac ON ua.activation_code_id = ac.id
    WHERE ua.user_id = ?
    ORDER BY ua.activated_at DESC
");
$stmt->execute([$user_id]);
$activations = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔑 Activar Lista M3U - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .activation-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 3rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .activation-icon {
            font-size: 4rem;
            color: var(--accent-color);
            margin-bottom: 2rem;
        }

        .activation-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .activation-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .activation-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .code-input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .code-input {
            width: 100%;
            padding: 1.5rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 0.2rem;
            transition: var(--transition);
        }

        .code-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.15);
        }

        .code-input::placeholder {
            color: var(--text-secondary);
            text-transform: none;
            letter-spacing: normal;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            width: 100%;
            justify-content: center;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .message {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .activations-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .section-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .activations-list {
            padding: 1.5rem;
        }

        .activation-item {
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--accent-color);
            transition: var(--transition);
        }

        .activation-item:hover {
            background: rgba(255,255,255,0.08);
        }

        .activation-item:last-child {
            margin-bottom: 0;
        }

        .activation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .activation-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .activation-code {
            font-family: 'Courier New', monospace;
            background: var(--dark-bg);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            color: var(--accent-color);
            font-weight: 600;
            letter-spacing: 0.1rem;
        }

        .activation-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .activation-description {
            color: var(--text-secondary);
            margin-top: 1rem;
            line-height: 1.6;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .info-box {
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid rgba(37, 99, 235, 0.3);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        .info-box h3 {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-box ul {
            list-style: none;
            padding: 0;
        }

        .info-box li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-box li::before {
            content: '•';
            color: var(--primary-color);
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .activation-section {
                padding: 2rem;
            }

            .activation-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .code-input {
                font-size: 1rem;
                letter-spacing: 0.1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_activation.php" class="logo">
                <i class="fas fa-key"></i>
                <span>Activar Lista</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-key" style="color: var(--accent-color);"></i>
                Activar Lista M3U
            </h1>
            <p class="page-subtitle">
                Activa tu código para acceder a listas premium de canales IPTV
            </p>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message message">
            <i class="fas fa-check-circle"></i>
            <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="error-message message">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <div class="info-box">
            <h3>
                <i class="fas fa-info-circle"></i>
                Información sobre Códigos de Activación
            </h3>
            <ul>
                <li>Los códigos son únicos y de un solo uso por cuenta</li>
                <li>Algunos códigos pueden tener fecha de expiración</li>
                <li>Una vez activado, tendrás acceso inmediato al contenido</li>
                <li>Los códigos son sensibles a mayúsculas y minúsculas</li>
                <li>Contacta soporte si tienes problemas con tu código</li>
            </ul>
        </div>

        <!-- Sección de Activación -->
        <div class="activation-section">
            <div class="activation-icon">
                <i class="fas fa-unlock-alt"></i>
            </div>
            <h2 class="activation-title">Activar Código</h2>
            <p class="activation-description">
                Ingresa tu código de activación para desbloquear acceso a listas premium de canales IPTV
            </p>
            
            <form method="POST" class="activation-form">
                <div class="code-input-group">
                    <input 
                        type="text" 
                        name="activation_code" 
                        class="code-input" 
                        placeholder="Ingresa tu código"
                        required
                        maxlength="20"
                        pattern="[A-Za-z0-9]+"
                        title="Solo letras y números"
                    >
                </div>
                
                <button type="submit" name="activate_code" class="btn btn-primary">
                    <i class="fas fa-unlock"></i>
                    Activar Código
                </button>
            </form>
        </div>

        <!-- Lista de Activaciones -->
        <div class="activations-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    Mis Activaciones (<?php echo count($activations); ?>)
                </h2>
            </div>
            
            <div class="activations-list">
                <?php if (empty($activations)): ?>
                <div class="empty-state">
                    <i class="fas fa-key"></i>
                    <h3>No tienes códigos activados</h3>
                    <p>Cuando actives tu primer código, aparecerá aquí</p>
                </div>
                <?php else: ?>
                    <?php foreach ($activations as $activation): ?>
                    <div class="activation-item">
                        <div class="activation-header">
                            <div>
                                <div class="activation-name">
                                    <?php echo htmlspecialchars($activation['list_name']); ?>
                                </div>
                                <div class="activation-meta">
                                    <span><i class="fas fa-calendar"></i> Activado: <?php echo date('d/m/Y H:i', strtotime($activation['activated_at'])); ?></span>
                                    <?php if ($activation['expires_at']): ?>
                                    <span>
                                        <i class="fas fa-clock"></i> 
                                        <?php 
                                        $expires = strtotime($activation['expires_at']);
                                        if ($expires > time()) {
                                            $days_left = ceil(($expires - time()) / 86400);
                                            echo "Expira en $days_left día(s)";
                                        } else {
                                            echo "Expirado";
                                        }
                                        ?>
                                    </span>
                                    <?php else: ?>
                                    <span><i class="fas fa-infinity"></i> Sin expiración</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="activation-code">
                                <?php echo htmlspecialchars($activation['code']); ?>
                            </div>
                        </div>
                        
                        <?php if ($activation['description']): ?>
                        <div class="activation-description">
                            <?php echo nl2br(htmlspecialchars($activation['description'])); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        // Auto-format del código mientras se escribe
        const codeInput = document.querySelector('.code-input');
        
        codeInput.addEventListener('input', function(e) {
            // Convertir a mayúsculas y remover caracteres no válidos
            let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            
            // Limitar longitud
            if (value.length > 20) {
                value = value.substring(0, 20);
            }
            
            e.target.value = value;
        });

        // Auto-focus en el campo de código
        codeInput.focus();

        // Validación del formulario
        document.querySelector('form').addEventListener('submit', function(e) {
            const code = codeInput.value.trim();
            
            if (code.length < 4) {
                alert('El código debe tener al menos 4 caracteres');
                e.preventDefault();
                return;
            }
            
            if (!/^[A-Z0-9]+$/.test(code)) {
                alert('El código solo puede contener letras y números');
                e.preventDefault();
                return;
            }
        });

        // Animación de entrada para las activaciones
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.activation-item').forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(item);
        });

        // Copiar código al hacer clic
        document.querySelectorAll('.activation-code').forEach(codeElement => {
            codeElement.style.cursor = 'pointer';
            codeElement.title = 'Clic para copiar';
            
            codeElement.addEventListener('click', function() {
                const code = this.textContent;
                navigator.clipboard.writeText(code).then(() => {
                    // Mostrar feedback visual
                    const originalBg = this.style.background;
                    this.style.background = 'var(--success-color)';
                    this.style.color = 'white';
                    
                    setTimeout(() => {
                        this.style.background = originalBg;
                        this.style.color = 'var(--accent-color)';
                    }, 1000);
                });
            });
        });
    </script>
</body>
</html>
