# 🎯 Sistema de Soporte Técnico RGS

Un sistema completo de administración de soporte técnico para servicios IPTV con panel de control moderno y funcionalidades avanzadas.

## ✨ Características Principales

### 🎛️ **Panel de Administración**
- Dashboard con estadísticas en tiempo real
- Diseño responsive y moderno
- Auto-refresh inteligente
- Navegación intuitiva

### 🎫 **Gestión de Tickets**
- Sistema completo de tickets de soporte
- Estados y prioridades configurables
- Asignación automática/manual de agentes
- Historial completo de conversaciones
- Filtros avanzados y búsqueda

### 💬 **Chat en Vivo**
- Interface de chat en tiempo real
- Gestión de sesiones activas
- Indicadores de escritura y estado
- Calificaciones de servicio
- Historial de conversaciones

### 📱 **Gestión de Aplicaciones**
- Subida drag & drop de archivos
- Gestión de versiones
- Categorización por plataforma
- Control de publicación

### ❓ **Centro de Ayuda**
- Editor de contenido completo
- Sistema de categorías
- Artículos destacados
- Contador de visualizaciones
- Sistema de búsqueda

### 📺 **Solicitudes de Canales**
- Gestión de solicitudes de usuarios
- Aprobación/rechazo con notas
- Prueba de URLs de canales
- Categorización por país/idioma

### 🔑 **Activación de Listas M3U**
- Generación masiva de códigos
- Control de expiración
- Gestión de listas
- Extensión de períodos

### 💚 **Monitoreo del Sistema**
- Estado de servicios en tiempo real
- Métricas del servidor
- Verificación de conectividad
- Alertas automáticas

### 🔔 **Sistema de Notificaciones**
- Notificaciones globales
- Tipos y prioridades configurables
- Dirigidas a usuarios específicos
- Control de expiración

### 🛡️ **Panel de Seguridad**
- Logs de seguridad detallados
- Monitoreo de IPs sospechosas
- Acciones de bloqueo
- Análisis de patrones

## 🚀 Instalación Rápida

### Opción 1: Instalación Automática (Recomendada)

1. **Subir archivos al servidor**
   ```bash
   # Subir todos los archivos a tu directorio web
   ```

2. **Acceder al instalador**
   ```
   http://tu-dominio.com/setup.php
   ```

3. **Seguir el asistente de instalación**
   - Configurar base de datos
   - Crear tablas
   - Configurar sistema
   - ¡Listo!

### Opción 2: Instalación Manual

1. **Crear base de datos MySQL**
   ```sql
   CREATE DATABASE rgs_support_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Importar estructura de tablas**
   ```bash
   mysql -u usuario -p rgs_support_system < support_system_tables.sql
   ```

3. **Configurar config.php**
   ```php
   $db_host = 'localhost';
   $db_name = 'rgs_support_system';
   $db_user = 'tu_usuario';
   $db_pass = 'tu_contraseña';
   ```

4. **Configurar permisos**
   ```bash
   chmod 755 uploads/
   chmod 755 uploads/apps/
   chmod 755 uploads/tickets/
   ```

## 🔐 Acceso al Sistema

### Credenciales por Defecto
- **Usuario:** `admin`
- **Contraseña:** `admin123`

### URLs Principales
- **Panel Principal:** `admin2.php`
- **Login:** `admin_login.php`
- **Estado del Sistema:** `system_status.php`

## 📋 Requisitos del Sistema

### Servidor Web
- **PHP:** 7.4 o superior
- **MySQL:** 5.7 o superior
- **Apache/Nginx** con mod_rewrite

### Extensiones PHP Requeridas
- PDO y PDO_MySQL
- cURL
- JSON
- Session
- GD (opcional, para manipulación de imágenes)

### Configuración PHP Recomendada
```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 50M
post_max_size = 50M
```

## 📁 Estructura de Archivos

```
📦 Sistema de Soporte RGS
├── 📄 admin2.php                 # Panel principal
├── 📄 admin_login.php            # Login de administrador
├── 📄 config.php                 # Configuración del sistema
├── 📄 setup.php                  # Instalador automático
├── 📄 support_system_tables.sql  # Estructura de base de datos
├── 🎫 Gestión de Tickets
│   ├── 📄 tickets_admin.php      # Lista de tickets
│   └── 📄 ticket_detail.php      # Detalle de ticket
├── 💬 Chat en Vivo
│   ├── 📄 chat_admin.php         # Gestión de chat
│   ├── 📄 chat_detail.php        # Vista de conversación
│   └── 📄 api_chat_stats.php     # API de estadísticas
├── 📱 Aplicaciones
│   └── 📄 apps_admin.php         # Gestión de apps
├── ❓ Centro de Ayuda
│   └── 📄 help_admin.php         # Gestión de contenido
├── 📺 Canales
│   └── 📄 channels_admin.php     # Solicitudes de canales
├── 🔑 Activaciones
│   └── 📄 activations_admin.php  # Códigos de activación
├── 💚 Sistema
│   ├── 📄 system_status.php      # Estado del sistema
│   ├── 📄 notifications_admin.php # Notificaciones
│   ├── 📄 security_admin.php     # Panel de seguridad
│   └── 📄 api_support_stats.php  # API de estadísticas
└── 📁 uploads/                   # Archivos subidos
    ├── 📁 apps/                  # Aplicaciones
    ├── 📁 tickets/               # Archivos de tickets
    └── 📁 temp/                  # Archivos temporales
```

## 🎨 Características del Diseño

### Paleta de Colores
- **Primario:** #2563eb (Azul)
- **Secundario:** #1e293b (Gris oscuro)
- **Acento:** #10b981 (Verde)
- **Soporte:** #e91e63 (Rosa)
- **Advertencia:** #f59e0b (Amarillo)
- **Error:** #ef4444 (Rojo)

### Responsive Design
- ✅ **Mobile First** - Optimizado para móviles
- ✅ **Tablet Friendly** - Adaptado para tablets
- ✅ **Desktop Enhanced** - Experiencia completa en escritorio

### Animaciones y Transiciones
- ✅ Transiciones suaves
- ✅ Animaciones de carga
- ✅ Efectos hover
- ✅ Indicadores de estado

## 🔧 Configuración Avanzada

### Variables de Entorno
```php
// En config.php
define('ENVIRONMENT', 'production'); // o 'development'
define('DEBUG_MODE', false);
define('LOG_LEVEL', 'error');
```

### Configuración de Seguridad
```php
// Headers de seguridad
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// HTTPS (recomendado en producción)
ini_set('session.cookie_secure', 1);
```

### Configuración de Email
```php
// Para notificaciones por email
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USER', '<EMAIL>');
define('SMTP_PASS', 'tu-contraseña');
```

## 📊 APIs Disponibles

### Estadísticas de Soporte
```
GET /api_support_stats.php
```

### Estadísticas de Chat
```
GET /api_chat_stats.php
```

### Respuesta JSON Ejemplo
```json
{
  "tickets": {
    "total": 150,
    "open": 25,
    "in_progress": 10,
    "resolved": 115
  },
  "chat": {
    "active_sessions": 5,
    "total_today": 23,
    "avg_response_time": "2.5 min"
  }
}
```

## 🛠️ Personalización

### Cambiar Logo y Colores
```css
/* En los archivos CSS */
:root {
  --primary-color: #tu-color;
  --support-color: #tu-color;
}
```

### Agregar Nuevas Funcionalidades
1. Crear nuevo archivo PHP
2. Incluir `config.php`
3. Verificar autenticación
4. Seguir estructura existente

## 🔒 Seguridad

### Medidas Implementadas
- ✅ **Validación de entrada** en todos los formularios
- ✅ **Protección CSRF** en acciones críticas
- ✅ **Sanitización** de datos de salida
- ✅ **Rate limiting** para prevenir ataques
- ✅ **Logs de seguridad** detallados
- ✅ **Sesiones seguras** con timeout

### Recomendaciones Adicionales
- 🔐 Cambiar credenciales por defecto
- 🔐 Usar HTTPS en producción
- 🔐 Configurar firewall
- 🔐 Mantener PHP y MySQL actualizados
- 🔐 Realizar backups regulares

## 🐛 Solución de Problemas

### Error de Conexión a BD
```
Error: SQLSTATE[HY000] [1045] Access denied
```
**Solución:** Verificar credenciales en `config.php`

### Error de Permisos
```
Error: Permission denied
```
**Solución:** 
```bash
chmod 755 uploads/
chown www-data:www-data uploads/
```

### Página en Blanco
**Solución:** Activar debug en `config.php`
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## 📞 Soporte

### Documentación
- Código bien comentado
- Estructura clara y modular
- Funciones auxiliares documentadas

### Logs del Sistema
- **Actividad:** Tabla `activity_logs`
- **Seguridad:** Tabla `security_logs`
- **Errores:** Logs de PHP

## 📈 Rendimiento

### Optimizaciones Incluidas
- ✅ Consultas SQL optimizadas
- ✅ Índices en tablas principales
- ✅ Cache de estadísticas
- ✅ Compresión de archivos
- ✅ Lazy loading de imágenes

### Métricas de Rendimiento
- **Tiempo de carga:** < 2 segundos
- **Consultas por página:** < 10
- **Uso de memoria:** < 64MB por request

## 🎯 Próximas Funcionalidades

### En Desarrollo
- [ ] API REST completa
- [ ] Integración con WhatsApp
- [ ] Dashboard para usuarios finales
- [ ] Sistema de reportes avanzado
- [ ] Integración con sistemas de pago

### Sugerencias de Mejora
- [ ] Notificaciones push
- [ ] Chat con WebSockets
- [ ] Sistema de archivos en la nube
- [ ] Integración con CRM
- [ ] App móvil nativa

---

## 📄 Licencia

Este sistema está desarrollado para uso interno de RGS. Todos los derechos reservados.

## 👨‍💻 Desarrollado por

**RGS Development Team**  
Sistema de Soporte Técnico v1.0  
© 2024 RGS. Todos los derechos reservados.

---

¿Necesitas ayuda? Contacta al equipo de desarrollo. 🚀
