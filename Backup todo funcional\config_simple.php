<?php
// Configuración simplificada para evitar errores 500
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configuración de la base de datos
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

// Configuración del sistema
define('SITE_URL', 'http://localhost');
define('ADMIN_EMAIL', '<EMAIL>');
define('SYSTEM_NAME', 'RGS Support System');
define('UPLOAD_PATH', 'uploads/');

// Configuración de sesión
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);

// Conexión a la base de datos con manejo de errores
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // Mostrar error detallado para debug
    die("Error de conexión: " . $e->getMessage() . "<br><br>
         <strong>Posibles soluciones:</strong><br>
         1. Verificar que MySQL esté ejecutándose<br>
         2. Crear la base de datos: <a href='quick_setup.php'>Configuración Rápida</a><br>
         3. Verificar credenciales en config.php<br>");
}

// Función para limpiar datos de entrada
function clean_input($data) {
    if (!is_string($data)) return $data;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Función para formatear fechas
function format_date($date, $format = 'd/m/Y H:i') {
    if (!$date) return '';
    return date($format, strtotime($date));
}

// Función para formatear tamaños de archivo
function format_file_size($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Función para registrar actividad (simplificada)
function log_activity($pdo, $user_id, $action, $details = null) {
    try {
        // Verificar si la tabla existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                $user_id,
                $action,
                $details ? json_encode($details) : null,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
        }
    } catch (Exception $e) {
        // Silenciar errores de log para no interrumpir la aplicación
        error_log("Error logging activity: " . $e->getMessage());
    }
}

// Función para obtener IP real del usuario
function get_real_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}

// Función para verificar rate limiting básico (simplificada)
function check_rate_limit($identifier, $max_attempts = 5, $time_window = 300) {
    // Para demo, siempre permitir
    return true;
}

// Función para verificar si es una solicitud AJAX
function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// Función para respuesta JSON
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Función para verificar si el usuario es admin
function is_admin() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// Función para redirigir
function redirect($url) {
    header("Location: $url");
    exit;
}

// Configuración de zona horaria
date_default_timezone_set('Europe/Madrid');

// Crear directorio de uploads si no existe
if (!file_exists(UPLOAD_PATH)) {
    @mkdir(UPLOAD_PATH, 0755, true);
}

// Crear subdirectorios necesarios
$subdirs = ['apps', 'tickets', 'temp', 'avatars'];
foreach ($subdirs as $subdir) {
    $path = UPLOAD_PATH . $subdir;
    if (!file_exists($path)) {
        @mkdir($path, 0755, true);
    }
}

// Función para verificar si las tablas existen
function check_database_setup($pdo) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'support_tickets'");
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Función para crear usuario demo si no existe
function ensure_demo_user($pdo) {
    try {
        // Verificar si la tabla users existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'usuario'");
            $stmt->execute();
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash) VALUES ('usuario', '<EMAIL>', 'demo_hash')");
                $stmt->execute();
            }
        }
    } catch (Exception $e) {
        // Tabla users no existe, se creará con el SQL
        error_log("Error ensuring demo user: " . $e->getMessage());
    }
}

// Verificar configuración inicial
if (!check_database_setup($pdo)) {
    // Si no hay tablas, mostrar mensaje de ayuda
    if (basename($_SERVER['PHP_SELF']) !== 'quick_setup.php' && 
        basename($_SERVER['PHP_SELF']) !== 'debug.php' && 
        basename($_SERVER['PHP_SELF']) !== 'setup.php') {
        
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
        echo "<h3>⚠️ Base de datos no configurada</h3>";
        echo "<p>Las tablas del sistema no están creadas. Por favor:</p>";
        echo "<ol>";
        echo "<li><a href='quick_setup.php'><strong>Ejecutar Configuración Rápida</strong></a> (Recomendado)</li>";
        echo "<li><a href='setup.php'>Usar el Instalador Completo</a></li>";
        echo "<li><a href='debug.php'>Ejecutar Diagnóstico</a></li>";
        echo "</ol>";
        echo "</div>";
        
        // No detener la ejecución para permitir acceso a las herramientas de setup
    }
} else {
    // Asegurar que existe el usuario demo
    ensure_demo_user($pdo);
}

// Constantes para estados
define('TICKET_STATUS_OPEN', 'open');
define('TICKET_STATUS_IN_PROGRESS', 'in_progress');
define('TICKET_STATUS_RESOLVED', 'resolved');
define('TICKET_STATUS_CLOSED', 'closed');

define('TICKET_PRIORITY_LOW', 'low');
define('TICKET_PRIORITY_MEDIUM', 'medium');
define('TICKET_PRIORITY_HIGH', 'high');
define('TICKET_PRIORITY_URGENT', 'urgent');

define('CHAT_STATUS_WAITING', 'waiting');
define('CHAT_STATUS_ACTIVE', 'active');
define('CHAT_STATUS_ENDED', 'ended');

// Configuración de notificaciones
define('NOTIFICATION_TYPE_INFO', 'info');
define('NOTIFICATION_TYPE_WARNING', 'warning');
define('NOTIFICATION_TYPE_ERROR', 'error');
define('NOTIFICATION_TYPE_SUCCESS', 'success');
?>
