<?php
// API para análisis M3U con respuesta JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(300);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Error de conexión: ' . $e->getMessage()]);
    exit;
}

// Función para analizar M3U
function analyzeM3uContent($url, $username = null, $password = null) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    if ($username && $password) {
        $auth = base64_encode("$username:$password");
        $context = stream_context_create([
            'http' => [
                'header' => "Authorization: Basic $auth\r\n",
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
    }
    
    $content = @file_get_contents($url, false, $context);
    
    if ($content === false) {
        $error = error_get_last();
        throw new Exception("No se pudo descargar la lista M3U: " . ($error['message'] ?? 'Error desconocido'));
    }
    
    if (strlen($content) < 10) {
        throw new Exception("El archivo descargado está vacío o es muy pequeño");
    }
    
    // Verificar que sea un archivo M3U válido
    if (strpos($content, '#EXTM3U') === false && strpos($content, '#EXTINF') === false) {
        throw new Exception("El archivo no parece ser un M3U válido");
    }
    
    $lines = explode("\n", $content);
    $items = [];
    $current_item = null;
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        if (strpos($line, '#EXTINF:') === 0) {
            $current_item = [];
            
            // Extraer título
            if (preg_match('/#EXTINF:[^,]*,(.*)/', $line, $matches)) {
                $current_item['title'] = trim($matches[1]);
            } else {
                $current_item['title'] = 'Sin título';
            }
            
            // Extraer duración
            if (preg_match('/#EXTINF:([^,]+)/', $line, $matches)) {
                $current_item['duration'] = (float)$matches[1];
            }
            
        } elseif ($line && !empty($current_item) && strpos($line, '#') !== 0) {
            // Esta es la URL del stream
            $current_item['url'] = $line;
            
            if (!empty($current_item['title'])) {
                $title = $current_item['title'];
                $clean_title = strtolower(preg_replace('/[^\w\s]/', '', $title));
                
                // Detectar tipo
                $media_type = 'unknown';
                if (preg_match('/s\d+e\d+|season|temporada|\d+x\d+/i', $title)) {
                    $media_type = 'tv';
                } elseif (preg_match('/\(\d{4}\)|\d{4}|movie|film|película/i', $title)) {
                    $media_type = 'movie';
                }
                
                // Extraer año
                $year = null;
                if (preg_match('/\((\d{4})\)/', $title, $matches)) {
                    $year = (int)$matches[1];
                } elseif (preg_match('/(\d{4})/', $title, $matches)) {
                    $test_year = (int)$matches[1];
                    if ($test_year >= 1900 && $test_year <= date('Y') + 2) {
                        $year = $test_year;
                    }
                }
                
                $items[] = [
                    'title' => $title,
                    'clean_title' => $clean_title,
                    'url' => $current_item['url'],
                    'media_type' => $media_type,
                    'year' => $year,
                    'season' => null,
                    'episode' => null,
                    'duration' => $current_item['duration'] ?? null
                ];
            }
            
            $current_item = null;
        }
    }
    
    return [
        'items' => $items,
        'total_lines' => count($lines),
        'total_items' => count($items)
    ];
}

// Procesar solicitud
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'analyze') {
        $list_id = $_POST['list_id'] ?? null;
        
        if (!$list_id) {
            echo json_encode(['success' => false, 'message' => 'ID de lista requerido']);
            exit;
        }
        
        try {
            // Obtener datos de la lista
            $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
            $stmt->execute([$list_id]);
            $list = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$list) {
                echo json_encode(['success' => false, 'message' => 'Lista no encontrada']);
                exit;
            }
            
            // Analizar contenido
            $analysis = analyzeM3uContent($list['url'], $list['username'], $list['password']);
            $items = $analysis['items'];
            
            // Limpiar contenido anterior
            $stmt = $pdo->prepare("DELETE FROM m3u_content WHERE list_id = ?");
            $stmt->execute([$list_id]);
            
            // Insertar nuevo contenido
            $stmt = $pdo->prepare("
                INSERT INTO m3u_content 
                (list_id, title, clean_title, media_type, year, season, episode, url, duration) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $inserted = 0;
            foreach ($items as $item) {
                $stmt->execute([
                    $list_id,
                    $item['title'],
                    $item['clean_title'],
                    $item['media_type'],
                    $item['year'],
                    $item['season'],
                    $item['episode'],
                    $item['url'],
                    $item['duration']
                ]);
                $inserted++;
            }
            
            // Actualizar estadísticas
            $stmt = $pdo->prepare("
                UPDATE m3u_lists 
                SET last_scan = NOW(), total_items = ?, last_updated = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$inserted, $list_id]);
            
            // Calcular estadísticas
            $movies = count(array_filter($items, fn($i) => $i['media_type'] === 'movie'));
            $tv_shows = count(array_filter($items, fn($i) => $i['media_type'] === 'tv'));
            $unknown = count(array_filter($items, fn($i) => $i['media_type'] === 'unknown'));
            $with_year = count(array_filter($items, fn($i) => $i['year'] !== null));
            
            echo json_encode([
                'success' => true,
                'message' => 'Análisis completado exitosamente',
                'total_lines' => $analysis['total_lines'],
                'total_items' => $inserted,
                'movies' => $movies,
                'tv_shows' => $tv_shows,
                'unknown' => $unknown,
                'with_year' => $with_year,
                'list_name' => $list['name']
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error durante el análisis: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Acción no válida']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
}
?>
