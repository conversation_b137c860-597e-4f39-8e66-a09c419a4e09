<?php
// Script de diagnóstico para verificar acceso
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

echo "<h1>🔍 Diagnóstico de Acceso</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; } .success { color: #28a745; } .error { color: #dc3545; } .info { color: #17a2b8; } .warning { color: #ffc107; } .box { background: #222; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007bff; }</style>";

echo "<div class='box'>";
echo "<h2>📍 Información del Servidor</h2>";
echo "<p><strong>Directorio actual:</strong> " . __DIR__ . "</p>";
echo "<p><strong>URL actual:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Servidor:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Documento raíz:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "</div>";

echo "<div class='box'>";
echo "<h2>🔐 Estado de Sesión</h2>";
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    echo "<p class='success'>✅ Sesión de admin activa</p>";
    echo "<p><strong>Usuario:</strong> " . ($_SESSION['admin_username'] ?? 'N/A') . "</p>";
} else {
    echo "<p class='error'>❌ No hay sesión de admin activa</p>";
    echo "<p><a href='admin_login.php' style='color: #ffc107;'>🔐 Hacer login como admin</a></p>";
}
echo "</div>";

echo "<div class='box'>";
echo "<h2>📁 Archivos del Sistema M3U</h2>";
$m3u_files = [
    'setup_m3u_system.php' => 'Configuración inicial',
    'm3u_manager.php' => 'Gestor de listas',
    'm3u_analyzer.php' => 'Analizador de contenido'
];

foreach ($m3u_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $file - $description</p>";
        echo "<p style='margin-left: 20px;'><a href='$file' style='color: #28a745;'>🔗 Acceder a $file</a></p>";
    } else {
        echo "<p class='error'>❌ $file - No encontrado</p>";
    }
}
echo "</div>";

echo "<div class='box'>";
echo "<h2>🌐 URLs de Acceso</h2>";
$base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
echo "<p><strong>URL base:</strong> $base_url</p>";
echo "<ul>";
echo "<li><a href='{$base_url}/setup_m3u_system.php' style='color: #28a745;'>Setup Sistema M3U</a></li>";
echo "<li><a href='{$base_url}/m3u_manager.php' style='color: #17a2b8;'>Gestor M3U</a></li>";
echo "<li><a href='{$base_url}/m3u_analyzer.php' style='color: #ffc107;'>Analizador M3U</a></li>";
echo "<li><a href='{$base_url}/admin.php' style='color: #dc3545;'>Panel Admin</a></li>";
echo "</ul>";
echo "</div>";

// Verificar conexión a BD
echo "<div class='box'>";
echo "<h2>🗄️ Conexión a Base de Datos</h2>";
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión a BD exitosa</p>";
    
    // Verificar si las tablas M3U existen
    $tables = ['m3u_lists', 'm3u_content', 'content_matches'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p class='success'>✅ Tabla '$table' existe ($count registros)</p>";
        } catch (PDOException $e) {
            echo "<p class='warning'>⚠️ Tabla '$table' no existe - Necesita configuración</p>";
        }
    }
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error de BD: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='box'>";
echo "<h2>🚀 Acciones Recomendadas</h2>";
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo "<p class='warning'>1. <a href='admin_login.php' style='color: #ffc107;'>Hacer login como admin</a></p>";
}
echo "<p class='info'>2. <a href='setup_m3u_system.php' style='color: #17a2b8;'>Configurar sistema M3U</a></p>";
echo "<p class='info'>3. <a href='m3u_manager.php' style='color: #17a2b8;'>Gestionar listas M3U</a></p>";
echo "<p class='info'>4. <a href='admin.php' style='color: #17a2b8;'>Volver al panel admin</a></p>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "Diagnóstico ejecutado el " . date('Y-m-d H:i:s');
echo "</p>";
?>
