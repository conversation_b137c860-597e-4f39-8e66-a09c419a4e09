<?php
session_start();
require_once 'geo_restriction.php';
require_once 'user_geo_display.php';

// Activar modo test si se especifica
if (isset($_GET['test_country'])) {
    $_SESSION['geo_bypass'] = strtoupper($_GET['test_country']);
}

// Obtener información del usuario
$geo_info = getUserGeoInfo();
$access = checkGeoAccess();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌍 Test Sistema Geolocalización - RGS</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --border-color: #334155;
            --border-radius: 12px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }

        .status-card {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid;
        }

        .status-allowed {
            border-color: var(--success-color);
        }

        .status-blocked {
            border-color: var(--error-color);
        }

        .geo-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .info-item {
            background: var(--dark-bg);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .info-label {
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .info-value {
            font-size: 1.1rem;
            color: var(--text-primary);
        }

        .test-controls {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 2rem 0;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .component-demo {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 2rem 0;
        }

        .demo-item {
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(37, 99, 235, 0.1);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .countries-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .country-item {
            padding: 0.5rem;
            background: rgba(16, 185, 129, 0.1);
            border-radius: 6px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 Test Sistema de Geolocalización</h1>

        <!-- Estado Actual -->
        <div class="status-card <?php echo $access['allowed'] ? 'status-allowed' : 'status-blocked'; ?>">
            <h2 style="margin: 0 0 1rem 0; color: <?php echo $access['allowed'] ? 'var(--success-color)' : 'var(--error-color)'; ?>">
                <?php if ($access['allowed']): ?>
                    ✅ Acceso Permitido
                <?php else: ?>
                    🚫 Acceso Bloqueado
                <?php endif; ?>
            </h2>
            <p style="margin: 0; font-size: 1.1rem;">
                <?php echo $geo_info['flag']; ?> 
                Detectado desde: <strong><?php echo htmlspecialchars($geo_info['country_name']); ?></strong>
                (<?php echo $geo_info['country_code']; ?>)
            </p>
        </div>

        <!-- Información Detallada -->
        <div class="geo-info">
            <div class="info-item">
                <div class="info-label">País Detectado</div>
                <div class="info-value"><?php echo $geo_info['flag']; ?> <?php echo htmlspecialchars($geo_info['country_name']); ?></div>
            </div>
            <div class="info-item">
                <div class="info-label">Código de País</div>
                <div class="info-value"><?php echo $geo_info['country_code']; ?></div>
            </div>
            <div class="info-item">
                <div class="info-label">Dirección IP</div>
                <div class="info-value"><?php echo htmlspecialchars($geo_info['ip']); ?></div>
            </div>
            <div class="info-item">
                <div class="info-label">Estado de Acceso</div>
                <div class="info-value" style="color: <?php echo $access['allowed'] ? 'var(--success-color)' : 'var(--error-color)'; ?>">
                    <?php echo $access['allowed'] ? 'Permitido' : 'Bloqueado'; ?>
                </div>
            </div>
        </div>

        <!-- Controles de Prueba -->
        <div class="test-controls">
            <h3 style="color: var(--primary-color); margin-bottom: 1rem;">🧪 Controles de Prueba</h3>
            <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                Simula diferentes países para probar el sistema de restricción:
            </p>
            
            <div class="test-buttons">
                <!-- Países Permitidos -->
                <a href="?test_country=US" class="btn btn-success">🇺🇸 Estados Unidos</a>
                <a href="?test_country=MX" class="btn btn-success">🇲🇽 México</a>
                <a href="?test_country=CO" class="btn btn-success">🇨🇴 Colombia</a>
                <a href="?test_country=AR" class="btn btn-success">🇦🇷 Argentina</a>
                <a href="?test_country=BR" class="btn btn-success">🇧🇷 Brasil</a>
                <a href="?test_country=CL" class="btn btn-success">🇨🇱 Chile</a>
                
                <!-- Países Bloqueados -->
                <a href="?test_country=ES" class="btn btn-danger">🇪🇸 España (Bloqueado)</a>
                <a href="?test_country=FR" class="btn btn-danger">🇫🇷 Francia (Bloqueado)</a>
                <a href="?test_country=DE" class="btn btn-danger">🇩🇪 Alemania (Bloqueado)</a>
                <a href="?test_country=CN" class="btn btn-danger">🇨🇳 China (Bloqueado)</a>
                
                <!-- Reset -->
                <a href="test_geo_system.php" class="btn btn-warning">🔄 Reset (IP Real)</a>
            </div>
        </div>

        <!-- Demo de Componentes -->
        <div class="component-demo">
            <h3 style="color: var(--primary-color); margin-bottom: 1rem;">🎨 Demo de Componentes</h3>
            
            <div class="demo-item">
                <strong>Componente Completo con Tooltip:</strong><br>
                <?php echo renderUserGeoComponent('Usuario Demo', true); ?>
            </div>
            
            <div class="demo-item">
                <strong>Solo Bandera:</strong><br>
                <?php echo renderSimpleGeoFlag(); ?> Usuario con bandera simple
            </div>
            
            <div class="demo-item">
                <strong>Estilo Header:</strong><br>
                <?php echo renderUserGeoFlag(true, 'header'); ?>
            </div>
            
            <div class="demo-item">
                <strong>Estilo Sidebar:</strong><br>
                <?php echo renderUserGeoFlag(true, 'sidebar'); ?>
            </div>
        </div>

        <!-- Países Permitidos -->
        <div class="test-controls">
            <h3 style="color: var(--success-color); margin-bottom: 1rem;">✅ Países con Acceso Permitido</h3>
            <div class="countries-list">
                <?php 
                $allowed_countries = GeoRestriction::getAllowedCountriesList();
                foreach ($allowed_countries as $code => $name): 
                ?>
                <div class="country-item"><?php echo htmlspecialchars($name); ?></div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Acciones -->
        <div style="text-align: center; margin-top: 2rem;">
            <a href="index.php" class="btn btn-primary">🏠 Ir al Index</a>
            <a href="geo_blocked.php?country=ES" class="btn btn-danger">🚫 Ver Página Bloqueada</a>
            <a href="admin2.php" class="btn btn-warning">⚙️ Panel Admin</a>
        </div>

        <!-- Información Técnica -->
        <div style="margin-top: 2rem; padding: 1rem; background: var(--dark-bg); border-radius: 8px; font-family: monospace; font-size: 0.9rem;">
            <strong>Información Técnica:</strong><br>
            User Agent: <?php echo htmlspecialchars($_SERVER['HTTP_USER_AGENT'] ?? 'N/A'); ?><br>
            Headers CF: <?php echo isset($_SERVER['HTTP_CF_IPCOUNTRY']) ? $_SERVER['HTTP_CF_IPCOUNTRY'] : 'No disponible'; ?><br>
            Modo Test: <?php echo GeoRestriction::isTestMode() ? 'Activo (' . GeoRestriction::getTestCountry() . ')' : 'Inactivo'; ?><br>
            Timestamp: <?php echo date('Y-m-d H:i:s'); ?>
        </div>
    </div>

    <?php echo renderGeoJavaScript(); ?>

    <script>
        // Log adicional para debugging
        console.log('🌍 Geo Test System Loaded');
        console.log('Current Country:', window.userGeoInfo);
        
        // Mostrar notificación del estado
        document.addEventListener('DOMContentLoaded', function() {
            const allowed = <?php echo $access['allowed'] ? 'true' : 'false'; ?>;
            const country = '<?php echo $geo_info['country_name']; ?>';
            const flag = '<?php echo $geo_info['flag']; ?>';
            
            console.log(`${flag} Acceso desde ${country}: ${allowed ? 'PERMITIDO' : 'BLOQUEADO'}`);
        });
    </script>
</body>
</html>
