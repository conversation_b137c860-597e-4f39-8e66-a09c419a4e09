<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🚀 Configuración Completa del Sistema de Ayuda</h1>";

try {
    echo "<h2>Paso 1: Corrigiendo estructura de base de datos...</h2>";
    
    // Ejecutar corrección de estructura
    ob_start();
    include 'fix_help_table_structure.php';
    $structure_output = ob_get_clean();
    
    // Verificar que la tabla esté correcta
    $stmt = $pdo->query("DESCRIBE help_articles");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = ['id', 'title', 'content', 'excerpt', 'category', 'subcategory', 'status', 'is_featured', 'view_count'];
    $missing_columns = array_diff($required_columns, $columns);
    
    if (empty($missing_columns)) {
        $success_messages[] = "✅ Estructura de base de datos correcta";
    } else {
        $error_messages[] = "❌ Columnas faltantes: " . implode(', ', $missing_columns);
    }
    
    echo "<h2>Paso 2: Verificando archivos del sistema...</h2>";
    
    $required_files = [
        'user_help.php' => 'Centro de Ayuda para Usuarios',
        'help_admin.php' => 'Panel de Administración de Ayuda',
        'api_help_system.php' => 'API del Sistema de Ayuda'
    ];
    
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            $success_messages[] = "✅ $description disponible";
        } else {
            $error_messages[] = "❌ $description no encontrado";
        }
    }
    
    echo "<h2>Paso 3: Probando API...</h2>";
    
    // Probar endpoints principales de la API
    $api_tests = [
        'get_articles' => 'Obtener artículos',
        'get_categories' => 'Obtener categorías',
        'get_featured' => 'Obtener destacados'
    ];
    
    foreach ($api_tests as $action => $description) {
        try {
            $response = @file_get_contents("api_help_system.php?action=$action");
            if ($response) {
                $data = json_decode($response, true);
                if ($data && $data['success']) {
                    $success_messages[] = "✅ API $description: OK";
                } else {
                    $error_messages[] = "❌ API $description: Error en respuesta";
                }
            } else {
                $error_messages[] = "❌ API $description: No responde";
            }
        } catch (Exception $e) {
            $error_messages[] = "❌ API $description: " . $e->getMessage();
        }
    }
    
    echo "<h2>Paso 4: Verificando contenido...</h2>";
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
            SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured,
            SUM(view_count) as total_views
        FROM help_articles
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($stats['total'] > 0) {
        $success_messages[] = "✅ Sistema tiene {$stats['total']} artículos";
        $success_messages[] = "✅ {$stats['published']} artículos publicados";
        $success_messages[] = "✅ {$stats['featured']} artículos destacados";
        $success_messages[] = "✅ {$stats['total_views']} vistas totales";
    } else {
        $error_messages[] = "❌ No hay artículos en el sistema";
    }
    
    echo "<h2>Paso 5: Verificando integración...</h2>";
    
    // Verificar enlaces en admin2.php
    if (file_exists('admin2.php')) {
        $admin2_content = file_get_contents('admin2.php');
        if (strpos($admin2_content, 'help_admin.php') !== false) {
            $success_messages[] = "✅ help_admin.php enlazado en admin2.php";
        } else {
            $error_messages[] = "❌ help_admin.php NO enlazado en admin2.php";
        }
    }
    
    // Verificar enlaces en index2.php
    if (file_exists('index2.php')) {
        $index2_content = file_get_contents('index2.php');
        if (strpos($index2_content, 'user_help.php') !== false) {
            $success_messages[] = "✅ user_help.php enlazado en index2.php";
        } else {
            $error_messages[] = "❌ user_help.php NO enlazado en index2.php";
        }
    }
    
    echo "<h2>Paso 6: Creando tabla de feedback...</h2>";
    
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS help_feedback (
                id INT AUTO_INCREMENT PRIMARY KEY,
                article_id INT NOT NULL,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                feedback TEXT,
                user_id INT NULL,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (article_id) REFERENCES help_articles(id) ON DELETE CASCADE,
                INDEX idx_article (article_id),
                INDEX idx_rating (rating),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $success_messages[] = "✅ Tabla de feedback creada/verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando tabla feedback: " . $e->getMessage();
    }
    
    echo "<h2>✅ Configuración Completada</h2>";
    
    $total_success = count($success_messages);
    $total_errors = count($error_messages);
    
    if ($total_errors == 0) {
        $success_messages[] = "🎉 ¡Sistema de ayuda configurado completamente!";
        $success_messages[] = "🔗 Comunicación entre user_help.php y help_admin.php establecida";
    } else {
        $error_messages[] = "⚠️ Configuración completada con $total_errors errores";
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Setup Completo Sistema de Ayuda - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .summary {
            background: #0f172a;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat-card {
            background: #334155;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #10b981;
        }
        .stat-label {
            color: #cbd5e1;
            font-size: 0.9rem;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #10b981;
        }
        .btn.success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Sistema de Ayuda Configurado</h1>
        
        <div class="summary">
            <h3>📊 Resumen de Configuración</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($success_messages); ?></div>
                    <div class="stat-label">Operaciones Exitosas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #ef4444;"><?php echo count($error_messages); ?></div>
                    <div class="stat-label">Errores Encontrados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f59e0b;"><?php echo isset($stats) ? $stats['total'] : 0; ?></div>
                    <div class="stat-label">Artículos Disponibles</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #8b5cf6;"><?php echo isset($stats) ? $stats['published'] : 0; ?></div>
                    <div class="stat-label">Artículos Publicados</div>
                </div>
            </div>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Configuración Exitosa</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <h3 style="color: #f8fafc; margin-bottom: 1rem;">🎯 Próximos Pasos</h3>
            <a href="user_help.php" class="btn success">👥 Probar Centro de Ayuda</a>
            <a href="help_admin.php" class="btn success">⚙️ Panel de Administración</a>
            <a href="api_help_system.php?action=get_articles" class="btn">🔌 Probar API</a>
            <a href="test_help_communication.php" class="btn">🧪 Test Comunicación</a>
            <a href="admin2.php" class="btn">🏠 Volver al Dashboard</a>
        </div>
        
        <?php if (count($error_messages) == 0): ?>
        <div style="background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; border-radius: 12px; padding: 2rem; margin: 2rem 0; text-align: center;">
            <h2 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Configuración Completada!</h2>
            <p style="color: #10b981; font-size: 1.1rem;">
                El sistema de ayuda está completamente configurado y la comunicación entre 
                <strong>user_help.php</strong> y <strong>help_admin.php</strong> está establecida.
            </p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
