<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Crear directorios necesarios si no existen
$uploadDir = __DIR__ . '/apploader';
$uploadsDir = __DIR__ . '/uploads/apps';

if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
    // Crear archivo .htaccess para seguridad
    file_put_contents($uploadDir . '/.htaccess', 'Options -Indexes' . PHP_EOL . 'Header set Content-Disposition "attachment"');
}

if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'toggle_status':
                $app_id = (int)$_POST['app_id'];
                $stmt = $pdo->prepare("UPDATE support_apps SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$app_id]);
                $success_message = "Estado de la aplicación actualizado";
                break;
                
            case 'delete_app':
                $app_id = (int)$_POST['app_id'];
                
                // Obtener información del archivo para eliminarlo
                $stmt = $pdo->prepare("SELECT file_path FROM support_apps WHERE id = ?");
                $stmt->execute([$app_id]);
                $app = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($app && file_exists($app['file_path'])) {
                    unlink($app['file_path']);
                }
                
                // Eliminar de la base de datos
                $stmt = $pdo->prepare("DELETE FROM support_apps WHERE id = ?");
                $stmt->execute([$app_id]);
                
                $success_message = "Aplicación eliminada correctamente";
                break;
                
            case 'upload_app':
                try {
                    $name = clean_input($_POST['name']);
                    $description = clean_input($_POST['description']);
                    $version = clean_input($_POST['version']);
                    $platform = clean_input($_POST['platform']);
                    $features = clean_input($_POST['features'] ?? '');
                    $download_url = clean_input($_POST['download_url'] ?? '');
                    $external_url = clean_input($_POST['external_url'] ?? '');

                    if (empty($name) || empty($version) || empty($platform)) {
                        throw new Exception("Todos los campos obligatorios deben completarse");
                    }

                    // Procesar archivo subido
                    if (isset($_FILES['app_file']) && $_FILES['app_file']['error'] === UPLOAD_ERR_OK) {
                        $upload_dir = 'apploader/';
                        if (!is_dir($upload_dir)) {
                            mkdir($upload_dir, 0755, true);
                        }

                        // Validar tipo de archivo
                        $allowed_extensions = ['apk', 'ipa', 'exe', 'dmg', 'deb', 'zip', 'msi'];
                        $file_extension = strtolower(pathinfo($_FILES['app_file']['name'], PATHINFO_EXTENSION));

                        if (!in_array($file_extension, $allowed_extensions)) {
                            throw new Exception("Tipo de archivo no permitido. Extensiones permitidas: " . implode(', ', $allowed_extensions));
                        }

                        // Validar tamaño (máximo 100MB)
                        if ($_FILES['app_file']['size'] > 100 * 1024 * 1024) {
                            throw new Exception("El archivo es demasiado grande. Tamaño máximo: 100MB");
                        }

                        $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $name) . '_' . $version . '.' . $file_extension;
                        $file_path = $upload_dir . $filename;

                        if (move_uploaded_file($_FILES['app_file']['tmp_name'], $file_path)) {
                            $file_size = filesize($file_path);

                            $stmt = $pdo->prepare("INSERT INTO support_apps (name, description, version, platform, file_path, file_size, features, download_url, external_url, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')");
                            $stmt->execute([$name, $description, $version, $platform, $file_path, $file_size, $features, $download_url, $external_url]);

                            // Crear notificación
                            $app_id = $pdo->lastInsertId();
                            $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
                            $stmt->execute(['app_download', 'Nueva aplicación agregada', "Se agregó la aplicación {$name} v{$version}", $app_id, 'support_apps']);

                            $success_message = "Aplicación subida correctamente";
                        } else {
                            throw new Exception("Error al mover el archivo al directorio de destino");
                        }
                    } else {
                        // Permitir crear app sin archivo (solo con URLs externas)
                        if (empty($download_url) && empty($external_url)) {
                            throw new Exception("Debe proporcionar un archivo o al menos una URL de descarga");
                        }

                        $stmt = $pdo->prepare("INSERT INTO support_apps (name, description, version, platform, features, download_url, external_url, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'active')");
                        $stmt->execute([$name, $description, $version, $platform, $features, $download_url, $external_url]);

                        $success_message = "Aplicación agregada correctamente";
                    }
                } catch (Exception $e) {
                    $error_message = $e->getMessage();
                }
                break;
        }
    }

    // Manejar subida de APK
    if (isset($_FILES['apk_file'])) {
        $file = $_FILES['apk_file'];
        if ($file['error'] === UPLOAD_ERR_OK) {
            $filename = basename($file['name']);
            $targetPath = $uploadDir . '/' . $filename;
            
            // Verificar que sea un archivo APK
            $fileType = strtolower(pathinfo($targetPath, PATHINFO_EXTENSION));
            if ($fileType === 'apk') {
                if (move_uploaded_file($file['tmp_name'], $targetPath)) {
                    $success_message = "APK subido correctamente";
                } else {
                    $error_message = "Error al subir el archivo";
                }
            } else {
                $error_message = "Solo se permiten archivos APK";
            }
        }
    }
}

// Obtener filtros
$platform_filter = $_GET['platform'] ?? 'all';
$status_filter = $_GET['status'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($platform_filter !== 'all') {
    $where_conditions[] = "platform = ?";
    $params[] = $platform_filter;
}

if ($status_filter !== 'all') {
    $is_active = $status_filter === 'active' ? 1 : 0;
    $where_conditions[] = "is_active = ?";
    $params[] = $is_active;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener aplicaciones
$stmt = $pdo->prepare("
    SELECT sa.*, 
           (SELECT COUNT(*) FROM app_downloads ad WHERE ad.app_id = sa.id) as total_downloads,
           (SELECT COUNT(*) FROM app_downloads ad WHERE ad.app_id = sa.id AND DATE(ad.downloaded_at) = CURDATE()) as today_downloads
    FROM support_apps sa 
    $where_clause
    ORDER BY sa.created_at DESC
");
$stmt->execute($params);
$apps = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total_apps,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_apps,
        SUM(download_count) as total_downloads,
        (SELECT COUNT(*) FROM app_downloads WHERE DATE(downloaded_at) = CURDATE()) as today_downloads
    FROM support_apps
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Gestión de Aplicaciones - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .filters {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }

        .app-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
        }

        .app-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
        }

        .app-header {
            padding: 1.5rem;
            background: var(--dark-bg);
        }

        .app-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .app-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .app-content {
            padding: 1.5rem;
        }

        .app-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .app-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .app-stat {
            text-align: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
        }

        .app-stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .app-stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .app-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .platform-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .platform-android { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
        .platform-ios { background: rgba(33, 150, 243, 0.2); color: #2196f3; }
        .platform-windows { background: rgba(96, 125, 139, 0.2); color: #607d8b; }
        .platform-mac { background: rgba(158, 158, 158, 0.2); color: #9e9e9e; }
        .platform-linux { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        .platform-web { background: rgba(156, 39, 176, 0.2); color: #9c27b0; }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active { background: rgba(16, 185, 129, 0.2); color: #10b981; }
        .status-inactive { background: rgba(107, 114, 128, 0.2); color: #6b7280; }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .upload-form {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            display: none;
        }

        .upload-form.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .file-input {
            position: relative;
            overflow: hidden;
            display: block;
            cursor: pointer;
            background: var(--dark-bg);
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            user-select: none;
            width: 100%;
        }

        .file-input:hover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
        }

        .file-input:active {
            transform: translateY(0);
        }

        .file-input.dragover {
            border-color: var(--primary-color) !important;
            background: rgba(37, 99, 235, 0.15) !important;
            transform: scale(1.02);
        }

        .file-input input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            cursor: pointer;
        }

        .file-input .upload-icon {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .file-input:hover .upload-icon {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .file-input .upload-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .file-input .upload-info {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin: 0.5rem 0;
        }

        .file-input .upload-tip {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 0.75rem;
            margin-top: 1rem;
            font-size: 0.85rem;
            color: var(--accent-color);
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .apps-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animaciones para mejorar la experiencia del usuario */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Clases de animación */
        .animate-fadeIn {
            animation: fadeIn 0.3s ease;
        }

        .animate-slideIn {
            animation: slideIn 0.3s ease;
        }

        .animate-shake {
            animation: shake 0.5s ease;
        }

        .animate-pulse {
            animation: pulse 0.3s ease;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="apps_admin.php" class="logo">
                <i class="fas fa-mobile-alt"></i>
                <span>Gestión de Aplicaciones</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-mobile-alt"></i>
                Gestión de Aplicaciones
            </h1>
            
            <button onclick="toggleUploadForm()" class="btn btn-success">
                <i class="fas fa-plus"></i>
                Subir Nueva App
            </button>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_apps']; ?></div>
                <div class="stat-label">Total Aplicaciones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['active_apps']; ?></div>
                <div class="stat-label">Aplicaciones Activas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total_downloads']); ?></div>
                <div class="stat-label">Descargas Totales</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today_downloads']; ?></div>
                <div class="stat-label">Descargas Hoy</div>
            </div>
        </div>

        <!-- Formulario de Subida -->
        <div id="uploadForm" class="upload-form">
            <h2 style="margin-bottom: 1.5rem; color: var(--text-primary);">
                <i class="fas fa-upload"></i>
                Subir Nueva Aplicación
            </h2>

            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="upload_app">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Nombre de la Aplicación</label>
                        <input type="text" name="name" class="form-input" required placeholder="Ej: IPTV Player">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Versión</label>
                        <input type="text" name="version" class="form-input" required placeholder="Ej: 2.1.0">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Plataforma</label>
                        <select name="platform" class="form-select" required>
                            <option value="">Seleccionar plataforma</option>
                            <option value="android">Android</option>
                            <option value="ios">iOS</option>
                            <option value="windows">Windows</option>
                            <option value="mac">Mac</option>
                            <option value="linux">Linux</option>
                            <option value="web">Web</option>
                        </select>
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">Descripción</label>
                    <textarea name="description" class="form-textarea" placeholder="Descripción de la aplicación..."></textarea>
                </div>

                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">Características</label>
                    <textarea name="features" class="form-textarea" placeholder="Ej: EPG, Grabación, Favoritos, Control parental..."></textarea>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">URL de Descarga (Opcional)</label>
                        <input type="url" name="download_url" class="form-input" placeholder="https://play.google.com/store/apps/...">
                    </div>

                    <div class="form-group">
                        <label class="form-label">URL Externa (Opcional)</label>
                        <input type="url" name="external_url" class="form-input" placeholder="https://sitio-web.com">
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 2rem;">
                    <label class="form-label">
                        📱 Archivo de la Aplicación
                        <span style="color: var(--text-secondary); font-weight: normal;">(Opcional si proporcionas URLs)</span>
                    </label>
                    <div class="file-input" id="fileInputContainer">
                        <input type="file" name="app_file" accept=".apk,.ipa,.exe,.dmg,.deb,.zip,.msi" id="appFileInput">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            Haz clic para seleccionar el archivo o arrastra aquí
                        </div>
                        <div class="upload-info">
                            📋 <strong>Formatos soportados:</strong> APK, IPA, EXE, DMG, DEB, ZIP, MSI
                        </div>
                        <div class="upload-info" style="color: var(--warning-color);">
                            ⚠️ <strong>Tamaño máximo:</strong> 100MB
                        </div>
                        <div class="upload-tip">
                            💡 <strong>Tip:</strong> Si no tienes archivo, puedes usar solo las URLs de descarga externa
                        </div>
                    </div>
                </div>

                <!-- Barra de progreso de subida -->
                <div id="uploadProgress" style="display: none; margin-bottom: 1rem;">
                    <div style="background: var(--dark-bg); border-radius: 8px; padding: 1rem;">
                        <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-upload" style="color: var(--primary-color);"></i>
                            <span>Subiendo aplicación...</span>
                        </div>
                        <div style="background: var(--border-color); border-radius: 4px; height: 8px; overflow: hidden;">
                            <div id="progressBar" style="background: var(--primary-color); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                        </div>
                        <div id="progressText" style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.5rem;">0%</div>
                    </div>
                </div>

                <div style="display: flex; gap: 1rem;">
                    <button type="submit" name="action" value="upload_app" class="btn btn-success" id="submitBtn">
                        <i class="fas fa-upload"></i>
                        Subir Aplicación
                    </button>
                    <button type="button" onclick="resetForm()" class="btn btn-secondary">
                        <i class="fas fa-eraser"></i>
                        Limpiar Formulario
                    </button>
                    <button type="button" onclick="toggleUploadForm()" class="btn btn-warning">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Plataforma</label>
                    <select name="platform" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $platform_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="android" <?php echo $platform_filter === 'android' ? 'selected' : ''; ?>>Android</option>
                        <option value="ios" <?php echo $platform_filter === 'ios' ? 'selected' : ''; ?>>iOS</option>
                        <option value="windows" <?php echo $platform_filter === 'windows' ? 'selected' : ''; ?>>Windows</option>
                        <option value="mac" <?php echo $platform_filter === 'mac' ? 'selected' : ''; ?>>Mac</option>
                        <option value="linux" <?php echo $platform_filter === 'linux' ? 'selected' : ''; ?>>Linux</option>
                        <option value="web" <?php echo $platform_filter === 'web' ? 'selected' : ''; ?>>Web</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Activas</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactivas</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Lista de Aplicaciones -->
        <div class="apps-grid">
            <?php if (empty($apps)): ?>
            <div style="grid-column: 1 / -1; text-align: center; color: var(--text-secondary); padding: 3rem;">
                <i class="fas fa-mobile-alt" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No hay aplicaciones que coincidan con los filtros seleccionados</p>
            </div>
            <?php else: ?>
                <?php foreach ($apps as $app): ?>
                <div class="app-card">
                    <div class="app-header">
                        <div class="app-title">
                            <?php echo htmlspecialchars($app['name']); ?>
                        </div>
                        <div class="app-meta">
                            <span><i class="fas fa-code-branch"></i> v<?php echo htmlspecialchars($app['version']); ?></span>
                            <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($app['created_at'])); ?></span>
                            <span><i class="fas fa-hdd"></i> <?php echo formatBytes($app['file_size']); ?></span>
                        </div>
                    </div>

                    <div class="app-content">
                        <div style="display: flex; gap: 0.5rem; margin-bottom: 1rem; flex-wrap: wrap;">
                            <span class="platform-badge platform-<?php echo $app['platform']; ?>">
                                <?php echo ucfirst($app['platform']); ?>
                            </span>
                            <span class="status-badge status-<?php echo $app['is_active'] ? 'active' : 'inactive'; ?>">
                                <?php echo $app['is_active'] ? 'Activa' : 'Inactiva'; ?>
                            </span>
                        </div>

                        <?php if ($app['description']): ?>
                        <div class="app-description">
                            <?php echo nl2br(htmlspecialchars($app['description'])); ?>
                        </div>
                        <?php endif; ?>

                        <div class="app-stats">
                            <div class="app-stat">
                                <div class="app-stat-number"><?php echo number_format($app['total_downloads']); ?></div>
                                <div class="app-stat-label">Descargas Totales</div>
                            </div>
                            <div class="app-stat">
                                <div class="app-stat-number"><?php echo $app['today_downloads']; ?></div>
                                <div class="app-stat-label">Hoy</div>
                            </div>
                        </div>

                        <div class="app-actions">
                            <a href="<?php echo htmlspecialchars($app['file_path']); ?>"
                               class="btn btn-primary"
                               download="<?php echo htmlspecialchars($app['name'] . '_' . $app['version']); ?>">
                                <i class="fas fa-download"></i>
                                Descargar
                            </a>

                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_status">
                                <input type="hidden" name="app_id" value="<?php echo $app['id']; ?>">
                                <button type="submit" class="btn <?php echo $app['is_active'] ? 'btn-warning' : 'btn-success'; ?>">
                                    <i class="fas fa-<?php echo $app['is_active'] ? 'pause' : 'play'; ?>"></i>
                                    <?php echo $app['is_active'] ? 'Desactivar' : 'Activar'; ?>
                                </button>
                            </form>

                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="delete_app">
                                <input type="hidden" name="app_id" value="<?php echo $app['id']; ?>">
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('¿Estás seguro de que quieres eliminar esta aplicación?')">
                                    <i class="fas fa-trash"></i>
                                    Eliminar
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>


    </main>

    <script>
        function toggleUploadForm() {
            const form = document.getElementById('uploadForm');
            form.classList.toggle('active');

            if (form.classList.contains('active')) {
                form.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // Función para resetear el formulario
        function resetForm() {
            const form = document.getElementById('uploadForm');
            if (form) {
                form.reset();
            }

            // Usar la función resetFileInput existente
            resetFileInput();

            // Ocultar progreso
            const progressContainer = document.getElementById('uploadProgress');
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }

        // Variable para evitar múltiples inicializaciones
        let fileInputInitialized = false;

        // Configurar input de archivo
        function setupFileInput() {
            if (fileInputInitialized) return; // Evitar múltiples inicializaciones

            const fileInput = document.querySelector('#appFileInput');
            const fileInputContainer = document.getElementById('fileInputContainer');

            if (fileInput && fileInputContainer) {
                // Hacer clic en el contenedor abre el diálogo de archivos
                fileInputContainer.addEventListener('click', function(e) {
                    // Solo si no se hizo clic en un botón y no es el input mismo
                    if (!e.target.closest('button') && e.target.type !== 'file') {
                        e.preventDefault();
                        e.stopPropagation();
                        fileInput.click();
                    }
                });

                // Manejar cambio de archivo
                fileInput.addEventListener('change', handleFileSelect);

                // Configurar drag & drop
                setupDragAndDrop(fileInputContainer, fileInput);

                fileInputInitialized = true;
            }
        }

        // Configurar funcionalidad de drag & drop
        function setupDragAndDrop(container, fileInput) {
            // Prevenir comportamiento por defecto
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                container.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            // Resaltar área de drop
            ['dragenter', 'dragover'].forEach(eventName => {
                container.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                container.addEventListener(eventName, unhighlight, false);
            });

            // Manejar drop
            container.addEventListener('drop', handleDrop, false);

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            function highlight(e) {
                container.classList.add('dragover');
                container.style.borderColor = '#2563eb';
                container.style.backgroundColor = 'rgba(37, 99, 235, 0.15)';
                container.style.transform = 'scale(1.02)';
            }

            function unhighlight(e) {
                container.classList.remove('dragover');
                container.style.borderColor = '#334155';
                container.style.backgroundColor = 'transparent';
                container.style.transform = 'scale(1)';
            }

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length > 0) {
                    try {
                        // Asignar archivo directamente sin disparar click
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(files[0]);
                        fileInput.files = dataTransfer.files;

                        // Disparar evento change manualmente
                        const changeEvent = new Event('change', { bubbles: true });
                        fileInput.dispatchEvent(changeEvent);
                    } catch (error) {
                        console.error('Error handling dropped file:', error);
                        // Mostrar mensaje de error al usuario
                        alert('Error al procesar el archivo. Por favor, inténtalo de nuevo.');
                    }
                }
            }
        }

        // Manejar selección de archivo
        function handleFileSelect(e) {
            const fileInput = e.target;
            const fileInputContainer = fileInput.parentElement;

            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                const fileName = file.name;
                const fileSize = formatBytes(file.size);
                const fileExtension = fileName.split('.').pop().toLowerCase();

                // Validar tipo de archivo
                const allowedTypes = ['apk', 'ipa', 'exe', 'dmg', 'deb', 'zip', 'msi'];
                if (!allowedTypes.includes(fileExtension)) {
                    alert('❌ Tipo de archivo no permitido. Solo se permiten: ' + allowedTypes.join(', ').toUpperCase());
                    fileInput.value = '';
                    return;
                }

                // Validar tamaño (100MB)
                if (file.size > 100 * 1024 * 1024) {
                    alert('❌ El archivo es demasiado grande. Tamaño máximo: 100MB');
                    fileInput.value = '';
                    return;
                }

                // Mostrar archivo seleccionado
                fileInputContainer.innerHTML = `
                    <i class="fas fa-file-check" style="font-size: 2rem; margin-bottom: 1rem; color: var(--success-color);"></i>
                    <p><strong>📱 ${fileName}</strong></p>
                    <p style="font-size: 0.9rem; color: var(--text-secondary); margin: 0.5rem 0;">
                        📊 Tamaño: ${fileSize}
                    </p>
                    <p style="font-size: 0.8rem; color: var(--success-color); margin-top: 0.5rem;">
                        ✅ Archivo seleccionado correctamente
                    </p>
                    <button type="button" onclick="resetFileInput()" style="
                        background: var(--warning-color);
                        color: white;
                        border: none;
                        padding: 0.5rem 1rem;
                        border-radius: 6px;
                        margin-top: 1rem;
                        cursor: pointer;
                        font-size: 0.8rem;
                    ">
                        <i class="fas fa-times"></i> Cambiar archivo
                    </button>
                `;
                fileInputContainer.appendChild(fileInput);
            }
        }

        // Función para resetear solo el input de archivo
        function resetFileInput() {
            const fileInputContainer = document.getElementById('fileInputContainer');
            const fileInput = fileInputContainer.querySelector('#appFileInput');

            // Solo resetear el valor del input, no recrear todo
            if (fileInput) {
                fileInput.value = '';
            }

            // Restaurar contenido original
            fileInputContainer.innerHTML = `
                <input type="file" name="app_file" accept=".apk,.ipa,.exe,.dmg,.deb,.zip,.msi" id="appFileInput">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="upload-text">
                    Haz clic para seleccionar el archivo o arrastra aquí
                </div>
                <div class="upload-info">
                    📋 <strong>Formatos soportados:</strong> APK, IPA, EXE, DMG, DEB, ZIP, MSI
                </div>
                <div class="upload-info" style="color: var(--warning-color);">
                    ⚠️ <strong>Tamaño máximo:</strong> 100MB
                </div>
                <div class="upload-tip">
                    💡 <strong>Tip:</strong> Si no tienes archivo, puedes usar solo las URLs de descarga externa
                </div>
            `;

            // Reinicializar eventos
            fileInputInitialized = false;
            setupFileInput();
        }

        // Manejar envío del formulario con progreso
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            const fileInput = document.querySelector('#appFileInput');
            const submitBtn = document.getElementById('submitBtn');
            const progressContainer = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            // Si hay archivo, mostrar progreso
            if (fileInput && fileInput.files.length > 0) {
                // Mostrar barra de progreso
                progressContainer.style.display = 'block';
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subiendo...';

                // Simular progreso (ya que no podemos acceder al progreso real de PHP)
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 90) progress = 90;

                    progressBar.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '%';
                }, 200);

                // Limpiar interval después de 10 segundos
                setTimeout(() => {
                    clearInterval(interval);
                    progressBar.style.width = '100%';
                    progressText.textContent = '100% - Procesando...';
                }, 10000);
            }
        });

        // Inicializar cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            setupFileInput();
        });

        // También inicializar inmediatamente por si el DOM ya está listo
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupFileInput);
        } else {
            setupFileInput();
        }

        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }

        // Confirmación para acciones críticas
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'delete_app') {
                    if (!confirm('¿Estás seguro de que quieres eliminar esta aplicación? Esta acción no se puede deshacer.')) {
                        e.preventDefault();
                    }
                }
            });
        });


    </script>
</body>
</html>

<?php
function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>
