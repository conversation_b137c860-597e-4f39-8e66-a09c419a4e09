<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$ticket_id = (int)($_GET['id'] ?? 0);

if (!$ticket_id) {
    header('Location: user_tickets.php');
    exit;
}

// Procesar respuesta del usuario
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_response'])) {
    $message = clean_input($_POST['message']);
    
    $stmt = $pdo->prepare("INSERT INTO ticket_responses (ticket_id, user_id, message, is_admin) VALUES (?, ?, ?, 0)");
    $stmt->execute([$ticket_id, $user_id, $message]);
    
    // Actualizar timestamp del ticket
    $stmt = $pdo->prepare("UPDATE support_tickets SET updated_at = NOW() WHERE id = ?");
    $stmt->execute([$ticket_id]);
    
    $success_message = "Respuesta agregada correctamente";
}

// Obtener información del ticket
$stmt = $pdo->prepare("
    SELECT st.*, u.username as assigned_to_name
    FROM support_tickets st 
    LEFT JOIN users u ON st.assigned_to = u.id 
    WHERE st.id = ? AND st.user_id = ?
");
$stmt->execute([$ticket_id, $user_id]);
$ticket = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$ticket) {
    header('Location: user_tickets.php');
    exit;
}

// Obtener respuestas del ticket
$stmt = $pdo->prepare("
    SELECT tr.*, u.username
    FROM ticket_responses tr
    LEFT JOIN users u ON tr.user_id = u.id
    WHERE tr.ticket_id = ?
    ORDER BY tr.created_at ASC
");
$stmt->execute([$ticket_id]);
$responses = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Ticket #<?php echo $ticket['id']; ?> - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .ticket-header {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--primary-color);
        }

        .ticket-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .ticket-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
        }

        .meta-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .status-badge {
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-open { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .status-in_progress { 
            background: rgba(37, 99, 235, 0.2); 
            color: #2563eb; 
        }

        .status-resolved { 
            background: rgba(16, 185, 129, 0.2); 
            color: #10b981; 
        }

        .status-closed { 
            background: rgba(107, 114, 128, 0.2); 
            color: #6b7280; 
        }

        .ticket-description {
            background: rgba(255,255,255,0.05);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-top: 1rem;
            line-height: 1.7;
        }

        .responses-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .responses-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .responses-list {
            padding: 1.5rem;
            max-height: 600px;
            overflow-y: auto;
        }

        .response-item {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .response-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .response-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
        }

        .response-avatar.user {
            background: var(--accent-color);
        }

        .response-avatar.admin {
            background: var(--primary-color);
        }

        .response-content {
            flex: 1;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .response-author {
            font-weight: 600;
            color: var(--text-primary);
        }

        .response-time {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .response-message {
            color: var(--text-primary);
            line-height: 1.6;
            background: rgba(255,255,255,0.05);
            padding: 1rem;
            border-radius: var(--border-radius);
        }

        .add-response-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-textarea {
            width: 100%;
            padding: 1rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
            min-height: 120px;
            resize: vertical;
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .empty-responses {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .empty-responses i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .ticket-meta {
                grid-template-columns: 1fr;
            }

            .response-item {
                flex-direction: column;
                gap: 0.5rem;
            }

            .response-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_ticket_detail.php?id=<?php echo $ticket['id']; ?>" class="logo">
                <i class="fas fa-ticket-alt"></i>
                <span>Ticket #<?php echo $ticket['id']; ?></span>
            </a>
            
            <div class="nav-buttons">
                <a href="user_tickets.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Tickets</span>
                </a>
                <a href="index2.php" class="nav-btn">
                    <i class="fas fa-headset"></i>
                    <span>Servicios</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Información del Ticket -->
        <div class="ticket-header">
            <h1 class="ticket-title">
                <?php echo htmlspecialchars($ticket['subject']); ?>
            </h1>
            
            <div class="ticket-meta">
                <div class="meta-item">
                    <i class="fas fa-hashtag"></i>
                    <span>ID:</span>
                    <span class="meta-value">#<?php echo $ticket['id']; ?></span>
                </div>
                
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>Creado:</span>
                    <span class="meta-value"><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></span>
                </div>
                
                <div class="meta-item">
                    <i class="fas fa-tag"></i>
                    <span>Categoría:</span>
                    <span class="meta-value"><?php echo ucfirst($ticket['category']); ?></span>
                </div>
                
                <div class="meta-item">
                    <i class="fas fa-flag"></i>
                    <span>Prioridad:</span>
                    <span class="meta-value"><?php echo ucfirst($ticket['priority']); ?></span>
                </div>
                
                <div class="meta-item">
                    <i class="fas fa-info-circle"></i>
                    <span>Estado:</span>
                    <span class="status-badge status-<?php echo $ticket['status']; ?>">
                        <?php 
                        $status_labels = [
                            'open' => 'Abierto',
                            'in_progress' => 'En Progreso',
                            'resolved' => 'Resuelto',
                            'closed' => 'Cerrado'
                        ];
                        echo $status_labels[$ticket['status']] ?? $ticket['status'];
                        ?>
                    </span>
                </div>
                
                <?php if ($ticket['assigned_to_name']): ?>
                <div class="meta-item">
                    <i class="fas fa-user"></i>
                    <span>Asignado a:</span>
                    <span class="meta-value"><?php echo htmlspecialchars($ticket['assigned_to_name']); ?></span>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="ticket-description">
                <strong>Descripción del problema:</strong><br><br>
                <?php echo nl2br(htmlspecialchars($ticket['description'])); ?>
            </div>
        </div>

        <!-- Respuestas -->
        <div class="responses-section">
            <div class="responses-header">
                <h2 class="section-title">
                    <i class="fas fa-comments"></i>
                    Conversación (<?php echo count($responses); ?> respuestas)
                </h2>
            </div>
            
            <div class="responses-list">
                <?php if (empty($responses)): ?>
                <div class="empty-responses">
                    <i class="fas fa-comments"></i>
                    <h3>No hay respuestas aún</h3>
                    <p>Cuando recibas una respuesta de nuestro equipo, aparecerá aquí</p>
                </div>
                <?php else: ?>
                    <?php foreach ($responses as $response): ?>
                    <div class="response-item">
                        <div class="response-avatar <?php echo $response['is_admin'] ? 'admin' : 'user'; ?>">
                            <?php if ($response['is_admin']): ?>
                                <i class="fas fa-user-shield"></i>
                            <?php else: ?>
                                <?php echo strtoupper(substr($response['username'], 0, 1)); ?>
                            <?php endif; ?>
                        </div>
                        
                        <div class="response-content">
                            <div class="response-header">
                                <div class="response-author">
                                    <?php echo htmlspecialchars($response['username']); ?>
                                    <?php if ($response['is_admin']): ?>
                                        <span style="color: var(--primary-color); font-size: 0.8rem; margin-left: 0.5rem;">
                                            <i class="fas fa-shield-alt"></i> Soporte Técnico
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="response-time">
                                    <?php echo date('d/m/Y H:i', strtotime($response['created_at'])); ?>
                                </div>
                            </div>
                            
                            <div class="response-message">
                                <?php echo nl2br(htmlspecialchars($response['message'])); ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Agregar Respuesta -->
        <?php if ($ticket['status'] !== 'closed'): ?>
        <div class="add-response-section">
            <h2 class="section-title">
                <i class="fas fa-reply"></i>
                Agregar Respuesta
            </h2>
            
            <form method="POST">
                <div class="form-group">
                    <label class="form-label">Tu respuesta</label>
                    <textarea name="message" class="form-textarea" required placeholder="Escribe tu respuesta o proporciona información adicional..."></textarea>
                </div>
                
                <button type="submit" name="add_response" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Enviar Respuesta
                </button>
            </form>
        </div>
        <?php else: ?>
        <div class="add-response-section" style="text-align: center; color: var(--text-secondary);">
            <i class="fas fa-lock" style="font-size: 2rem; margin-bottom: 1rem;"></i>
            <h3>Ticket Cerrado</h3>
            <p>Este ticket ha sido cerrado y no se pueden agregar más respuestas.</p>
        </div>
        <?php endif; ?>
    </main>

    <script>
        // Auto-resize textarea
        const textarea = document.querySelector('.form-textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        }

        // Auto-scroll a la última respuesta
        const responsesList = document.querySelector('.responses-list');
        if (responsesList && responsesList.children.length > 1) {
            responsesList.scrollTop = responsesList.scrollHeight;
        }

        // Validación del formulario
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const message = textarea.value.trim();
                
                if (message.length < 10) {
                    alert('La respuesta debe tener al menos 10 caracteres');
                    e.preventDefault();
                    return;
                }
            });
        }

        // Auto-refresh cada 30 segundos para nuevas respuestas
        setInterval(function() {
            if (document.hidden) return;
            
            // Solo actualizar si no estamos escribiendo
            const activeElement = document.activeElement;
            if (activeElement !== textarea) {
                location.reload();
            }
        }, 30000);

        // Animación de entrada para las respuestas
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.response-item').forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(item);
        });
    </script>
</body>
</html>
