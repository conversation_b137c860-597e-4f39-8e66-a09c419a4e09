<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔗 Probando Comunicación Sistema de Canales</h1>";

try {
    // 1. Probar API de canales
    echo "<h2>🔌 Probando API de Canales...</h2>";
    
    $api_tests = [
        'get_all_requests' => 'Obtener todas las solicitudes',
        'get_stats' => 'Obtener estadísticas',
        'get_notifications' => 'Obtener notificaciones'
    ];
    
    foreach ($api_tests as $action => $description) {
        try {
            $response = @file_get_contents("api_channels_system.php?action=$action");
            if ($response) {
                $data = json_decode($response, true);
                if ($data && $data['success']) {
                    $success_messages[] = "✅ API $description: OK";
                } else {
                    $error_messages[] = "❌ API $description: Error en respuesta";
                }
            } else {
                $error_messages[] = "❌ API $description: No responde";
            }
        } catch (Exception $e) {
            $error_messages[] = "❌ API $description: " . $e->getMessage();
        }
    }
    
    // 2. Verificar tabla channel_requests
    echo "<h2>🗄️ Verificando base de datos...</h2>";
    
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'channel_requests'");
        if ($stmt->rowCount() > 0) {
            $success_messages[] = "✅ Tabla channel_requests existe";
            
            // Verificar estructura
            $stmt = $pdo->query("DESCRIBE channel_requests");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $success_messages[] = "✅ Columnas: " . implode(', ', $columns);
            
            // Contar registros
            $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests");
            $count = $stmt->fetchColumn();
            $success_messages[] = "✅ Total de solicitudes: $count";
            
        } else {
            $error_messages[] = "❌ Tabla channel_requests no existe";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error verificando tabla: " . $e->getMessage();
    }
    
    // 3. Verificar archivos del sistema
    echo "<h2>📁 Verificando archivos...</h2>";
    
    $files = [
        'user_channels.php' => 'Página de usuario para solicitar canales',
        'admin_channels_real.php' => 'Panel de administración de canales',
        'api_channels_system.php' => 'API del sistema de canales'
    ];
    
    foreach ($files as $file => $description) {
        if (file_exists($file)) {
            $success_messages[] = "✅ $description ($file)";
        } else {
            $error_messages[] = "❌ $description ($file) no existe";
        }
    }
    
    // 4. Probar directorio apploader
    echo "<h2>📱 Verificando sistema de aplicaciones...</h2>";
    
    $apploader_dir = __DIR__ . '/apploader';
    if (is_dir($apploader_dir)) {
        $success_messages[] = "✅ Directorio apploader existe";
        
        if (is_writable($apploader_dir)) {
            $success_messages[] = "✅ Directorio apploader tiene permisos de escritura";
        } else {
            $error_messages[] = "❌ Directorio apploader no tiene permisos de escritura";
        }
        
        // Contar archivos APK
        $apk_files = glob($apploader_dir . '/*.apk');
        $success_messages[] = "✅ Archivos APK disponibles: " . count($apk_files);
        
    } else {
        $error_messages[] = "❌ Directorio apploader no existe";
    }
    
    // 5. Verificar tabla support_apps
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'support_apps'");
        if ($stmt->rowCount() > 0) {
            $success_messages[] = "✅ Tabla support_apps existe";
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps");
            $app_count = $stmt->fetchColumn();
            $success_messages[] = "✅ Aplicaciones registradas: $app_count";
            
        } else {
            $error_messages[] = "❌ Tabla support_apps no existe";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error verificando tabla support_apps: " . $e->getMessage();
    }
    
    // 6. Probar comunicación entre archivos
    echo "<h2>🔗 Probando comunicación...</h2>";
    
    // Simular solicitud de canal
    try {
        $test_data = [
            'channel_name' => 'Canal de Prueba',
            'country' => 'España',
            'language' => 'Español',
            'category' => 'Entretenimiento',
            'description' => 'Canal de prueba para verificar comunicación'
        ];
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/x-www-form-urlencoded',
                'content' => http_build_query($test_data)
            ]
        ]);
        
        $response = @file_get_contents('api_channels_system.php?action=submit_request', false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                $success_messages[] = "✅ Comunicación user_channels.php ↔ admin_channels_real.php: OK";
                
                // Limpiar solicitud de prueba
                $request_id = $data['request_id'];
                $stmt = $pdo->prepare("DELETE FROM channel_requests WHERE id = ?");
                $stmt->execute([$request_id]);
                
            } else {
                $error_messages[] = "❌ Error en comunicación: " . ($data['error'] ?? 'Unknown');
            }
        } else {
            $error_messages[] = "❌ No hay respuesta de la API";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error probando comunicación: " . $e->getMessage();
    }
    
    // 7. Verificar configuración PHP para subidas
    echo "<h2>⚙️ Verificando configuración PHP...</h2>";
    
    $upload_max = ini_get('upload_max_filesize');
    $post_max = ini_get('post_max_size');
    $file_uploads = ini_get('file_uploads');
    
    if ($file_uploads) {
        $success_messages[] = "✅ Subida de archivos habilitada";
    } else {
        $error_messages[] = "❌ Subida de archivos deshabilitada";
    }
    
    $success_messages[] = "ℹ️ upload_max_filesize: $upload_max";
    $success_messages[] = "ℹ️ post_max_size: $post_max";
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Test Comunicación Canales - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Test de Comunicación Completado</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Pruebas Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="user_channels.php" class="btn">📺 Solicitar Canales</a>
            <a href="admin_channels_real.php" class="btn">⚙️ Admin Canales</a>
            <a href="apps_admin.php" class="btn">📱 Admin Apps</a>
            <a href="setup_apploader.php" class="btn">🔧 Setup Apploader</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
        
        <?php if (count($error_messages) == 0): ?>
        <div style="background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; border-radius: 12px; padding: 2rem; margin: 2rem 0; text-align: center;">
            <h2 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Comunicación Establecida!</h2>
            <p style="color: #10b981; font-size: 1.1rem;">
                La comunicación entre user_channels.php y admin_channels_real.php está funcionando correctamente.
                El sistema de aplicaciones con apploader también está configurado.
            </p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
