<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔔 Generando Notificaciones de Prueba</h1>";

try {
    // Verificar que la tabla admin_notifications existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
    if ($stmt->rowCount() == 0) {
        $error_messages[] = "❌ La tabla admin_notifications no existe. Ejecuta create_notifications_table.php primero.";
    } else {
        // Generar notificaciones de prueba
        $notifications = [
            [
                'type' => 'ticket',
                'title' => 'Nuevo Ticket #' . rand(100, 999),
                'message' => 'Usuario reporta problemas con la reproducción de canales HD',
                'reference_id' => 6,
                'reference_type' => 'support_tickets'
            ],
            [
                'type' => 'chat',
                'title' => 'Nueva sesión de chat',
                'message' => 'Usuario solicita ayuda técnica urgente',
                'reference_id' => rand(1, 10),
                'reference_type' => 'chat_sessions'
            ],
            [
                'type' => 'app_download',
                'title' => 'Nueva descarga de aplicación',
                'message' => 'Se descargó IPTV Player Android v2.1.0',
                'reference_id' => 1,
                'reference_type' => 'support_apps'
            ],
            [
                'type' => 'channel_request',
                'title' => 'Solicitud de canal',
                'message' => 'Usuario solicita agregar ESPN 2 HD',
                'reference_id' => rand(1, 20),
                'reference_type' => 'channel_requests'
            ],
            [
                'type' => 'system',
                'title' => 'Actualización del sistema',
                'message' => 'Se aplicaron mejoras de seguridad y rendimiento',
                'reference_id' => null,
                'reference_type' => null
            ]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type, is_read) VALUES (?, ?, ?, ?, ?, 0)");
        
        foreach ($notifications as $notification) {
            try {
                $stmt->execute([
                    $notification['type'],
                    $notification['title'],
                    $notification['message'],
                    $notification['reference_id'],
                    $notification['reference_type']
                ]);
                $success_messages[] = "✅ Notificación creada: " . $notification['title'];
            } catch (Exception $e) {
                $error_messages[] = "❌ Error creando notificación: " . $e->getMessage();
            }
        }
        
        // Crear algunas notificaciones adicionales para simular actividad
        $additional_notifications = [
            ['ticket', 'Ticket Resuelto #' . rand(50, 99), 'Se resolvió el problema de conectividad'],
            ['chat', 'Chat Finalizado', 'Sesión de soporte técnico completada exitosamente'],
            ['app_download', 'Descarga Masiva', 'Se registraron 15 descargas en la última hora'],
            ['system', 'Mantenimiento Programado', 'Mantenimiento del servidor programado para mañana']
        ];
        
        foreach ($additional_notifications as $notif) {
            try {
                $stmt->execute([
                    $notif[0],
                    $notif[1],
                    $notif[2],
                    rand(1, 50),
                    $notif[0] === 'system' ? null : 'test_reference'
                ]);
                $success_messages[] = "✅ Notificación adicional: " . $notif[1];
            } catch (Exception $e) {
                $error_messages[] = "❌ Error: " . $e->getMessage();
            }
        }
        
        // Mostrar estadísticas finales
        $stmt = $pdo->query("SELECT COUNT(*) FROM admin_notifications WHERE is_read = 0");
        $unread_count = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM admin_notifications WHERE is_read = 0 GROUP BY type");
        $type_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $success_messages[] = "📊 Total de notificaciones no leídas: $unread_count";
        
        foreach ($type_counts as $type_count) {
            $success_messages[] = "📋 {$type_count['type']}: {$type_count['count']} notificaciones";
        }
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 Generar Notificaciones - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
        .auto-refresh {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Notificaciones de Prueba Generadas</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <div class="auto-refresh">
            <h3>🔄 Auto-actualización Activada</h3>
            <p>Las notificaciones se actualizarán automáticamente en admin2.php cada 10 segundos</p>
            <p>El widget en admin.php se actualizará cada 30 segundos</p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Notificaciones Creadas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="admin2.php" class="btn">🏠 Ver admin2.php</a>
            <a href="admin.php" class="btn">🔔 Ver admin.php</a>
            <a href="api_admin_notifications.php?action=get_notifications" class="btn">📊 Ver API</a>
            <button onclick="location.reload()" class="btn" style="background: #10b981;">🔄 Generar Más</button>
        </div>
    </div>
    
    <script>
        // Auto-refresh cada 30 segundos para generar más notificaciones
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
