<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$session_id = (int)($_GET['id'] ?? 0);
if (!$session_id) {
    header('Location: chat_admin.php');
    exit;
}

// Procesar envío de mensaje
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $message = $_POST['message'];
    $admin_id = $_SESSION['admin_id'] ?? 1;
    
    $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, 1)");
    $stmt->execute([$session_id, $admin_id, $message]);
    
    // Actualizar timestamp de la sesión
    $stmt = $pdo->prepare("UPDATE chat_sessions SET started_at = started_at WHERE id = ?");
    $stmt->execute([$session_id]);
    
    // Redirigir para evitar reenvío
    header("Location: chat_detail.php?id=$session_id");
    exit;
}

// Obtener información de la sesión
$stmt = $pdo->prepare("
    SELECT cs.*, u.username, u.email
    FROM chat_sessions cs 
    LEFT JOIN users u ON cs.user_id = u.id 
    WHERE cs.id = ?
");
$stmt->execute([$session_id]);
$session = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$session) {
    header('Location: chat_admin.php');
    exit;
}

// Obtener mensajes del chat
$stmt = $pdo->prepare("
    SELECT cm.*, u.username, cm.is_admin
    FROM chat_messages cm
    LEFT JOIN users u ON cm.sender_id = u.id
    WHERE cm.session_id = ?
    ORDER BY cm.sent_at ASC
");
$stmt->execute([$session_id]);
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 Chat #<?php echo $session['id']; ?> - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .chat-container {
            display: flex;
            height: calc(100vh - 80px);
            max-width: 1200px;
            margin: 0 auto;
        }

        .chat-sidebar {
            width: 300px;
            background: var(--secondary-color);
            border-right: 1px solid var(--border-color);
            padding: 1.5rem;
            overflow-y: auto;
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--dark-bg);
        }

        .session-info {
            margin-bottom: 2rem;
        }

        .session-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-waiting { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .status-active { 
            background: rgba(16, 185, 129, 0.2); 
            color: #10b981; 
        }

        .status-ended { 
            background: rgba(107, 114, 128, 0.2); 
            color: #6b7280; 
        }

        .chat-header {
            background: var(--secondary-color);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .user-info h3 {
            color: var(--text-primary);
            font-size: 1.1rem;
            margin-bottom: 0.2rem;
        }

        .user-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .chat-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .message {
            display: flex;
            gap: 0.75rem;
            max-width: 80%;
        }

        .message.admin {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message-avatar.user {
            background: var(--accent-color);
        }

        .message-avatar.admin {
            background: var(--support-color);
        }

        .message-content {
            background: var(--secondary-color);
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            position: relative;
        }

        .message.admin .message-content {
            background: var(--primary-color);
        }

        .message-text {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            word-wrap: break-word;
        }

        .message-time {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .message-form {
            background: var(--secondary-color);
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .form-container {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .send-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
        }

        .send-btn:hover {
            background: var(--primary-dark);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .empty-chat {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-secondary);
        }

        .empty-chat i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-style: italic;
            padding: 0.5rem 1rem;
        }

        .typing-dots {
            display: flex;
            gap: 0.2rem;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: var(--text-secondary);
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .chat-container {
                flex-direction: column;
            }

            .chat-sidebar {
                width: 100%;
                height: auto;
                max-height: 200px;
            }

            .message {
                max-width: 95%;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="chat_detail.php?id=<?php echo $session['id']; ?>" class="logo">
                <i class="fas fa-comments"></i>
                <span>Chat #<?php echo $session['id']; ?></span>
            </a>
            
            <div class="nav-buttons">
                <a href="chat_admin.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Chats</span>
                </a>
                <a href="admin2.php" class="nav-btn">
                    <i class="fas fa-headset"></i>
                    <span>Admin Soporte</span>
                </a>
            </div>
        </div>
    </header>

    <div class="chat-container">
        <!-- Sidebar con información de la sesión -->
        <div class="chat-sidebar">
            <div class="session-info">
                <h3 class="session-title">Información de la Sesión</h3>
                
                <div class="info-item">
                    <span class="info-label">Estado</span>
                    <span class="status-badge status-<?php echo $session['status']; ?>">
                        <?php 
                        $status_labels = [
                            'waiting' => 'En Espera',
                            'active' => 'Activa',
                            'ended' => 'Finalizada'
                        ];
                        echo $status_labels[$session['status']] ?? $session['status'];
                        ?>
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Usuario</span>
                    <span class="info-value"><?php echo htmlspecialchars($session['username'] ?? 'Anónimo'); ?></span>
                </div>
                
                <?php if ($session['email']): ?>
                <div class="info-item">
                    <span class="info-label">Email</span>
                    <span class="info-value"><?php echo htmlspecialchars($session['email']); ?></span>
                </div>
                <?php endif; ?>
                
                <div class="info-item">
                    <span class="info-label">Iniciado</span>
                    <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($session['started_at'])); ?></span>
                </div>
                
                <?php if ($session['ended_at']): ?>
                <div class="info-item">
                    <span class="info-label">Finalizado</span>
                    <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($session['ended_at'])); ?></span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Duración</span>
                    <span class="info-value">
                        <?php 
                        $duration = strtotime($session['ended_at']) - strtotime($session['started_at']);
                        echo gmdate('H:i:s', $duration);
                        ?>
                    </span>
                </div>
                <?php endif; ?>
                
                <div class="info-item">
                    <span class="info-label">Mensajes</span>
                    <span class="info-value"><?php echo count($messages); ?></span>
                </div>
                
                <?php if ($session['rating']): ?>
                <div class="info-item">
                    <span class="info-label">Calificación</span>
                    <span class="info-value">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star" style="color: <?php echo $i <= $session['rating'] ? '#f59e0b' : '#374151'; ?>"></i>
                        <?php endfor; ?>
                        (<?php echo $session['rating']; ?>/5)
                    </span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Área principal del chat -->
        <div class="chat-main">
            <!-- Header del chat -->
            <div class="chat-header">
                <div class="chat-user">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($session['username'] ?? 'A', 0, 1)); ?>
                    </div>
                    <div class="user-info">
                        <h3><?php echo htmlspecialchars($session['username'] ?? 'Usuario Anónimo'); ?></h3>
                        <p>
                            <?php if ($session['status'] === 'active'): ?>
                                <i class="fas fa-circle" style="color: var(--success-color); font-size: 0.7rem;"></i>
                                En línea
                            <?php else: ?>
                                <i class="fas fa-circle" style="color: var(--text-secondary); font-size: 0.7rem;"></i>
                                <?php echo $session['status'] === 'ended' ? 'Desconectado' : 'En espera'; ?>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
                
                <div class="chat-actions">
                    <?php if ($session['status'] === 'active'): ?>
                    <form method="POST" action="chat_admin.php" style="display: inline;">
                        <input type="hidden" name="action" value="end_session">
                        <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('¿Finalizar esta sesión de chat?')">
                            <i class="fas fa-times"></i>
                            Finalizar
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Mensajes -->
            <div class="messages-container" id="messagesContainer">
                <?php if (empty($messages)): ?>
                <div class="empty-chat">
                    <i class="fas fa-comments"></i>
                    <p>No hay mensajes en esta conversación</p>
                </div>
                <?php else: ?>
                    <?php foreach ($messages as $message): ?>
                    <div class="message <?php echo $message['is_admin'] ? 'admin' : 'user'; ?>">
                        <div class="message-avatar <?php echo $message['is_admin'] ? 'admin' : 'user'; ?>">
                            <?php if ($message['is_admin']): ?>
                                <i class="fas fa-user-shield"></i>
                            <?php else: ?>
                                <?php echo strtoupper(substr($message['username'] ?? 'U', 0, 1)); ?>
                            <?php endif; ?>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                            </div>
                            <div class="message-time">
                                <?php echo date('H:i', strtotime($message['sent_at'])); ?>
                                <?php if ($message['is_admin']): ?>
                                    <i class="fas fa-check" style="margin-left: 0.5rem; color: var(--success-color);"></i>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <div class="typing-indicator" id="typingIndicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <span>El usuario está escribiendo...</span>
                </div>
            </div>

            <!-- Formulario de mensaje -->
            <?php if ($session['status'] !== 'ended'): ?>
            <div class="message-form">
                <form method="POST" class="form-container" id="messageForm">
                    <textarea 
                        name="message" 
                        class="message-input" 
                        placeholder="Escribe tu respuesta..."
                        required
                        id="messageInput"
                    ></textarea>
                    <button type="submit" name="send_message" class="send-btn" id="sendBtn">
                        <i class="fas fa-paper-plane"></i>
                        Enviar
                    </button>
                </form>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Auto-scroll al final de los mensajes
        function scrollToBottom() {
            const container = document.getElementById('messagesContainer');
            container.scrollTop = container.scrollHeight;
        }

        // Scroll inicial
        scrollToBottom();

        // Auto-resize del textarea
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Enviar con Ctrl+Enter
            messageInput.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'Enter') {
                    document.getElementById('messageForm').submit();
                }
            });
        }

        // Auto-refresh cada 5 segundos para nuevos mensajes
        setInterval(function() {
            if (document.hidden) return;
            
            // Solo actualizar si no estamos escribiendo
            const activeElement = document.activeElement;
            if (activeElement !== messageInput) {
                location.reload();
            }
        }, 5000);

        // Simular indicador de escritura (en una implementación real sería via WebSocket)
        let typingTimeout;
        if (messageInput) {
            messageInput.addEventListener('input', function() {
                const typingIndicator = document.getElementById('typingIndicator');
                
                // Mostrar indicador
                typingIndicator.style.display = 'flex';
                
                // Ocultar después de 2 segundos de inactividad
                clearTimeout(typingTimeout);
                typingTimeout = setTimeout(() => {
                    typingIndicator.style.display = 'none';
                }, 2000);
            });
        }

        // Marcar mensajes como leídos
        const messages = document.querySelectorAll('.message:not(.admin)');
        if (messages.length > 0) {
            // En una implementación real, aquí se marcarían como leídos
            console.log(`${messages.length} mensajes del usuario vistos`);
        }

        // Notificación de sonido para nuevos mensajes (opcional)
        const lastMessageCount = <?php echo count($messages); ?>;
        
        // Función para reproducir sonido de notificación
        function playNotificationSound() {
            // En una implementación real, se reproduciría un sonido
            console.log('🔔 Nuevo mensaje recibido');
        }

        // Animación de entrada para mensajes
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.message').forEach(message => {
            message.style.opacity = '0';
            message.style.transform = 'translateY(20px)';
            message.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            observer.observe(message);
        });

        // Validación del formulario
        const messageForm = document.getElementById('messageForm');
        if (messageForm) {
            messageForm.addEventListener('submit', function(e) {
                const message = messageInput.value.trim();
                if (!message) {
                    e.preventDefault();
                    messageInput.focus();
                    return;
                }
                
                // Deshabilitar botón durante el envío
                const sendBtn = document.getElementById('sendBtn');
                sendBtn.disabled = true;
                sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            });
        }
    </script>
</body>
</html>
