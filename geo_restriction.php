<?php
/**
 * Sistema de Restricción Geográfica
 * Permite acceso solo a Latinoamérica y Estados Unidos
 */

class GeoRestriction {
    
    // Países permitidos (códigos ISO 3166-1 alpha-2)
    private static $allowed_countries = [
        // Estados Unidos
        'US', // Estados Unidos
        
        // México y Centroamérica
        'MX', // México
        'GT', // Guatemala
        'BZ', // Belice
        'SV', // El Salvador
        'HN', // Honduras
        'NI', // Nicaragua
        'CR', // Costa Rica
        'PA', // Panamá
        
        // Caribe
        'CU', // Cuba
        'JM', // Jamaica
        'HT', // <PERSON><PERSON><PERSON>
        'DO', // República Dominicana
        'PR', // Puerto Rico
        'TT', // Trinidad y Tobago
        'BB', // Barbados
        'GD', // Granada
        'LC', // Santa Lucía
        'VC', // San Vicente y las Granadinas
        'AG', // Antigua y Barbuda
        'KN', // San Cristóbal y Nieves
        'DM', // Dominica
        'BS', // Bahamas
        
        // Sudamérica
        'CO', // Colombia
        'VE', // Venezuela
        'GY', // Guyana
        'SR', // Surinam
        'GF', // Guayana Francesa
        'BR', // Brasil
        'EC', // Ecuador
        'PE', // Perú
        'BO', // Bolivia
        'PY', // Paraguay
        'UY', // Uruguay
        'AR', // Argentina
        'CL', // Chile
        'FK', // Islas Malvinas
    ];
    
    /**
     * Obtener país del usuario usando múltiples métodos
     */
    public static function getUserCountry() {
        $ip = self::getUserIP();
        
        // Método 1: Usar servicio gratuito ip-api.com
        $country = self::getCountryFromIPAPI($ip);
        if ($country) {
            return $country;
        }
        
        // Método 2: Usar CloudFlare headers (si está disponible)
        $country = self::getCountryFromCloudFlare();
        if ($country) {
            return $country;
        }
        
        // Método 3: Usar headers del servidor
        $country = self::getCountryFromHeaders();
        if ($country) {
            return $country;
        }
        
        // Método 4: Fallback - permitir acceso si no se puede determinar
        return 'US'; // Por defecto permitir
    }
    
    /**
     * Obtener IP real del usuario
     */
    private static function getUserIP() {
        $ip_keys = [
            'HTTP_CF_CONNECTING_IP',     // CloudFlare
            'HTTP_X_FORWARDED_FOR',      // Proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'HTTP_CLIENT_IP',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                // Validar que sea una IP válida y no privada
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Obtener país usando ip-api.com (gratuito, 1000 requests/mes)
     */
    private static function getCountryFromIPAPI($ip) {
        try {
            // No hacer request para IPs locales
            if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0) {
                return 'US'; // Permitir desarrollo local
            }
            
            $url = "http://ip-api.com/json/{$ip}?fields=countryCode";
            $context = stream_context_create([
                'http' => [
                    'timeout' => 3,
                    'user_agent' => 'RGS Media TV Geo Check'
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            if ($response) {
                $data = json_decode($response, true);
                if (isset($data['countryCode'])) {
                    return strtoupper($data['countryCode']);
                }
            }
        } catch (Exception $e) {
            error_log("Geo API Error: " . $e->getMessage());
        }
        
        return null;
    }
    
    /**
     * Obtener país desde headers de CloudFlare
     */
    private static function getCountryFromCloudFlare() {
        if (isset($_SERVER['HTTP_CF_IPCOUNTRY'])) {
            return strtoupper($_SERVER['HTTP_CF_IPCOUNTRY']);
        }
        return null;
    }
    
    /**
     * Obtener país desde otros headers del servidor
     */
    private static function getCountryFromHeaders() {
        $headers = [
            'HTTP_X_COUNTRY_CODE',
            'HTTP_X_GEO_COUNTRY',
            'HTTP_GEOIP_COUNTRY_CODE'
        ];
        
        foreach ($headers as $header) {
            if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
                return strtoupper($_SERVER[$header]);
            }
        }
        
        return null;
    }
    
    /**
     * Verificar si el país está permitido
     */
    public static function isCountryAllowed($country_code) {
        return in_array(strtoupper($country_code), self::$allowed_countries);
    }
    
    /**
     * Verificar acceso del usuario actual
     */
    public static function checkAccess() {
        $country = self::getUserCountry();
        $allowed = self::isCountryAllowed($country);
        
        // Log para debugging
        error_log("Geo Check - IP: " . self::getUserIP() . ", Country: $country, Allowed: " . ($allowed ? 'YES' : 'NO'));
        
        return [
            'allowed' => $allowed,
            'country' => $country,
            'ip' => self::getUserIP()
        ];
    }
    
    /**
     * Obtener nombre del país en español
     */
    public static function getCountryName($country_code) {
        $countries = [
            'US' => 'Estados Unidos',
            'MX' => 'México',
            'GT' => 'Guatemala',
            'BZ' => 'Belice',
            'SV' => 'El Salvador',
            'HN' => 'Honduras',
            'NI' => 'Nicaragua',
            'CR' => 'Costa Rica',
            'PA' => 'Panamá',
            'CU' => 'Cuba',
            'JM' => 'Jamaica',
            'HT' => 'Haití',
            'DO' => 'República Dominicana',
            'PR' => 'Puerto Rico',
            'TT' => 'Trinidad y Tobago',
            'BB' => 'Barbados',
            'GD' => 'Granada',
            'LC' => 'Santa Lucía',
            'VC' => 'San Vicente y las Granadinas',
            'AG' => 'Antigua y Barbuda',
            'KN' => 'San Cristóbal y Nieves',
            'DM' => 'Dominica',
            'BS' => 'Bahamas',
            'CO' => 'Colombia',
            'VE' => 'Venezuela',
            'GY' => 'Guyana',
            'SR' => 'Surinam',
            'GF' => 'Guayana Francesa',
            'BR' => 'Brasil',
            'EC' => 'Ecuador',
            'PE' => 'Perú',
            'BO' => 'Bolivia',
            'PY' => 'Paraguay',
            'UY' => 'Uruguay',
            'AR' => 'Argentina',
            'CL' => 'Chile',
            'FK' => 'Islas Malvinas'
        ];
        
        return $countries[$country_code] ?? $country_code;
    }
    
    /**
     * Obtener lista de países permitidos para mostrar
     */
    public static function getAllowedCountriesList() {
        $list = [];
        foreach (self::$allowed_countries as $code) {
            $list[$code] = self::getCountryName($code);
        }
        return $list;
    }
    
    /**
     * Bypass temporal para testing (solo en desarrollo)
     */
    public static function setTestMode($country_code = 'US') {
        if (isset($_GET['geo_test']) && $_GET['geo_test'] === 'bypass') {
            $_SESSION['geo_bypass'] = $country_code;
            return true;
        }
        return false;
    }
    
    /**
     * Verificar si está en modo test
     */
    public static function isTestMode() {
        return isset($_SESSION['geo_bypass']);
    }
    
    /**
     * Obtener país en modo test
     */
    public static function getTestCountry() {
        return $_SESSION['geo_bypass'] ?? null;
    }
}

// Función helper para uso rápido
function checkGeoAccess() {
    return GeoRestriction::checkAccess();
}

// Función para bloquear acceso automáticamente
function enforceGeoRestriction() {
    $access = GeoRestriction::checkAccess();
    
    if (!$access['allowed']) {
        // Redirigir a página de acceso denegado
        header('Location: geo_blocked.php?country=' . urlencode($access['country']));
        exit;
    }
    
    return $access;
}
?>
