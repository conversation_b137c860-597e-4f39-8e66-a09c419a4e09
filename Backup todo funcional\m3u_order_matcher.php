<?php
// Buscador de coincidencias entre pedidos y contenido M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para limpiar títulos para comparación
function cleanTitleForMatch($title) {
    $clean = strtolower($title);
    $clean = preg_replace('/[^\w\s]/', ' ', $clean);
    $clean = preg_replace('/\s+/', ' ', $clean);
    $clean = trim($clean);
    return $clean;
}

// Función para calcular similitud entre títulos
function calculateSimilarity($title1, $title2) {
    $clean1 = cleanTitleForMatch($title1);
    $clean2 = cleanTitleForMatch($title2);
    
    // Coincidencia exacta
    if ($clean1 === $clean2) {
        return 100;
    }
    
    // Verificar si uno contiene al otro
    if (strpos($clean1, $clean2) !== false || strpos($clean2, $clean1) !== false) {
        return 85;
    }
    
    // Calcular similitud usando similar_text
    similar_text($clean1, $clean2, $percent);
    return round($percent, 2);
}

// Función para buscar coincidencias
function findMatches($order_title, $pdo, $min_similarity = 70) {
    $stmt = $pdo->query("
        SELECT c.*, l.name as list_name 
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1
        ORDER BY c.title
    ");
    $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $matches = [];
    
    foreach ($content as $item) {
        $similarity = calculateSimilarity($order_title, $item['title']);
        
        if ($similarity >= $min_similarity) {
            $matches[] = [
                'content' => $item,
                'similarity' => $similarity,
                'match_type' => $similarity >= 95 ? 'exact' : ($similarity >= 80 ? 'high' : 'partial')
            ];
        }
    }
    
    // Ordenar por similitud descendente
    usort($matches, function($a, $b) {
        return $b['similarity'] <=> $a['similarity'];
    });
    
    return $matches;
}

$search_title = $_GET['search'] ?? '';
$matches = [];

if ($search_title) {
    $matches = findMatches($search_title, $pdo);
}

// Obtener pedidos recientes para sugerencias
$stmt = $pdo->query("
    SELECT DISTINCT title 
    FROM orders 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ORDER BY created_at DESC 
    LIMIT 20
");
$recent_orders = $stmt->fetchAll(PDO::FETCH_COLUMN);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Buscador de Coincidencias - RogsMediaTV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .search-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .search-form {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }

        .search-group {
            flex: 1;
            min-width: 300px;
        }

        .search-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--primary-color);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
        }

        .suggestions {
            margin-top: 1rem;
        }

        .suggestions h4 {
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .suggestion-tag {
            background: var(--primary-color);
            border: 1px solid var(--border-color);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-tag:hover {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .results-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .match-item {
            background: var(--primary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .match-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .match-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--accent-color);
            flex: 1;
        }

        .similarity-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 1rem;
        }

        .similarity-exact {
            background: rgba(40, 167, 69, 0.2);
            color: var(--success-color);
        }

        .similarity-high {
            background: rgba(255, 193, 7, 0.2);
            color: var(--warning-color);
        }

        .similarity-partial {
            background: rgba(23, 162, 184, 0.2);
            color: var(--info-color);
        }

        .match-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .meta-item {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .match-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>

        <div class="header">
            <h1><i class="fas fa-search"></i> Buscador de Coincidencias</h1>
            <p>Encuentra contenido disponible en tus listas M3U</p>
        </div>

        <!-- Formulario de búsqueda -->
        <div class="search-section">
            <form method="GET" class="search-form">
                <div class="search-group">
                    <label for="search">Título a buscar</label>
                    <input type="text" 
                           name="search" 
                           id="search" 
                           class="form-input" 
                           value="<?php echo htmlspecialchars($search_title); ?>" 
                           placeholder="Ej: Breaking Bad, Avengers, Game of Thrones..."
                           required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Buscar Coincidencias
                </button>
            </form>

            <?php if (!empty($recent_orders)): ?>
            <div class="suggestions">
                <h4>Pedidos recientes (haz clic para buscar):</h4>
                <div class="suggestion-tags">
                    <?php foreach (array_slice($recent_orders, 0, 10) as $order_title): ?>
                    <span class="suggestion-tag" onclick="searchTitle('<?php echo addslashes($order_title); ?>')">
                        <?php echo htmlspecialchars($order_title); ?>
                    </span>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Resultados -->
        <?php if ($search_title): ?>
        <div class="results-section">
            <div class="results-header">
                <h2>Resultados para: "<?php echo htmlspecialchars($search_title); ?>"</h2>
                <span><?php echo count($matches); ?> coincidencia(s) encontrada(s)</span>
            </div>

            <?php if (empty($matches)): ?>
            <div class="no-results">
                <i class="fas fa-search-minus"></i>
                <h3>No se encontraron coincidencias</h3>
                <p>Intenta con un título diferente o verifica que tengas listas M3U analizadas</p>
                <p style="margin-top: 1rem;">
                    <a href="m3u_content_viewer.php" class="btn btn-info">
                        <i class="fas fa-tv"></i>
                        Ver Todo el Contenido
                    </a>
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($matches as $match): ?>
            <div class="match-item">
                <div class="match-header">
                    <div class="match-title"><?php echo htmlspecialchars($match['content']['title']); ?></div>
                    <div class="similarity-badge similarity-<?php echo $match['match_type']; ?>">
                        <?php echo $match['similarity']; ?>% coincidencia
                    </div>
                </div>

                <div class="match-meta">
                    <div class="meta-item">
                        <i class="fas fa-tag"></i>
                        <?php 
                        echo $match['content']['media_type'] === 'movie' ? '🎬 Película' : 
                             ($match['content']['media_type'] === 'tv' ? '📺 Serie' : '❓ Desconocido'); 
                        ?>
                    </div>
                    
                    <?php if ($match['content']['year']): ?>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <?php echo $match['content']['year']; ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="meta-item">
                        <i class="fas fa-list"></i>
                        <?php echo htmlspecialchars($match['content']['list_name']); ?>
                    </div>
                    
                    <?php if ($match['content']['season']): ?>
                    <div class="meta-item">
                        <i class="fas fa-tv"></i>
                        Temporada <?php echo $match['content']['season']; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="match-actions">
                    <button class="btn btn-success btn-small" onclick="copyUrl('<?php echo addslashes($match['content']['url']); ?>')">
                        <i class="fas fa-copy"></i>
                        Copiar URL
                    </button>
                    
                    <a href="m3u_content_viewer.php?search=<?php echo urlencode($match['content']['title']); ?>" 
                       class="btn btn-info btn-small">
                        <i class="fas fa-eye"></i>
                        Ver Detalles
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <script>
        function searchTitle(title) {
            document.getElementById('search').value = title;
            document.querySelector('form').submit();
        }

        function copyUrl(url) {
            navigator.clipboard.writeText(url).then(function() {
                alert('URL copiada al portapapeles');
            }, function(err) {
                console.error('Error al copiar: ', err);
                // Fallback para navegadores más antiguos
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('URL copiada al portapapeles');
            });
        }
    </script>
</body>
</html>
