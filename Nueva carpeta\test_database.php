<?php
session_start();

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h1>Test de Base de Datos - RogsMediaTV</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }</style>";

try {
    echo "<h2>1. Conexión a la Base de Datos</h2>";
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✓ Conexión exitosa a la base de datos</p>";

    echo "<h2>2. Verificación de la tabla 'orders'</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✓ La tabla 'orders' existe</p>";
        
        // Mostrar estructura de la tabla
        echo "<h3>Estructura de la tabla 'orders':</h3>";
        $stmt = $pdo->query("DESCRIBE orders");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>";
        foreach ($columns as $column) {
            echo sprintf("%-20s %-20s %-10s %-10s %-20s %-10s\n", 
                $column['Field'], 
                $column['Type'], 
                $column['Null'], 
                $column['Key'], 
                $column['Default'], 
                $column['Extra']
            );
        }
        echo "</pre>";
        
    } else {
        echo "<p class='error'>✗ La tabla 'orders' no existe</p>";
        echo "<p class='info'>Creando tabla 'orders'...</p>";
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            tmdb_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            media_type VARCHAR(32) NOT NULL,
            year VARCHAR(8),
            country VARCHAR(64),
            city VARCHAR(64),
            ip_address VARCHAR(45),
            status VARCHAR(32) DEFAULT 'Recibido',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            notif_seen TINYINT(1) DEFAULT 0,
            status_notif_seen TINYINT(1) DEFAULT 0
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        
        echo "<p class='success'>✓ Tabla 'orders' creada exitosamente</p>";
    }

    echo "<h2>3. Verificación de la tabla 'users'</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✓ La tabla 'users' existe</p>";
        
        // Contar usuarios
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p class='info'>Número de usuarios registrados: " . $count['count'] . "</p>";
        
    } else {
        echo "<p class='error'>✗ La tabla 'users' no existe</p>";
    }

    echo "<h2>4. Verificación de pedidos existentes</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p class='info'>Número de pedidos en la base de datos: " . $count['count'] . "</p>";
    
    if ($count['count'] > 0) {
        echo "<h3>Últimos 5 pedidos:</h3>";
        $stmt = $pdo->query("SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id ORDER BY o.created_at DESC LIMIT 5");
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Usuario</th><th>Título</th><th>Tipo</th><th>Estado</th><th>Fecha</th></tr>";
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . ($order['username'] ?? 'Usuario #' . $order['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($order['title']) . "</td>";
            echo "<td>" . $order['media_type'] . "</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<h2>5. Test de inserción de pedido</h2>";
    
    // Simular un pedido de prueba
    $test_user_id = 1; // Asumiendo que existe un usuario con ID 1
    $test_tmdb_id = 12345;
    $test_title = "Película de Prueba";
    $test_media_type = "movie";
    $test_year = "2023";
    $test_country = "Test Country";
    $test_city = "Test City";
    $test_ip = "127.0.0.1";
    
    try {
        $stmt = $pdo->prepare("INSERT INTO orders (user_id, tmdb_id, title, media_type, year, country, city, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([
            $test_user_id,
            $test_tmdb_id,
            $test_title,
            $test_media_type,
            $test_year,
            $test_country,
            $test_city,
            $test_ip
        ]);
        
        if ($result) {
            $order_id = $pdo->lastInsertId();
            echo "<p class='success'>✓ Pedido de prueba insertado exitosamente con ID: $order_id</p>";
            
            // Eliminar el pedido de prueba
            $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$order_id]);
            echo "<p class='info'>Pedido de prueba eliminado</p>";
        } else {
            echo "<p class='error'>✗ Error al insertar pedido de prueba</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>✗ Error al insertar pedido de prueba: " . $e->getMessage() . "</p>";
    }

    echo "<h2>6. Verificación de permisos</h2>";
    
    // Verificar permisos de escritura
    try {
        $stmt = $pdo->query("SELECT 1");
        echo "<p class='success'>✓ Permisos de lectura: OK</p>";
        
        $pdo->exec("CREATE TEMPORARY TABLE test_temp (id INT)");
        echo "<p class='success'>✓ Permisos de escritura: OK</p>";
        
    } catch (PDOException $e) {
        echo "<p class='error'>✗ Error de permisos: " . $e->getMessage() . "</p>";
    }

} catch(PDOException $e) {
    echo "<p class='error'>✗ Error de conexión a la base de datos: " . $e->getMessage() . "</p>";
    echo "<p class='error'>Código de error: " . $e->getCode() . "</p>";
}

echo "<h2>7. Información del sistema</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? 'Disponible' : 'No disponible') . "</p>";
echo "<p><strong>Fecha/Hora del servidor:</strong> " . date('Y-m-d H:i:s') . "</p>";

echo "<hr>";
echo "<p><a href='index.php'>← Volver al índice</a></p>";
?>
