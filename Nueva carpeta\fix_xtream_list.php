<?php
// Script para corregir listas Xtream Codes mal configuradas
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

echo "<h1>🔧 Corregir Lista Xtream Codes</h1>";
echo "<style>body{font-family:Arial;margin:20px;background:#141414;color:white;} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .warning{color:#ffc107;} .section{background:#2d2d2d;padding:20px;border-radius:10px;margin:20px 0;border:1px solid #404040;} .form-group{margin:15px 0;} .form-group label{display:block;margin-bottom:5px;color:#b0b0b0;} .form-input{width:100%;padding:10px;border:1px solid #404040;border-radius:5px;background:#1a1a1a;color:white;} .btn{padding:10px 20px;border:none;border-radius:5px;cursor:pointer;margin:5px;} .btn-primary{background:#46d347;color:#1a1a1a;} .btn-warning{background:#ffc107;color:#1a1a1a;}</style>";

// Función para generar URL M3U de Xtream Codes
function generateXtreamM3uUrl($serverUrl, $username, $password) {
    $serverUrl = rtrim($serverUrl, '/');
    return $serverUrl . '/get.php?username=' . urlencode($username) . '&password=' . urlencode($password) . '&type=m3u_plus&output=ts';
}

// Función para probar URL Xtream
function testXtreamCredentials($serverUrl, $username, $password) {
    $testUrl = rtrim($serverUrl, '/') . '/player_api.php?username=' . urlencode($username) . '&password=' . urlencode($password);
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $response = @file_get_contents($testUrl, false, $context);
    
    if ($response === false) {
        return ['success' => false, 'error' => 'No se pudo conectar al servidor'];
    }
    
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['success' => false, 'error' => 'Respuesta no válida del servidor'];
    }
    
    if (isset($data['user_info'])) {
        return ['success' => true, 'data' => $data];
    }
    
    return ['success' => false, 'error' => 'Credenciales incorrectas'];
}

$message = '';
$message_type = '';

// Procesar corrección
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_list'])) {
    $list_id = $_POST['list_id'];
    $server_url = trim($_POST['server_url']);
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    
    if ($server_url && $username && $password) {
        // Probar credenciales
        $test_result = testXtreamCredentials($server_url, $username, $password);
        
        if ($test_result['success']) {
            // Generar URL M3U correcta
            $m3u_url = generateXtreamM3uUrl($server_url, $username, $password);
            
            try {
                // Actualizar la lista
                $stmt = $pdo->prepare("
                    UPDATE m3u_lists 
                    SET url = ?, server_url = ?, username = ?, password = ?, list_type = 'xtream_codes', last_updated = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$m3u_url, $server_url, $username, $password, $list_id]);
                
                $message = "Lista corregida exitosamente. URL M3U generada: " . $m3u_url;
                $message_type = "success";
                
            } catch (PDOException $e) {
                $message = "Error al actualizar la lista: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "Error al verificar credenciales: " . $test_result['error'];
            $message_type = "error";
        }
    } else {
        $message = "Por favor completa todos los campos";
        $message_type = "error";
    }
}

// Obtener listas problemáticas
$stmt = $pdo->query("
    SELECT * FROM m3u_lists 
    WHERE url LIKE '%:8080' OR url LIKE '%:8080/' 
    OR (url NOT LIKE '%.m3u%' AND url NOT LIKE '%get.php%')
    ORDER BY name
");
$problematic_lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<div class='section'>";
echo "<h2>🔍 Listas Detectadas con Problemas</h2>";

if (empty($problematic_lists)) {
    echo "<p class='success'>✅ No se detectaron listas con problemas de configuración</p>";
    echo "<p><a href='m3u_manager.php' style='color:#46d347;'>📡 Ir al Gestor M3U</a></p>";
} else {
    echo "<p class='warning'>⚠️ Se detectaron " . count($problematic_lists) . " lista(s) que parecen ser servidores Xtream Codes mal configurados:</p>";
    
    foreach ($problematic_lists as $list) {
        echo "<div style='background:#1a1a1a;padding:15px;margin:15px 0;border-radius:8px;border-left:4px solid #ffc107;'>";
        echo "<h3>" . htmlspecialchars($list['name']) . "</h3>";
        echo "<p><strong>URL actual:</strong> " . htmlspecialchars($list['url']) . "</p>";
        echo "<p><strong>Problema:</strong> Parece ser URL base de servidor Xtream Codes, no URL de M3U</p>";
        
        if ($list['id'] == ($_POST['list_id'] ?? null)) {
            // Mostrar formulario de corrección para esta lista
            echo "<form method='POST' style='margin-top:15px;'>";
            echo "<input type='hidden' name='list_id' value='" . $list['id'] . "'>";
            
            echo "<div class='form-group'>";
            echo "<label>URL del Servidor Xtream Codes:</label>";
            echo "<input type='url' name='server_url' class='form-input' value='" . htmlspecialchars($list['url']) . "' required>";
            echo "</div>";
            
            echo "<div class='form-group'>";
            echo "<label>Usuario:</label>";
            echo "<input type='text' name='username' class='form-input' value='" . htmlspecialchars($list['username'] ?? '') . "' required>";
            echo "</div>";
            
            echo "<div class='form-group'>";
            echo "<label>Contraseña:</label>";
            echo "<input type='password' name='password' class='form-input' value='" . htmlspecialchars($list['password'] ?? '') . "' required>";
            echo "</div>";
            
            echo "<button type='submit' name='fix_list' class='btn btn-primary'>🔧 Corregir Lista</button>";
            echo "<a href='?' class='btn' style='background:#666;color:white;text-decoration:none;'>❌ Cancelar</a>";
            echo "</form>";
        } else {
            echo "<a href='?list_id=" . $list['id'] . "' class='btn btn-warning'>🔧 Corregir Esta Lista</a>";
        }
        
        echo "</div>";
    }
}
echo "</div>";

if ($message) {
    echo "<div class='section'>";
    echo "<div class='$message_type'>" . htmlspecialchars($message) . "</div>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>💡 Información sobre Xtream Codes</h2>";
echo "<p><strong>URL del Servidor:</strong> La URL base como <code>http://servidor.com:8080</code></p>";
echo "<p><strong>URL M3U generada:</strong> <code>http://servidor.com:8080/get.php?username=USER&password=PASS&type=m3u_plus&output=ts</code></p>";
echo "<p><strong>Diferencia:</strong> La URL base muestra una página web, la URL M3U devuelve el archivo de listas</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🛠️ Herramientas</h2>";
echo "<p><a href='m3u_manager.php' style='color:#46d347;'>📡 Gestor M3U</a></p>";
echo "<p><a href='test_m3u_access.php' style='color:#46d347;'>🧪 Test de Acceso</a></p>";
echo "<p><a href='m3u_analyzer_debug.php' style='color:#46d347;'>🔍 Analizador Debug</a></p>";
echo "<p><a href='admin.php' style='color:#46d347;'>🏠 Panel Admin</a></p>";
echo "</div>";
?>
