<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

echo "<h1>🔍 Verificando Estructura de Base de Datos</h1>";

$tables_to_check = [
    'users',
    'support_tickets', 
    'ticket_responses',
    'chat_sessions',
    'chat_messages',
    'support_apps',
    'app_downloads',
    'admin_notifications',
    'chat_status'
];

foreach ($tables_to_check as $table) {
    echo "<h2>📋 Tabla: $table</h2>";
    
    try {
        // Verificar si la tabla existe
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            echo "<p style='color: red;'>❌ La tabla '$table' NO EXISTE</p>";
            continue;
        }
        
        // Mostrar estructura de la tabla
        $stmt = $pdo->query("DESCRIBE $table");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #333; color: white;'>";
        echo "<th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Contar registros
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>📊 Registros en la tabla: <strong>$count</strong></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<hr>";
}

?>
<style>
body {
    font-family: Arial, sans-serif;
    background: #1a1a1a;
    color: #fff;
    padding: 20px;
}
table {
    background: #2a2a2a;
    color: #fff;
}
th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #555;
}
</style>
