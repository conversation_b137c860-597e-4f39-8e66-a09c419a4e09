<?php
header('Content-Type: application/json');
session_start();

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    // Simular estadísticas de soporte (en el futuro se conectarán a la base de datos real)
    $stats = [
        'success' => true,
        'tickets_open' => rand(3, 8),
        'tickets_in_progress' => rand(1, 5),
        'tickets_resolved' => rand(10, 20),
        'chat_active' => rand(0, 5),
        'chat_waiting' => rand(0, 3),
        'chat_today' => rand(5, 15),
        'downloads_today' => rand(10, 30),
        'downloads_total' => rand(200, 500),
        'channels_pending' => rand(2, 8),
        'channels_approved_today' => rand(0, 5),
        'activations_pending' => rand(1, 6),
        'activations_completed_today' => rand(3, 12),
        'timestamp' => time()
    ];
    
    echo json_encode($stats);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
