<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_content':
                $title = $_POST['title'];
                $content = $_POST['content'];
                $category = $_POST['category'];
                $subcategory = $_POST['subcategory'];
                $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                
                $excerpt = substr(strip_tags($content), 0, 200) . '...';
                $stmt = $pdo->prepare("INSERT INTO help_articles (title, content, excerpt, category, subcategory, is_featured, status) VALUES (?, ?, ?, ?, ?, ?, 'published')");
                $stmt->execute([$title, $content, $excerpt, $category, $subcategory, $is_featured]);
                
                $success_message = "Contenido creado correctamente";
                break;
                
            case 'update_content':
                $id = (int)$_POST['content_id'];
                $title = $_POST['title'];
                $content = $_POST['content'];
                $category = $_POST['category'];
                $subcategory = $_POST['subcategory'];
                $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                $status = isset($_POST['is_published']) && $_POST['is_published'] ? 'published' : 'draft';
                $excerpt = substr(strip_tags($content), 0, 200) . '...';

                $stmt = $pdo->prepare("UPDATE help_articles SET title = ?, content = ?, excerpt = ?, category = ?, subcategory = ?, is_featured = ?, status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$title, $content, $excerpt, $category, $subcategory, $is_featured, $status, $id]);
                
                $success_message = "Contenido actualizado correctamente";
                break;
                
            case 'delete_content':
                $id = (int)$_POST['content_id'];
                
                $stmt = $pdo->prepare("DELETE FROM help_articles WHERE id = ?");
                $stmt->execute([$id]);
                
                $success_message = "Contenido eliminado correctamente";
                break;
                
            case 'toggle_featured':
                $id = (int)$_POST['content_id'];
                
                $stmt = $pdo->prepare("UPDATE help_articles SET is_featured = NOT is_featured WHERE id = ?");
                $stmt->execute([$id]);
                
                $success_message = "Estado destacado actualizado";
                break;
                
            case 'toggle_published':
                $id = (int)$_POST['content_id'];
                
                $stmt = $pdo->prepare("UPDATE help_articles SET status = CASE WHEN status = 'published' THEN 'draft' ELSE 'published' END WHERE id = ?");
                $stmt->execute([$id]);
                
                $success_message = "Estado de publicación actualizado";
                break;
        }
    }
}

// Obtener filtros
$category_filter = $_GET['category'] ?? 'all';
$status_filter = $_GET['status'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($category_filter !== 'all') {
    $where_conditions[] = "category = ?";
    $params[] = $category_filter;
}

if ($status_filter !== 'all') {
    if ($status_filter === 'published') {
        $where_conditions[] = "status = 'published'";
    } elseif ($status_filter === 'draft') {
        $where_conditions[] = "status = 'draft'";
    } elseif ($status_filter === 'featured') {
        $where_conditions[] = "is_featured = 1";
    }
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener contenido
$stmt = $pdo->prepare("
    SELECT * FROM help_articles
    $where_clause
    ORDER BY is_featured DESC, view_count DESC, created_at DESC
");
$stmt->execute($params);
$content_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
        SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured,
        SUM(view_count) as total_views,
        COUNT(CASE WHEN category = 'faq' THEN 1 END) as faq_count,
        COUNT(CASE WHEN category = 'tutorial' THEN 1 END) as tutorial_count
    FROM help_articles
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Obtener contenido para editar si se especifica
$edit_content = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $stmt = $pdo->prepare("SELECT * FROM help_articles WHERE id = ?");
    $stmt->execute([(int)$_GET['edit']]);
    $edit_content = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>❓ Centro de Ayuda - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .filters {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .content-form {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            display: none;
        }

        .content-form.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .form-textarea {
            min-height: 200px;
            resize: vertical;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 2rem;
        }

        .content-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
        }

        .content-header {
            padding: 1.5rem;
            background: var(--dark-bg);
        }

        .content-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .content-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .content-body {
            padding: 1.5rem;
        }

        .content-preview {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .content-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .content-stat {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .content-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .category-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .category-faq { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
        .category-tutorial { background: rgba(16, 185, 129, 0.2); color: #10b981; }
        .category-guide { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .category-troubleshooting { background: rgba(239, 68, 68, 0.2); color: #ef4444; }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-published { background: rgba(16, 185, 129, 0.2); color: #10b981; }
        .status-draft { background: rgba(107, 114, 128, 0.2); color: #6b7280; }

        .featured-badge {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="help_admin.php" class="logo">
                <i class="fas fa-question-circle"></i>
                <span>Centro de Ayuda</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-question-circle"></i>
                Centro de Ayuda
            </h1>
            
            <button onclick="toggleContentForm()" class="btn btn-success">
                <i class="fas fa-plus"></i>
                Crear Contenido
            </button>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Artículos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['published']; ?></div>
                <div class="stat-label">Publicados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['featured']; ?></div>
                <div class="stat-label">Destacados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total_views']); ?></div>
                <div class="stat-label">Visualizaciones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['faq_count']; ?></div>
                <div class="stat-label">FAQ</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['tutorial_count']; ?></div>
                <div class="stat-label">Tutoriales</div>
            </div>
        </div>

        <!-- Formulario de Contenido -->
        <div id="contentForm" class="content-form <?php echo $edit_content ? 'active' : ''; ?>">
            <h2 style="margin-bottom: 1.5rem; color: var(--text-primary);">
                <i class="fas fa-<?php echo $edit_content ? 'edit' : 'plus'; ?>"></i>
                <?php echo $edit_content ? 'Editar Contenido' : 'Crear Nuevo Contenido'; ?>
            </h2>

            <form method="POST">
                <input type="hidden" name="action" value="<?php echo $edit_content ? 'update_content' : 'create_content'; ?>">
                <?php if ($edit_content): ?>
                <input type="hidden" name="content_id" value="<?php echo $edit_content['id']; ?>">
                <?php endif; ?>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Título</label>
                        <input type="text" name="title" class="form-input" required
                               value="<?php echo htmlspecialchars($edit_content['title'] ?? ''); ?>"
                               placeholder="Título del artículo">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Categoría</label>
                        <select name="category" class="form-select" required>
                            <option value="">Seleccionar categoría</option>
                            <option value="faq" <?php echo ($edit_content['category'] ?? '') === 'faq' ? 'selected' : ''; ?>>FAQ</option>
                            <option value="tutorial" <?php echo ($edit_content['category'] ?? '') === 'tutorial' ? 'selected' : ''; ?>>Tutorial</option>
                            <option value="guide" <?php echo ($edit_content['category'] ?? '') === 'guide' ? 'selected' : ''; ?>>Guía</option>
                            <option value="troubleshooting" <?php echo ($edit_content['category'] ?? '') === 'troubleshooting' ? 'selected' : ''; ?>>Solución de Problemas</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Subcategoría</label>
                        <input type="text" name="subcategory" class="form-input"
                               value="<?php echo htmlspecialchars($edit_content['subcategory'] ?? ''); ?>"
                               placeholder="Ej: configuracion, streaming, android">
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">Contenido</label>
                    <textarea name="content" class="form-textarea" required
                              placeholder="Escribe el contenido del artículo aquí..."><?php echo htmlspecialchars($edit_content['content'] ?? ''); ?></textarea>
                </div>

                <div style="display: flex; gap: 2rem; margin-bottom: 2rem;">
                    <div class="form-checkbox">
                        <input type="checkbox" name="is_featured" id="is_featured"
                               <?php echo ($edit_content['is_featured'] ?? 0) ? 'checked' : ''; ?>>
                        <label for="is_featured">Artículo destacado</label>
                    </div>

                    <?php if ($edit_content): ?>
                    <div class="form-checkbox">
                        <input type="checkbox" name="is_published" id="is_published"
                               <?php echo ($edit_content['status'] ?? 'published') === 'published' ? 'checked' : ''; ?>>
                        <label for="is_published">Publicado</label>
                    </div>
                    <?php endif; ?>
                </div>

                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i>
                        <?php echo $edit_content ? 'Actualizar' : 'Crear'; ?> Contenido
                    </button>
                    <button type="button" onclick="cancelEdit()" class="btn btn-warning">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Categoría</label>
                    <select name="category" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="faq" <?php echo $category_filter === 'faq' ? 'selected' : ''; ?>>FAQ</option>
                        <option value="tutorial" <?php echo $category_filter === 'tutorial' ? 'selected' : ''; ?>>Tutoriales</option>
                        <option value="guide" <?php echo $category_filter === 'guide' ? 'selected' : ''; ?>>Guías</option>
                        <option value="troubleshooting" <?php echo $category_filter === 'troubleshooting' ? 'selected' : ''; ?>>Solución de Problemas</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="published" <?php echo $status_filter === 'published' ? 'selected' : ''; ?>>Publicados</option>
                        <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>Borradores</option>
                        <option value="featured" <?php echo $status_filter === 'featured' ? 'selected' : ''; ?>>Destacados</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Lista de Contenido -->
        <div class="content-grid">
            <?php if (empty($content_items)): ?>
            <div style="grid-column: 1 / -1; text-align: center; color: var(--text-secondary); padding: 3rem;">
                <i class="fas fa-file-alt" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No hay contenido que coincida con los filtros seleccionados</p>
            </div>
            <?php else: ?>
                <?php foreach ($content_items as $item): ?>
                <div class="content-card">
                    <div class="content-header">
                        <div class="content-title">
                            <?php echo htmlspecialchars($item['title']); ?>
                        </div>
                        <div class="content-meta">
                            <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($item['created_at'])); ?></span>
                            <span><i class="fas fa-eye"></i> <?php echo number_format($item['view_count']); ?> vistas</span>
                            <?php if ($item['updated_at'] !== $item['created_at']): ?>
                            <span><i class="fas fa-edit"></i> Editado <?php echo date('d/m/Y', strtotime($item['updated_at'])); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="content-body">
                        <div style="display: flex; gap: 0.5rem; margin-bottom: 1rem; flex-wrap: wrap;">
                            <span class="category-badge category-<?php echo $item['category']; ?>">
                                <?php
                                $category_labels = [
                                    'faq' => 'FAQ',
                                    'tutorial' => 'Tutorial',
                                    'guide' => 'Guía',
                                    'troubleshooting' => 'Solución de Problemas'
                                ];
                                echo $category_labels[$item['category']] ?? ucfirst($item['category']);
                                ?>
                            </span>

                            <span class="status-badge status-<?php echo $item['status']; ?>">
                                <?php echo $item['status'] === 'published' ? 'Publicado' : 'Borrador'; ?>
                            </span>

                            <?php if ($item['is_featured']): ?>
                            <span class="featured-badge">
                                <i class="fas fa-star"></i>
                                Destacado
                            </span>
                            <?php endif; ?>
                        </div>

                        <?php if ($item['subcategory']): ?>
                        <div style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;">
                            <i class="fas fa-tag"></i> <?php echo htmlspecialchars($item['subcategory']); ?>
                        </div>
                        <?php endif; ?>

                        <div class="content-preview">
                            <?php echo nl2br(htmlspecialchars(substr($item['content'], 0, 200))); ?>
                            <?php if (strlen($item['content']) > 200): ?>...<?php endif; ?>
                        </div>

                        <div class="content-stats">
                            <div class="content-stat">
                                <i class="fas fa-eye"></i>
                                <span><?php echo number_format($item['view_count']); ?> visualizaciones</span>
                            </div>
                            <div class="content-stat">
                                <i class="fas fa-clock"></i>
                                <span><?php echo date('d/m/Y H:i', strtotime($item['created_at'])); ?></span>
                            </div>
                        </div>

                        <div class="content-actions">
                            <a href="?edit=<?php echo $item['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-edit"></i>
                                Editar
                            </a>

                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_featured">
                                <input type="hidden" name="content_id" value="<?php echo $item['id']; ?>">
                                <button type="submit" class="btn <?php echo $item['is_featured'] ? 'btn-warning' : 'btn-success'; ?>">
                                    <i class="fas fa-star"></i>
                                    <?php echo $item['is_featured'] ? 'Quitar Destacado' : 'Destacar'; ?>
                                </button>
                            </form>

                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_published">
                                <input type="hidden" name="content_id" value="<?php echo $item['id']; ?>">
                                <button type="submit" class="btn <?php echo $item['status'] === 'published' ? 'btn-warning' : 'btn-success'; ?>">
                                    <i class="fas fa-<?php echo $item['status'] === 'published' ? 'eye-slash' : 'eye'; ?>"></i>
                                    <?php echo $item['status'] === 'published' ? 'Despublicar' : 'Publicar'; ?>
                                </button>
                            </form>

                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="delete_content">
                                <input type="hidden" name="content_id" value="<?php echo $item['id']; ?>">
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('¿Estás seguro de que quieres eliminar este contenido?')">
                                    <i class="fas fa-trash"></i>
                                    Eliminar
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>

    <script>
        function toggleContentForm() {
            const form = document.getElementById('contentForm');
            form.classList.toggle('active');

            if (form.classList.contains('active')) {
                form.scrollIntoView({ behavior: 'smooth' });
                // Focus en el primer input
                const firstInput = form.querySelector('input[type="text"]');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 300);
                }
            }
        }

        function cancelEdit() {
            // Limpiar URL de parámetros de edición
            const url = new URL(window.location);
            url.searchParams.delete('edit');
            window.history.replaceState({}, '', url);

            // Ocultar formulario
            document.getElementById('contentForm').classList.remove('active');

            // Limpiar formulario
            const form = document.querySelector('#contentForm form');
            form.reset();
            form.querySelector('input[name="action"]').value = 'create_content';

            // Actualizar título del formulario
            const title = form.parentElement.querySelector('h2');
            title.innerHTML = '<i class="fas fa-plus"></i> Crear Nuevo Contenido';
        }

        // Auto-resize textarea
        const textarea = document.querySelector('.form-textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });

            // Trigger inicial
            textarea.style.height = textarea.scrollHeight + 'px';
        }

        // Confirmación para acciones críticas
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'delete_content') {
                    if (!confirm('¿Estás seguro de que quieres eliminar este contenido? Esta acción no se puede deshacer.')) {
                        e.preventDefault();
                    }
                }
            });
        });

        // Mostrar formulario si hay contenido para editar
        <?php if ($edit_content): ?>
        document.getElementById('contentForm').scrollIntoView({ behavior: 'smooth' });
        <?php endif; ?>

        // Contador de caracteres para el textarea
        const contentTextarea = document.querySelector('textarea[name="content"]');
        if (contentTextarea) {
            const counter = document.createElement('div');
            counter.style.cssText = 'text-align: right; color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.5rem;';
            contentTextarea.parentElement.appendChild(counter);

            function updateCounter() {
                const length = contentTextarea.value.length;
                counter.textContent = `${length} caracteres`;

                if (length < 100) {
                    counter.style.color = 'var(--error-color)';
                } else if (length < 300) {
                    counter.style.color = 'var(--warning-color)';
                } else {
                    counter.style.color = 'var(--success-color)';
                }
            }

            contentTextarea.addEventListener('input', updateCounter);
            updateCounter(); // Inicial
        }
    </script>
</body>
</html>
