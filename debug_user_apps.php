<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔧 Debug User Apps</h1>";

try {
    // 1. Verificar directorio apploader
    echo "<h2>📁 Verificando directorio apploader...</h2>";
    
    $apploader_dir = __DIR__ . '/apploader';
    if (is_dir($apploader_dir)) {
        $success_messages[] = "✅ Directorio apploader existe: $apploader_dir";
        
        // Listar archivos
        $files = glob($apploader_dir . '/*.{apk,ipa,exe,dmg,deb,zip,msi}', GLOB_BRACE);
        $success_messages[] = "ℹ️ Archivos encontrados: " . count($files);
        
        foreach ($files as $file) {
            $filename = basename($file);
            $size = filesize($file);
            $success_messages[] = "📱 $filename (" . formatBytes($size) . ")";
        }
        
        if (empty($files)) {
            $error_messages[] = "❌ No hay archivos APK/IPA/EXE en el directorio apploader";
        }
        
    } else {
        $error_messages[] = "❌ Directorio apploader no existe";
    }
    
    // 2. Verificar base de datos
    echo "<h2>🗄️ Verificando base de datos...</h2>";
    
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'support_apps'");
        if ($stmt->rowCount() > 0) {
            $success_messages[] = "✅ Tabla support_apps existe";
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps");
            $app_count = $stmt->fetchColumn();
            $success_messages[] = "ℹ️ Aplicaciones en BD: $app_count";
            
            if ($app_count > 0) {
                $stmt = $pdo->query("SELECT name, version, platform, status FROM support_apps LIMIT 5");
                $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($apps as $app) {
                    $success_messages[] = "📱 BD: {$app['name']} v{$app['version']} ({$app['platform']}) - {$app['status']}";
                }
            }
            
        } else {
            $error_messages[] = "❌ Tabla support_apps no existe";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error verificando BD: " . $e->getMessage();
    }
    
    // 3. Simular lógica de user_apps.php
    echo "<h2>🔄 Simulando lógica de user_apps.php...</h2>";
    
    // Obtener aplicaciones de la base de datos
    $apps_db = [];
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM support_apps
            WHERE status = 'active' OR status IS NULL
            ORDER BY platform, name
        ");
        $stmt->execute();
        $apps_db = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $success_messages[] = "✅ Apps de BD cargadas: " . count($apps_db);
    } catch (Exception $e) {
        $error_messages[] = "❌ Error cargando apps de BD: " . $e->getMessage();
    }
    
    // Obtener archivos APK del directorio apploader
    $apps_files = [];
    if (is_dir($apploader_dir)) {
        $files = glob($apploader_dir . '/*.{apk,ipa,exe,dmg,deb,zip,msi}', GLOB_BRACE);
        foreach ($files as $file) {
            $filename = basename($file);
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $size = filesize($file);
            
            // Determinar plataforma por extensión
            $platform = 'other';
            switch ($extension) {
                case 'apk':
                    $platform = 'android';
                    break;
                case 'ipa':
                    $platform = 'ios';
                    break;
                case 'exe':
                case 'msi':
                    $platform = 'windows';
                    break;
                case 'dmg':
                    $platform = 'macos';
                    break;
            }
            
            $apps_files[] = [
                'id' => 'file_' . md5($filename),
                'name' => pathinfo($filename, PATHINFO_FILENAME),
                'version' => 'Última versión',
                'platform' => $platform,
                'file_path' => 'apploader/' . $filename,
                'file_size' => $size,
                'description' => 'Aplicación IPTV para ' . ucfirst($platform),
                'features' => 'Reproducción IPTV, EPG, Favoritos',
                'download_url' => 'apploader/' . $filename,
                'status' => 'active',
                'source' => 'file'
            ];
        }
        $success_messages[] = "✅ Apps de archivos cargadas: " . count($apps_files);
    }
    
    // Combinar aplicaciones
    $apps = array_merge($apps_db, $apps_files);
    $success_messages[] = "✅ Total de apps disponibles: " . count($apps);
    
    // Agrupar por plataforma
    $apps_by_platform = [];
    foreach ($apps as $app) {
        $apps_by_platform[$app['platform']][] = $app;
    }
    
    $success_messages[] = "ℹ️ Plataformas con apps: " . implode(', ', array_keys($apps_by_platform));
    
    // 4. Verificar URLs de descarga
    echo "<h2>🔗 Verificando URLs de descarga...</h2>";
    
    foreach ($apps as $app) {
        if (!empty($app['file_path'])) {
            if (file_exists($app['file_path'])) {
                $success_messages[] = "✅ Archivo existe: {$app['file_path']}";
            } else {
                $error_messages[] = "❌ Archivo no existe: {$app['file_path']}";
            }
        }
        
        if (!empty($app['download_url'])) {
            $success_messages[] = "🔗 URL de descarga: {$app['download_url']}";
        }
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

function formatBytes($size, $precision = 2) {
    if ($size == 0) return '0 B';
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug User Apps - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug User Apps Completado</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Estado Correcto</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Problemas Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="user_apps.php" class="btn">📱 User Apps</a>
            <a href="apps_admin.php" class="btn">⚙️ Apps Admin</a>
            <a href="setup_apploader.php" class="btn">🔧 Setup Apploader</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
        
        <?php if (count($error_messages) == 0): ?>
        <div style="background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; border-radius: 12px; padding: 2rem; margin: 2rem 0; text-align: center;">
            <h2 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Todo Funcionando!</h2>
            <p style="color: #10b981; font-size: 1.1rem;">
                Las aplicaciones deberían mostrarse correctamente en user_apps.php
            </p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
