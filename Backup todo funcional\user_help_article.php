<?php
session_start();
require_once 'config.php';

// Obtener ID del artículo
$article_id = (int)($_GET['id'] ?? 0);

if (!$article_id) {
    header('Location: user_help.php');
    exit;
}

// Obtener artículo directamente de la base de datos
$article = null;
try {
    $stmt = $pdo->prepare("
        SELECT * FROM help_articles
        WHERE id = ? AND status = 'published'
    ");
    $stmt->execute([$article_id]);
    $article = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($article) {
        // Incrementar contador de vistas
        $stmt = $pdo->prepare("UPDATE help_articles SET view_count = view_count + 1 WHERE id = ?");
        $stmt->execute([$article_id]);
    }
} catch (Exception $e) {
    // Error de base de datos
    $error = $e->getMessage();
}

if (!$article) {
    header('Location: user_help.php?error=article_not_found');
    exit;
}

// Obtener artículos relacionados
$related_articles = [];
try {
    $stmt = $pdo->prepare("
        SELECT id, title, excerpt, view_count
        FROM help_articles 
        WHERE category = ? AND id != ? AND status = 'published'
        ORDER BY view_count DESC, created_at DESC
        LIMIT 3
    ");
    $stmt->execute([$article['category'], $article_id]);
    $related_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Sin artículos relacionados
}

// Procesar feedback si se envía
$feedback_sent = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_feedback'])) {
    $rating = (int)($_POST['rating'] ?? 0);
    $feedback_text = htmlspecialchars(strip_tags(trim($_POST['feedback'] ?? '')));

    if ($rating >= 1 && $rating <= 5) {
        try {
            // Crear tabla de feedback si no existe
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS help_feedback (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    article_id INT NOT NULL,
                    rating INT NOT NULL,
                    feedback TEXT,
                    user_id INT NULL,
                    ip_address VARCHAR(45),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");

            $user_id = $_SESSION['user_id'] ?? null;
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';

            $stmt = $pdo->prepare("INSERT INTO help_feedback (article_id, rating, feedback, user_id, ip_address) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$article_id, $rating, $feedback_text, $user_id, $ip_address]);

            $feedback_sent = true;
        } catch (Exception $e) {
            // Error enviando feedback
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['title']); ?> - Centro de Ayuda RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --dark-bg: #0f172a;
            --border-color: #334155;
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, #020617 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
        }

        .back-button {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .article-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .category-badge {
            background: rgba(37, 99, 235, 0.2);
            color: var(--primary-color);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .subcategory-badge {
            background: rgba(16, 185, 129, 0.2);
            color: var(--accent-color);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .article-stats {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .article-title {
            font-size: 2.5rem;
            color: var(--text-primary);
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 2rem;
            align-items: start;
        }

        .article-content {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
        }

        .content-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-secondary);
            white-space: pre-wrap;
        }

        .content-text h1, .content-text h2, .content-text h3 {
            color: var(--text-primary);
            margin: 2rem 0 1rem 0;
        }

        .content-text strong {
            color: var(--text-primary);
            font-weight: 600;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .feedback-section {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
        }

        .feedback-title {
            color: var(--text-primary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .rating-display {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .stars {
            display: flex;
            gap: 0.2rem;
        }

        .star {
            font-size: 1.2rem;
            color: #6b7280;
            cursor: pointer;
            transition: color 0.2s;
        }

        .star.active, .star:hover {
            color: #f59e0b;
        }

        .rating-form {
            margin-top: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .btn.success {
            background: var(--accent-color);
        }

        .related-articles {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
        }

        .related-title {
            color: var(--text-primary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .related-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.2s;
        }

        .related-item:last-child {
            border-bottom: none;
        }

        .related-item:hover {
            background: var(--dark-bg);
        }

        .related-link {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            display: block;
            margin-bottom: 0.5rem;
        }

        .related-excerpt {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--accent-color);
            color: var(--accent-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .article-title {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .back-button {
                position: static;
                margin-bottom: 1rem;
                display: inline-block;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="user_help.php" class="back-button">
                <i class="fas fa-arrow-left"></i> Volver al Centro de Ayuda
            </a>
            
            <div class="article-meta">
                <span class="category-badge"><?php echo ucfirst($article['category']); ?></span>
                <?php if (!empty($article['subcategory'])): ?>
                <span class="subcategory-badge"><?php echo ucfirst($article['subcategory']); ?></span>
                <?php endif; ?>
                <span class="article-stats">
                    <i class="fas fa-eye"></i> <?php echo number_format($article['view_count']); ?> vistas
                </span>
                <span class="article-stats">
                    <i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($article['created_at'])); ?>
                </span>
                <?php if ($feedback_stats && $feedback_stats['total_feedback'] > 0): ?>
                <span class="article-stats">
                    <i class="fas fa-star" style="color: #f59e0b;"></i> 
                    <?php echo number_format($feedback_stats['avg_rating'], 1); ?>/5 
                    (<?php echo $feedback_stats['total_feedback']; ?> valoraciones)
                </span>
                <?php endif; ?>
            </div>
            
            <h1 class="article-title"><?php echo htmlspecialchars($article['title']); ?></h1>
        </div>

        <div class="main-content">
            <div class="article-content">
                <div class="content-text">
                    <?php echo nl2br(htmlspecialchars($article['content'])); ?>
                </div>
            </div>

            <div class="sidebar">
                <!-- Sección de Feedback -->
                <div class="feedback-section">
                    <h3 class="feedback-title">¿Te fue útil este artículo?</h3>
                    
                    <?php if ($feedback_sent): ?>
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        ¡Gracias por tu feedback! Nos ayuda a mejorar.
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($feedback_stats && $feedback_stats['total_feedback'] > 0): ?>
                    <div class="rating-display">
                        <div class="stars">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <span class="star <?php echo $i <= round($feedback_stats['avg_rating']) ? 'active' : ''; ?>">★</span>
                            <?php endfor; ?>
                        </div>
                        <span><?php echo number_format($feedback_stats['avg_rating'], 1); ?>/5</span>
                        <span class="article-stats">(<?php echo $feedback_stats['total_feedback']; ?> valoraciones)</span>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="rating-form">
                        <div class="form-group">
                            <label class="form-label">Tu calificación:</label>
                            <div class="stars" id="rating-stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                <span class="star" data-rating="<?php echo $i; ?>">★</span>
                                <?php endfor; ?>
                            </div>
                            <input type="hidden" name="rating" id="rating-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="feedback" class="form-label">Comentarios (opcional):</label>
                            <textarea name="feedback" id="feedback" class="form-textarea" 
                                      placeholder="Comparte tu experiencia o sugerencias..."></textarea>
                        </div>
                        
                        <button type="submit" name="submit_feedback" class="btn">
                            <i class="fas fa-paper-plane"></i> Enviar Feedback
                        </button>
                    </form>
                </div>

                <!-- Artículos Relacionados -->
                <?php if (!empty($related_articles)): ?>
                <div class="related-articles">
                    <h3 class="related-title">Artículos Relacionados</h3>
                    <?php foreach ($related_articles as $related): ?>
                    <div class="related-item">
                        <a href="user_help_article.php?id=<?php echo $related['id']; ?>" class="related-link">
                            <?php echo htmlspecialchars($related['title']); ?>
                        </a>
                        <?php if (!empty($related['excerpt'])): ?>
                        <p class="related-excerpt"><?php echo htmlspecialchars($related['excerpt']); ?></p>
                        <?php endif; ?>
                        <div class="article-stats">
                            <i class="fas fa-eye"></i> <?php echo number_format($related['view_count']); ?> vistas
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Manejo de calificación con estrellas
        const stars = document.querySelectorAll('#rating-stars .star');
        const ratingInput = document.getElementById('rating-input');
        
        stars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                ratingInput.value = rating;
                
                stars.forEach((s, index) => {
                    s.classList.toggle('active', index < rating);
                });
            });
            
            star.addEventListener('mouseover', function() {
                const rating = parseInt(this.dataset.rating);
                stars.forEach((s, index) => {
                    s.style.color = index < rating ? '#f59e0b' : '#6b7280';
                });
            });
        });
        
        document.getElementById('rating-stars').addEventListener('mouseleave', function() {
            const currentRating = parseInt(ratingInput.value) || 0;
            stars.forEach((s, index) => {
                s.style.color = index < currentRating ? '#f59e0b' : '#6b7280';
            });
        });
    </script>
</body>
</html>
