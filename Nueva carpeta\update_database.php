<?php
// Script para actualizar la base de datos con las nuevas columnas necesarias
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Conectado a la base de datos...\n";
    
    // Verificar y agregar columnas a la tabla orders
    $columns_to_add = [
        'updated_at' => 'DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'status_notif_seen' => 'TINYINT(1) DEFAULT 0',
        'notif_seen' => 'TINYINT(1) DEFAULT 0'
    ];
    
    foreach ($columns_to_add as $column => $definition) {
        // Verificar si la columna existe
        $stmt = $pdo->prepare("SHOW COLUMNS FROM orders LIKE ?");
        $stmt->execute([$column]);
        
        if ($stmt->rowCount() == 0) {
            // La columna no existe, agregarla
            $sql = "ALTER TABLE orders ADD COLUMN $column $definition";
            $pdo->exec($sql);
            echo "Columna '$column' agregada a la tabla orders.\n";
        } else {
            echo "Columna '$column' ya existe en la tabla orders.\n";
        }
    }
    
    // Crear tabla de historial si no existe
    $pdo->exec("CREATE TABLE IF NOT EXISTS orders_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        original_order_id INT,
        user_id INT,
        tmdb_id INT,
        title VARCHAR(255),
        media_type VARCHAR(32),
        year VARCHAR(8),
        country VARCHAR(64),
        city VARCHAR(64),
        ip_address VARCHAR(45),
        status VARCHAR(32),
        created_at DATETIME,
        updated_at DATETIME,
        archived_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        notif_seen TINYINT(1) DEFAULT 1
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    
    echo "Tabla orders_history creada o verificada.\n";
    
    echo "Base de datos actualizada correctamente.\n";
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
