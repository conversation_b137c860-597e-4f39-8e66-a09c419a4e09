<?php
session_start();

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// NO hacer consultas a la base de datos aquí - todo se carga vía JavaScript
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎧 Admin Soporte - RGS TOOL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --support-color: #e91e63;
            --border-color: #334155;
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
            border: 1px solid var(--primary-color);
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 16px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background: rgba(233, 30, 99, 0.2);
            color: var(--support-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: var(--secondary-color);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            border-color: var(--primary-color);
        }

        .service-header {
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
            text-align: center;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            backdrop-filter: blur(10px);
        }

        .service-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }

        .service-description {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .service-content {
            padding: 2rem;
        }

        .service-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .service-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .service-btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .service-btn.secondary {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .service-btn.secondary:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--primary-color);
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="admin2_fast.php" class="logo">
                <i class="fas fa-headset"></i>
                <span>Admin Soporte</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Admin</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
                <a href="admin_logout.php" class="nav-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-headset"></i>
                Panel de Administración de Soporte
            </h1>
            <p class="page-subtitle">
                Gestiona tickets, chats, aplicaciones, canales y activaciones de listas
            </p>
        </div>

        <!-- Stats Section -->
        <section class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                </div>
                <div class="stat-number" id="tickets-open">0</div>
                <div class="stat-label">Tickets Abiertos</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
                <div class="stat-number" id="chat-active">0</div>
                <div class="stat-label">Chats Activos</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                </div>
                <div class="stat-number" id="downloads-today">0</div>
                <div class="stat-label">Descargas Hoy</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-tv"></i>
                    </div>
                </div>
                <div class="stat-number" id="channels-pending">0</div>
                <div class="stat-label">Canales Pendientes</div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="services-grid">
            <!-- Gestión de Tickets -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h3 class="service-title">Gestión de Tickets</h3>
                    <p class="service-description">Administra tickets de soporte técnico</p>
                </div>
                <div class="service-content">
                    <div class="service-actions">
                        <a href="tickets_admin.php" class="service-btn">
                            <i class="fas fa-list"></i>
                            Ver Todos los Tickets
                        </a>
                        <a href="tickets_admin.php?status=open" class="service-btn secondary">
                            <i class="fas fa-exclamation-circle"></i>
                            Tickets Abiertos (<span id="tickets-open-count">0</span>)
                        </a>
                    </div>
                </div>
            </div>

            <!-- Chat en Tiempo Real -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="service-title">Chat en Tiempo Real</h3>
                    <p class="service-description">Atención al cliente en vivo</p>
                </div>
                <div class="service-content">
                    <div class="service-actions">
                        <a href="admin_chat_real.php" class="service-btn">
                            <i class="fas fa-comment-dots"></i>
                            Chat en Tiempo Real
                        </a>
                        <a href="admin_chat_real.php" class="service-btn secondary">
                            <i class="fas fa-circle" style="color: #10b981;"></i>
                            Sesiones Activas (<span id="chat-active-count">0</span>)
                        </a>
                    </div>
                </div>
            </div>

            <!-- Gestión de Aplicaciones -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="service-title">Gestión de Aplicaciones</h3>
                    <p class="service-description">Administra descargas de apps</p>
                </div>
                <div class="service-content">
                    <div class="service-actions">
                        <a href="apps_admin.php" class="service-btn">
                            <i class="fas fa-download"></i>
                            Gestionar Aplicaciones
                        </a>
                        <a href="apps_admin.php" class="service-btn secondary">
                            <i class="fas fa-chart-line"></i>
                            Descargas Hoy (<span id="downloads-count">0</span>)
                        </a>
                    </div>
                </div>
            </div>

            <!-- Solicitudes de Canales -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-tv"></i>
                    </div>
                    <h3 class="service-title">Solicitudes de Canales</h3>
                    <p class="service-description">Gestiona peticiones de canales</p>
                </div>
                <div class="service-content">
                    <div class="service-actions">
                        <a href="admin_channels_real.php" class="service-btn">
                            <i class="fas fa-satellite-dish"></i>
                            Gestionar Canales
                        </a>
                        <a href="admin_channels_real.php" class="service-btn secondary">
                            <i class="fas fa-clock"></i>
                            Pendientes (<span id="channels-count">0</span>)
                        </a>
                    </div>
                </div>
            </div>

            <!-- Centro de Ayuda -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3 class="service-title">Centro de Ayuda</h3>
                    <p class="service-description">Gestiona documentación y FAQ</p>
                </div>
                <div class="service-content">
                    <div class="service-actions">
                        <a href="help_admin.php" class="service-btn">
                            <i class="fas fa-book"></i>
                            Gestionar Ayuda
                        </a>
                        <a href="help_admin.php" class="service-btn secondary">
                            <i class="fas fa-plus"></i>
                            Agregar Contenido
                        </a>
                    </div>
                </div>
            </div>

            <!-- Activaciones de Listas -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h3 class="service-title">Activaciones de Listas</h3>
                    <p class="service-description">Gestiona códigos de activación</p>
                </div>
                <div class="service-content">
                    <div class="service-actions">
                        <a href="list_activation_admin.php" class="service-btn">
                            <i class="fas fa-unlock-alt"></i>
                            Gestionar Activaciones
                        </a>
                        <a href="list_activation_admin.php" class="service-btn secondary">
                            <i class="fas fa-hourglass-half"></i>
                            Pendientes (<span id="activations-count">0</span>)
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Función para cargar estadísticas rápidamente
        async function loadStats() {
            try {
                const response = await fetch('api_support_stats.php');
                const data = await response.json();
                
                if (data.success && data.stats) {
                    updateStatsInCards(data.stats);
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        function updateStatsInCards(stats) {
            const mappings = {
                'tickets-open': stats.tickets_open || 0,
                'tickets-open-count': stats.tickets_open || 0,
                'chat-active': stats.chat_active || 0,
                'chat-active-count': stats.chat_active || 0,
                'downloads-today': stats.downloads_today || 0,
                'downloads-count': stats.downloads_today || 0,
                'channels-pending': stats.channels_pending || 0,
                'channels-count': stats.channels_pending || 0,
                'activations-count': stats.activations_pending || 0
            };
            
            Object.entries(mappings).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    const oldValue = parseInt(element.textContent) || 0;
                    element.textContent = value;
                    
                    if (value > oldValue) {
                        element.style.color = '#10b981';
                        element.style.fontWeight = 'bold';
                        setTimeout(() => {
                            element.style.color = '';
                            element.style.fontWeight = '';
                        }, 2000);
                    }
                }
            });
        }
        
        // Cargar estadísticas al inicio
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            setInterval(loadStats, 15000);
        });
    </script>

    <!-- Sistema de Notificaciones en Tiempo Real -->
    <script src="realtime_notifications.js"></script>
</body>
</html>
