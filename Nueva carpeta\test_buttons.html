<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Botones - RGS TOOL</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #0a0a0a;
            color: white;
        }
        
        .test-section {
            background: #221f1f;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .movie-card {
            background: #221f1f;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #333;
            position: relative;
            cursor: pointer;
            width: 200px;
            height: 300px;
            margin: 10px;
            display: inline-block;
        }
        
        .movie-poster {
            position: relative;
            height: 200px;
            background: #333;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .movie-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.9) 100%);
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: flex-end;
            padding: 1rem;
        }
        
        .movie-card:hover .movie-overlay {
            opacity: 1;
        }
        
        .movie-actions {
            display: flex;
            gap: 0.5rem;
            width: 100%;
        }
        
        .action-btn {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
        }
        
        .btn-order {
            background: #46d369;
            color: white;
        }
        
        .btn-order:hover {
            background: #3bc55a;
            transform: translateY(-2px);
        }
        
        .btn-details {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .movie-info {
            padding: 1rem;
        }
        
        .movie-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test de Botones de Pedido</h1>
    
    <div class="test-section">
        <h2>Información del Sistema</h2>
        <div id="systemInfo"></div>
    </div>
    
    <div class="test-section">
        <h2>Botones de Prueba</h2>
        <button class="test-btn" onclick="testDirectFunction()">Test Función Directa</button>
        <button class="test-btn" onclick="testButtonClick()">Test Click en Botón</button>
        <button class="test-btn" onclick="showAllButtons()">Mostrar Todos los Botones</button>
        <button class="test-btn" onclick="clearLog()">Limpiar Log</button>
    </div>
    
    <div class="test-section">
        <h2>Simulación de Tarjeta de Película</h2>
        <div class="movie-card" data-id="550" data-type="movie">
            <div class="movie-poster">
                <span>Poster de Prueba</span>
                <div class="movie-overlay">
                    <div class="movie-actions">
                        <button class="action-btn btn-order" onclick="openOrderModal(550, 'Fight Club Test', 'movie', '1999')">
                            <i class="fas fa-shopping-cart"></i>
                            Pedir
                        </button>
                        <button class="action-btn btn-details" onclick="showDetails(550, 'movie')">
                            <i class="fas fa-info-circle"></i>
                            Detalles
                        </button>
                    </div>
                </div>
            </div>
            <div class="movie-info">
                <h3 class="movie-title">Fight Club (Test)</h3>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Log de Eventos</h2>
        <div id="eventLog" class="log">Esperando eventos...\n</div>
    </div>
    
    <!-- Formulario oculto (igual que en index.php) -->
    <form id="orderForm" method="POST" style="position: absolute; visibility: hidden;">
        <input type="hidden" name="pedido_tmdb_id" id="pedido_tmdb_id">
        <input type="hidden" name="pedido_title" id="pedido_title">
        <input type="hidden" name="pedido_media_type" id="pedido_media_type">
        <input type="hidden" name="pedido_year" id="pedido_year">
    </form>

    <script>
        // Log function
        function log(message) {
            const logDiv = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // Función openOrderModal (copiada de index.php)
        function openOrderModal(tmdbId, title, mediaType, year) {
            log(`🎬 openOrderModal called with: ${tmdbId}, "${title}", ${mediaType}, ${year}`);
            
            // Verificar que los elementos del formulario existen
            const form = document.getElementById('orderForm');
            const tmdbInput = document.getElementById('pedido_tmdb_id');
            const titleInput = document.getElementById('pedido_title');
            const typeInput = document.getElementById('pedido_media_type');
            const yearInput = document.getElementById('pedido_year');
            
            log(`📋 Form elements check: form=${!!form}, inputs=${!!tmdbInput && !!titleInput && !!typeInput && !!yearInput}`);
            
            if (!form || !tmdbInput || !titleInput || !typeInput || !yearInput) {
                log('❌ Missing form elements!');
                alert('Error: Elementos del formulario no encontrados.');
                return;
            }
            
            if (confirm('¿Deseas hacer el pedido de "' + title + '"?')) {
                try {
                    log('✅ User confirmed, processing order...');
                    
                    // Fill hidden form
                    tmdbInput.value = tmdbId;
                    titleInput.value = title;
                    typeInput.value = mediaType;
                    yearInput.value = year;
                    
                    log(`📝 Form values set: ${tmdbInput.value}, "${titleInput.value}", ${typeInput.value}, ${yearInput.value}`);
                    
                    // En lugar de enviar, solo mostrar que funcionaría
                    log('🚀 Form would be submitted here (prevented for testing)');
                    alert('✅ Test exitoso! La función funciona correctamente.');
                    
                } catch (error) {
                    log(`❌ Error in openOrderModal: ${error.message}`);
                    alert('Error: ' + error.message);
                }
            } else {
                log('❌ User cancelled the order');
            }
        }
        
        function showDetails(tmdbId, mediaType) {
            log(`🔍 showDetails called with: ${tmdbId}, ${mediaType}`);
            alert(`Detalles de ${mediaType} ID: ${tmdbId}`);
        }
        
        // Funciones de prueba
        function testDirectFunction() {
            log('🧪 Testing direct function call...');
            openOrderModal(12345, 'Test Direct Call', 'movie', '2023');
        }
        
        function testButtonClick() {
            log('🧪 Testing button click simulation...');
            const button = document.querySelector('.btn-order');
            if (button) {
                log('🔘 Button found, simulating click...');
                button.click();
            } else {
                log('❌ No button found!');
            }
        }
        
        function showAllButtons() {
            const buttons = document.querySelectorAll('.btn-order');
            log(`🔘 Found ${buttons.length} order buttons:`);
            buttons.forEach((button, index) => {
                log(`  Button ${index + 1}: onclick="${button.getAttribute('onclick')}", visible=${button.offsetParent !== null}`);
            });
        }
        
        function clearLog() {
            document.getElementById('eventLog').innerHTML = 'Log limpiado...\n';
        }
        
        // Información del sistema
        function updateSystemInfo() {
            const info = document.getElementById('systemInfo');
            info.innerHTML = `
                <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
                <p><strong>Viewport:</strong> ${window.innerWidth}x${window.innerHeight}</p>
                <p><strong>openOrderModal function:</strong> ${typeof openOrderModal === 'function' ? '✅ Available' : '❌ Not found'}</p>
                <p><strong>Order form:</strong> ${document.getElementById('orderForm') ? '✅ Found' : '❌ Not found'}</p>
                <p><strong>Order buttons:</strong> ${document.querySelectorAll('.btn-order').length} found</p>
            `;
        }
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Page loaded');
            updateSystemInfo();
            
            // Agregar listeners a todos los botones
            document.querySelectorAll('.btn-order').forEach((button, index) => {
                button.addEventListener('click', function(event) {
                    log(`🖱️ Order button ${index + 1} clicked (event listener)`);
                });
            });
        });
        
        // Hacer funciones disponibles globalmente para la consola
        window.testDirectFunction = testDirectFunction;
        window.testButtonClick = testButtonClick;
        window.showAllButtons = showAllButtons;
        window.openOrderModal = openOrderModal;
        window.debugOrderSystem = function() {
            log('🔍 DEBUGGING ORDER SYSTEM:');
            updateSystemInfo();
            showAllButtons();
        };
    </script>
</body>
</html>
