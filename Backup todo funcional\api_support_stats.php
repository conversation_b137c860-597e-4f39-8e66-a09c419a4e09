<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Acceso denegado']);
    exit;
}

try {
    $stats = [];

    // Estadísticas de tickets
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
    $stats['tickets_open'] = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'in_progress'");
    $stats['tickets_in_progress'] = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'resolved'");
    $stats['tickets_resolved'] = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets");
    $stats['tickets_total'] = $stmt->fetchColumn() ?: 0;

    // Estadísticas de chat
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
    $stats['chat_active'] = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'waiting'");
    $stats['chat_waiting'] = $stmt->fetchColumn() ?: 0;

    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE DATE(started_at) = CURDATE()");
    $stats['chat_today'] = $stmt->fetchColumn() ?: 0;

    // Estadísticas de aplicaciones
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM app_downloads WHERE DATE(downloaded_at) = CURDATE()");
        $stats['downloads_today'] = $stmt->fetchColumn() ?: 0;

        $stmt = $pdo->query("SELECT COUNT(*) FROM app_downloads");
        $stats['downloads_total'] = $stmt->fetchColumn() ?: 0;
    } catch (Exception $e) {
        $stats['downloads_today'] = 0;
        $stats['downloads_total'] = 0;
    }

    // Estadísticas de canales
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
        $stats['channels_pending'] = $stmt->fetchColumn() ?: 0;

        $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'approved' AND DATE(updated_at) = CURDATE()");
        $stats['channels_approved_today'] = $stmt->fetchColumn() ?: 0;
    } catch (Exception $e) {
        $stats['channels_pending'] = 0;
        $stats['channels_approved_today'] = 0;
    }

    // Estadísticas de activaciones
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE is_active = 1");
        $stats['activations_pending'] = $stmt->fetchColumn() ?: 0;

        $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE DATE(created_at) = CURDATE()");
        $stats['activations_today'] = $stmt->fetchColumn() ?: 0;
    } catch (Exception $e) {
        $stats['activations_pending'] = 0;
        $stats['activations_today'] = 0;
    }

    // Obtener notificaciones no leídas
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM admin_notifications WHERE is_read = 0");
        $stats['notifications_unread'] = $stmt->fetchColumn() ?: 0;

        // Obtener notificaciones recientes por tipo
        $stmt = $pdo->query("
            SELECT
                type,
                COUNT(*) as count
            FROM admin_notifications
            WHERE is_read = 0
            GROUP BY type
        ");
        $counters = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stats['notification_counters'] = [];
        foreach ($counters as $counter) {
            $stats['notification_counters'][$counter['type']] = $counter['count'];
        }

    } catch (Exception $e) {
        $stats['notifications_unread'] = 0;
        $stats['notification_counters'] = [];
    }

    // Timestamp
    $stats['timestamp'] = time();
    $stats['server_time'] = date('Y-m-d H:i:s');

    echo json_encode([
        'success' => true,
        // Datos para compatibilidad con admin2.php
        'tickets_open' => $stats['tickets_open'],
        'chat_active' => $stats['chat_active'],
        'downloads_today' => $stats['downloads_today'],
        'channels_pending' => $stats['channels_pending'],
        'activations_pending' => $stats['activations_pending'],
        // Datos completos
        'stats' => $stats
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error al obtener estadísticas: ' . $e->getMessage()
    ]);
}
?>
