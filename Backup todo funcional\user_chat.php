<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';

// Variables para el estado del chat
$current_session = null;
$messages = [];
$error_message = '';
$success_message = '';

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['start_chat'])) {
        // Verificar si ya tiene una sesión activa
        $stmt = $pdo->prepare("SELECT id FROM chat_sessions WHERE user_id = ? AND status IN ('waiting', 'active')");
        $stmt->execute([$user_id]);
        $existing_session = $stmt->fetch();
        
        if (!$existing_session) {
            // Crear nueva sesión de chat
            $stmt = $pdo->prepare("INSERT INTO chat_sessions (user_id, status) VALUES (?, 'waiting')");
            $stmt->execute([$user_id]);
            $session_id = $pdo->lastInsertId();
        } else {
            $session_id = $existing_session['id'];
        }
        
        header("Location: user_chat.php?session=$session_id");
        exit;
    }
    
    if (isset($_POST['send_message'])) {
        $session_id = (int)$_POST['session_id'];
        $message = clean_input($_POST['message']);
        
        // Verificar que la sesión pertenece al usuario
        $stmt = $pdo->prepare("SELECT id FROM chat_sessions WHERE id = ? AND user_id = ?");
        $stmt->execute([$session_id, $user_id]);
        
        if ($stmt->fetch()) {
            $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, 0)");
            $stmt->execute([$session_id, $user_id, $message]);
        }
        
        header("Location: user_chat.php?session=$session_id");
        exit;
    }
    
    if (isset($_POST['end_chat'])) {
        $session_id = (int)$_POST['session_id'];
        
        // Verificar que la sesión pertenece al usuario
        $stmt = $pdo->prepare("SELECT id FROM chat_sessions WHERE id = ? AND user_id = ?");
        $stmt->execute([$session_id, $user_id]);
        
        if ($stmt->fetch()) {
            $stmt = $pdo->prepare("UPDATE chat_sessions SET status = 'ended', ended_at = NOW() WHERE id = ?");
            $stmt->execute([$session_id]);
        }
        
        header("Location: user_chat.php");
        exit;
    }
}

// Obtener sesión actual si existe
$current_session = null;
$messages = [];

if (isset($_GET['session'])) {
    $session_id = (int)$_GET['session'];
    
    $stmt = $pdo->prepare("SELECT * FROM chat_sessions WHERE id = ? AND user_id = ?");
    $stmt->execute([$session_id, $user_id]);
    $current_session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($current_session) {
        // Obtener mensajes
        $stmt = $pdo->prepare("
            SELECT cm.*, u.username
            FROM chat_messages cm
            LEFT JOIN users u ON cm.sender_id = u.id
            WHERE cm.session_id = ?
            ORDER BY cm.sent_at ASC
        ");
        $stmt->execute([$session_id]);
        $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} else {
    // Verificar si tiene sesión activa
    $stmt = $pdo->prepare("SELECT id FROM chat_sessions WHERE user_id = ? AND status IN ('waiting', 'active') ORDER BY started_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    $active_session = $stmt->fetch();
    
    if ($active_session) {
        header("Location: user_chat.php?session=" . $active_session['id']);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 Chat de Soporte - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .chat-container {
            height: calc(100vh - 80px);
            display: flex;
            flex-direction: column;
            max-width: 1000px;
            margin: 0 auto;
        }

        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 2rem;
        }

        .welcome-icon {
            font-size: 5rem;
            color: var(--accent-color);
            margin-bottom: 2rem;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .welcome-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 500px;
        }

        .chat-header {
            background: var(--secondary-color);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-waiting .status-dot {
            background: var(--warning-color);
        }

        .status-active .status-dot {
            background: var(--success-color);
        }

        .status-ended .status-dot {
            background: var(--border-color);
            animation: none;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background: var(--dark-bg);
        }

        .message {
            display: flex;
            gap: 0.75rem;
            max-width: 80%;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message-avatar.user {
            background: var(--accent-color);
        }

        .message-avatar.admin {
            background: var(--primary-color);
        }

        .message-content {
            background: var(--secondary-color);
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            position: relative;
        }

        .message.user .message-content {
            background: var(--primary-color);
        }

        .message-text {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            word-wrap: break-word;
        }

        .message-time {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .message-form {
            background: var(--secondary-color);
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .form-container {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .empty-chat {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-secondary);
        }

        .empty-chat i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .chat-info {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin: 1rem;
            color: var(--success-color);
        }

        @media (max-width: 768px) {
            .message {
                max-width: 95%;
            }

            .form-container {
                flex-direction: column;
                gap: 0.5rem;
            }

            .message-input {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_chat.php" class="logo">
                <i class="fas fa-comments"></i>
                <span>Chat de Soporte</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <div class="chat-container">
        <?php if (!$current_session): ?>
        <!-- Pantalla de Bienvenida -->
        <div class="welcome-screen">
            <div class="welcome-icon">
                <i class="fas fa-headset"></i>
            </div>
            <h1 class="welcome-title">Chat de Soporte en Vivo</h1>
            <p class="welcome-subtitle">
                Conecta con nuestro equipo de soporte técnico especializado en IPTV. 
                Te ayudaremos a resolver cualquier problema o duda que tengas.
            </p>
            
            <form method="POST">
                <button type="submit" name="start_chat" class="btn btn-success">
                    <i class="fas fa-play"></i>
                    Iniciar Chat
                </button>
            </form>
            
            <div style="margin-top: 2rem; color: var(--text-secondary); font-size: 0.9rem;">
                <p><i class="fas fa-clock"></i> Tiempo de respuesta promedio: 2-5 minutos</p>
                <p><i class="fas fa-users"></i> Soporte disponible 24/7</p>
            </div>
        </div>
        <?php else: ?>
        <!-- Chat Activo -->
        <div class="chat-header">
            <div class="chat-status status-<?php echo $current_session['status']; ?>">
                <div class="status-dot"></div>
                <div>
                    <strong>
                        <?php 
                        $status_labels = [
                            'waiting' => 'Esperando agente...',
                            'active' => 'Chat activo',
                            'ended' => 'Chat finalizado'
                        ];
                        echo $status_labels[$current_session['status']] ?? $current_session['status'];
                        ?>
                    </strong>
                    <div style="font-size: 0.8rem; color: var(--text-secondary);">
                        Sesión #<?php echo $current_session['id']; ?>
                    </div>
                </div>
            </div>
            
            <?php if ($current_session['status'] !== 'ended'): ?>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="session_id" value="<?php echo $current_session['id']; ?>">
                <button type="submit" name="end_chat" class="btn btn-danger" onclick="return confirm('¿Estás seguro de que quieres finalizar el chat?')">
                    <i class="fas fa-times"></i>
                    Finalizar Chat
                </button>
            </form>
            <?php endif; ?>
        </div>

        <?php if ($current_session['status'] === 'waiting'): ?>
        <div class="chat-info">
            <i class="fas fa-info-circle"></i>
            <strong>Esperando conexión...</strong> Un agente se conectará contigo en breve. Mientras tanto, puedes escribir tu consulta.
        </div>
        <?php endif; ?>

        <!-- Mensajes -->
        <div class="messages-container" id="messagesContainer">
            <?php if (empty($messages)): ?>
            <div class="empty-chat">
                <i class="fas fa-comments"></i>
                <h3>¡Hola <?php echo htmlspecialchars($username); ?>!</h3>
                <p>Escribe tu mensaje para comenzar la conversación</p>
            </div>
            <?php else: ?>
                <?php foreach ($messages as $message): ?>
                <div class="message <?php echo $message['is_admin'] ? 'admin' : 'user'; ?>">
                    <div class="message-avatar <?php echo $message['is_admin'] ? 'admin' : 'user'; ?>">
                        <?php if ($message['is_admin']): ?>
                            <i class="fas fa-user-shield"></i>
                        <?php else: ?>
                            <?php echo strtoupper(substr($message['username'], 0, 1)); ?>
                        <?php endif; ?>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                        </div>
                        <div class="message-time">
                            <?php echo date('H:i', strtotime($message['sent_at'])); ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Formulario de mensaje -->
        <?php if ($current_session['status'] !== 'ended'): ?>
        <div class="message-form">
            <form method="POST" class="form-container" id="messageForm">
                <input type="hidden" name="session_id" value="<?php echo $current_session['id']; ?>">
                <textarea 
                    name="message" 
                    class="message-input" 
                    placeholder="Escribe tu mensaje..."
                    required
                    id="messageInput"
                ></textarea>
                <button type="submit" name="send_message" class="btn btn-primary" id="sendBtn">
                    <i class="fas fa-paper-plane"></i>
                    Enviar
                </button>
            </form>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>

    <script>
        // Auto-scroll al final de los mensajes
        function scrollToBottom() {
            const container = document.getElementById('messagesContainer');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        }

        // Scroll inicial
        scrollToBottom();

        // Auto-resize del textarea
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Enviar con Ctrl+Enter
            messageInput.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'Enter') {
                    document.getElementById('messageForm').submit();
                }
            });

            // Focus automático
            messageInput.focus();
        }

        // Auto-refresh cada 3 segundos para nuevos mensajes
        <?php if ($current_session): ?>
        setInterval(function() {
            if (document.hidden) return;
            
            // Solo actualizar si no estamos escribiendo
            const activeElement = document.activeElement;
            if (activeElement !== messageInput) {
                location.reload();
            }
        }, 3000);
        <?php endif; ?>

        // Validación del formulario
        const messageForm = document.getElementById('messageForm');
        if (messageForm) {
            messageForm.addEventListener('submit', function(e) {
                const message = messageInput.value.trim();
                if (!message) {
                    e.preventDefault();
                    messageInput.focus();
                    return;
                }
                
                // Deshabilitar botón durante el envío
                const sendBtn = document.getElementById('sendBtn');
                sendBtn.disabled = true;
                sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            });
        }

        // Notificación de sonido para nuevos mensajes (opcional)
        const messageCount = <?php echo count($messages); ?>;
        
        // Animación de entrada para mensajes
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.message').forEach(message => {
            message.style.opacity = '0';
            message.style.transform = 'translateY(20px)';
            message.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            observer.observe(message);
        });
    </script>
</body>
</html>
