<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';

// Procesar solicitud de canal
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_channel'])) {
    try {
        $channel_name = clean_input($_POST['channel_name']);
        $channel_url = clean_input($_POST['channel_url']);
        $country = clean_input($_POST['country']);
        $language = clean_input($_POST['language']);
        $category = clean_input($_POST['category']);
        $description = clean_input($_POST['description']);

        // Validar campos requeridos
        if (empty($channel_name) || empty($country) || empty($language) || empty($category)) {
            $error_message = "Por favor completa todos los campos requeridos.";
        } else {
            $stmt = $pdo->prepare("INSERT INTO channel_requests (user_id, channel_name, channel_url, country, language, category, description, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')");
            $stmt->execute([$user_id, $channel_name, $channel_url, $country, $language, $category, $description]);

            $success_message = "Solicitud de canal enviada correctamente. La revisaremos y te notificaremos el resultado.";
        }
    } catch (Exception $e) {
        $error_message = "Error al enviar solicitud: " . $e->getMessage();
    }
}

// Obtener solicitudes del usuario
try {
    $stmt = $pdo->prepare("
        SELECT * FROM channel_requests
        WHERE user_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$user_id]);
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug: mostrar cuántas solicitudes se encontraron
    error_log("Solicitudes encontradas para usuario $user_id: " . count($requests));

} catch (Exception $e) {
    $requests = [];
    $error_message = "Error al cargar solicitudes: " . $e->getMessage();
    error_log("Error en user_channels: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📺 Solicitar Canales - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .request-form-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .requests-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .requests-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .requests-list {
            padding: 1.5rem;
        }

        .request-item {
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--border-color);
            transition: var(--transition);
        }

        .request-item:hover {
            background: rgba(255,255,255,0.08);
        }

        .request-item.status-pending {
            border-left-color: var(--warning-color);
        }

        .request-item.status-approved {
            border-left-color: var(--success-color);
        }

        .request-item.status-rejected {
            border-left-color: var(--error-color);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .request-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .request-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .status-approved { 
            background: rgba(16, 185, 129, 0.2); 
            color: #10b981; 
        }

        .status-rejected { 
            background: rgba(239, 68, 68, 0.2); 
            color: #ef4444; 
        }

        .request-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .request-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .info-box {
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid rgba(37, 99, 235, 0.3);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        .info-box h3 {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-box ul {
            list-style: none;
            padding: 0;
        }

        .info-box li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-box li::before {
            content: '•';
            color: var(--primary-color);
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .request-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .request-details {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_channels.php" class="logo">
                <i class="fas fa-tv"></i>
                <span>Solicitar Canales</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-tv" style="color: var(--primary-color);"></i>
                Solicitar Canales
            </h1>
            <p class="page-subtitle">
                ¿Falta algún canal en tu lista? Solicítalo y lo evaluaremos para agregarlo a nuestro servicio
            </p>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <div class="info-box">
            <h3>
                <i class="fas fa-info-circle"></i>
                Información sobre Solicitudes de Canales
            </h3>
            <ul>
                <li>Revisamos todas las solicitudes en un plazo de 24-48 horas</li>
                <li>Los canales deben tener transmisión legal y estable</li>
                <li>Priorizamos canales con alta demanda de usuarios</li>
                <li>Te notificaremos por email el resultado de tu solicitud</li>
                <li>Puedes solicitar hasta 5 canales por semana</li>
            </ul>
        </div>

        <!-- Formulario de Solicitud -->
        <div class="request-form-section">
            <h2 class="section-title">
                <i class="fas fa-plus"></i>
                Solicitar Nuevo Canal
            </h2>
            
            <form method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Nombre del Canal *</label>
                        <input type="text" name="channel_name" class="form-input" required placeholder="Ej: CNN en Español">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">URL del Canal (opcional)</label>
                        <input type="url" name="channel_url" class="form-input" placeholder="https://ejemplo.com/canal">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">País *</label>
                        <select name="country" class="form-select" required>
                            <option value="">Seleccionar país</option>
                            <option value="ES">España</option>
                            <option value="MX">México</option>
                            <option value="AR">Argentina</option>
                            <option value="CO">Colombia</option>
                            <option value="PE">Perú</option>
                            <option value="CL">Chile</option>
                            <option value="VE">Venezuela</option>
                            <option value="EC">Ecuador</option>
                            <option value="BO">Bolivia</option>
                            <option value="PY">Paraguay</option>
                            <option value="UY">Uruguay</option>
                            <option value="US">Estados Unidos</option>
                            <option value="OTHER">Otro</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Idioma *</label>
                        <select name="language" class="form-select" required>
                            <option value="">Seleccionar idioma</option>
                            <option value="spanish">Español</option>
                            <option value="english">Inglés</option>
                            <option value="portuguese">Portugués</option>
                            <option value="french">Francés</option>
                            <option value="italian">Italiano</option>
                            <option value="german">Alemán</option>
                            <option value="other">Otro</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Categoría *</label>
                        <select name="category" class="form-select" required>
                            <option value="">Seleccionar categoría</option>
                            <option value="news">Noticias</option>
                            <option value="sports">Deportes</option>
                            <option value="entertainment">Entretenimiento</option>
                            <option value="movies">Películas</option>
                            <option value="series">Series</option>
                            <option value="kids">Infantil</option>
                            <option value="music">Música</option>
                            <option value="documentary">Documentales</option>
                            <option value="lifestyle">Estilo de Vida</option>
                            <option value="religious">Religioso</option>
                            <option value="educational">Educativo</option>
                            <option value="other">Otro</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group" style="margin-bottom: 2rem;">
                    <label class="form-label">Descripción / Motivo de la Solicitud</label>
                    <textarea name="description" class="form-textarea" placeholder="Describe por qué te gustaría que agreguemos este canal, su contenido, popularidad, etc."></textarea>
                </div>
                
                <button type="submit" name="request_channel" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Enviar Solicitud
                </button>
            </form>
        </div>

        <!-- Lista de Solicitudes -->
        <div class="requests-section">
            <div class="requests-header">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    Mis Solicitudes (<?php echo count($requests); ?>)
                </h2>
            </div>
            
            <div class="requests-list">
                <?php if (empty($requests)): ?>
                <div class="empty-state">
                    <i class="fas fa-tv"></i>
                    <h3>No tienes solicitudes de canales</h3>
                    <p>Cuando envíes tu primera solicitud, aparecerá aquí</p>
                </div>
                <?php else: ?>
                    <?php foreach ($requests as $request): ?>
                    <div class="request-item status-<?php echo $request['status']; ?>">
                        <div class="request-header">
                            <div>
                                <div class="request-title">
                                    <?php echo htmlspecialchars($request['channel_name']); ?>
                                </div>
                                <div class="request-meta">
                                    <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($request['created_at'])); ?></span>
                                    <span><i class="fas fa-flag"></i> <?php echo htmlspecialchars($request['country']); ?></span>
                                    <span><i class="fas fa-language"></i> <?php echo ucfirst($request['language']); ?></span>
                                </div>
                            </div>
                            <span class="status-badge status-<?php echo $request['status']; ?>">
                                <?php 
                                $status_labels = [
                                    'pending' => 'Pendiente',
                                    'approved' => 'Aprobado',
                                    'rejected' => 'Rechazado'
                                ];
                                echo $status_labels[$request['status']] ?? $request['status'];
                                ?>
                            </span>
                        </div>
                        
                        <div class="request-details">
                            <div class="detail-item">
                                <div class="detail-label">Categoría</div>
                                <div class="detail-value"><?php echo ucfirst($request['category']); ?></div>
                            </div>
                            <?php if ($request['channel_url']): ?>
                            <div class="detail-item">
                                <div class="detail-label">URL</div>
                                <div class="detail-value">
                                    <a href="<?php echo htmlspecialchars($request['channel_url']); ?>" target="_blank" style="color: var(--primary-color);">
                                        <i class="fas fa-external-link-alt"></i> Ver
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($request['description']): ?>
                        <div class="request-description">
                            <strong>Descripción:</strong><br>
                            <?php echo nl2br(htmlspecialchars($request['description'])); ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($request['admin_notes']): ?>
                        <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: var(--border-radius); margin-top: 1rem;">
                            <strong>Notas del administrador:</strong><br>
                            <?php echo nl2br(htmlspecialchars($request['admin_notes'])); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        // Validación del formulario
        document.querySelector('form').addEventListener('submit', function(e) {
            const channelName = document.querySelector('input[name="channel_name"]').value.trim();
            
            if (channelName.length < 3) {
                alert('El nombre del canal debe tener al menos 3 caracteres');
                e.preventDefault();
                return;
            }
        });

        // Animación de entrada para las solicitudes
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.request-item').forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(item);
        });
    </script>
</body>
</html>
