/**
 * Sistema de Notificaciones en Tiempo Real
 * Para comunicación entre admin.php y admin2.php
 */

class RealtimeNotifications {
    constructor(options = {}) {
        this.panel = options.panel || 'admin'; // 'admin' o 'admin2'
        this.eventSource = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.isConnected = false;
        this.notifications = [];
        this.onNotificationCallback = options.onNotification || null;
        this.onStatsUpdateCallback = options.onStatsUpdate || null;
        
        this.init();
    }
    
    init() {
        this.connect();
        this.setupUI();
        this.loadExistingNotifications();
    }
    
    connect() {
        if (this.eventSource) {
            this.eventSource.close();
        }
        
        console.log('🔔 Connecting to notification stream...');
        
        const url = `realtime_notifications.php?panel=${this.panel}&last_check=${new Date().toISOString()}`;
        this.eventSource = new EventSource(url);
        
        this.eventSource.onopen = () => {
            console.log('✅ Notification stream connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus(true);
        };
        
        this.eventSource.onmessage = (event) => {
            this.handleMessage(event);
        };
        
        this.eventSource.addEventListener('notification', (event) => {
            this.handleNotification(JSON.parse(event.data));
        });
        
        this.eventSource.addEventListener('stats_update', (event) => {
            this.handleStatsUpdate(JSON.parse(event.data));
        });
        
        this.eventSource.addEventListener('heartbeat', (event) => {
            // console.log('💓 Heartbeat received');
        });
        
        this.eventSource.onerror = (event) => {
            console.error('❌ Notification stream error:', event);
            this.isConnected = false;
            this.updateConnectionStatus(false);
            this.handleReconnect();
        };
        
        this.eventSource.addEventListener('close', (event) => {
            console.log('🔌 Notification stream closed');
            this.isConnected = false;
            this.updateConnectionStatus(false);
            this.handleReconnect();
        });
    }
    
    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('❌ Max reconnection attempts reached');
            this.showReconnectButton();
        }
    }
    
    handleNotification(notification) {
        console.log('🔔 New notification:', notification);
        
        this.notifications.unshift(notification);
        this.updateNotificationUI();
        this.showNotificationPopup(notification);
        this.playNotificationSound(notification.priority);
        
        if (this.onNotificationCallback) {
            this.onNotificationCallback(notification);
        }
    }
    
    handleStatsUpdate(data) {
        console.log('📊 Stats update:', data.stats);
        
        if (this.onStatsUpdateCallback) {
            this.onStatsUpdateCallback(data.stats);
        }
        
        this.updateStatsUI(data.stats);
    }
    
    setupUI() {
        // Crear contenedor de notificaciones si no existe
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.innerHTML = `
                <div id="notification-bell" class="notification-bell">
                    <i class="fas fa-bell"></i>
                    <span id="notification-count" class="notification-count">0</span>
                </div>
                <div id="notification-dropdown" class="notification-dropdown">
                    <div class="notification-header">
                        <h4>Notificaciones</h4>
                        <button id="mark-all-read" class="btn-small">Marcar todas como leídas</button>
                    </div>
                    <div id="notification-list" class="notification-list">
                        <div class="no-notifications">No hay notificaciones</div>
                    </div>
                </div>
                <div id="connection-status" class="connection-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">Conectando...</span>
                </div>
            `;
            
            // Agregar estilos
            const style = document.createElement('style');
            style.textContent = `
                #notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                }
                
                .notification-bell {
                    position: relative;
                    background: #2563eb;
                    color: white;
                    padding: 12px;
                    border-radius: 50%;
                    cursor: pointer;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    transition: all 0.3s ease;
                }
                
                .notification-bell:hover {
                    background: #1d4ed8;
                    transform: scale(1.1);
                }
                
                .notification-bell.has-notifications {
                    animation: notification-pulse 2s infinite;
                }
                
                @keyframes notification-pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }
                
                .notification-count {
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    background: #ef4444;
                    color: white;
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.7rem;
                    font-weight: bold;
                }
                
                .notification-dropdown {
                    position: absolute;
                    top: 60px;
                    right: 0;
                    width: 350px;
                    max-height: 400px;
                    background: #1e293b;
                    border-radius: 12px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    border: 1px solid #334155;
                    display: none;
                    overflow: hidden;
                }
                
                .notification-dropdown.show {
                    display: block;
                    animation: dropdown-appear 0.3s ease;
                }
                
                @keyframes dropdown-appear {
                    from { opacity: 0; transform: translateY(-10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                
                .notification-header {
                    padding: 1rem;
                    border-bottom: 1px solid #334155;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .notification-header h4 {
                    margin: 0;
                    color: #f8fafc;
                }
                
                .notification-list {
                    max-height: 300px;
                    overflow-y: auto;
                }
                
                .notification-item {
                    padding: 1rem;
                    border-bottom: 1px solid #334155;
                    cursor: pointer;
                    transition: background 0.2s ease;
                }
                
                .notification-item:hover {
                    background: rgba(37, 99, 235, 0.1);
                }
                
                .notification-item.unread {
                    background: rgba(37, 99, 235, 0.05);
                    border-left: 3px solid #2563eb;
                }
                
                .notification-title {
                    font-weight: 600;
                    color: #f8fafc;
                    margin-bottom: 0.25rem;
                }
                
                .notification-message {
                    color: #cbd5e1;
                    font-size: 0.9rem;
                    margin-bottom: 0.5rem;
                }
                
                .notification-time {
                    color: #64748b;
                    font-size: 0.8rem;
                }
                
                .notification-priority-urgent {
                    border-left-color: #ef4444 !important;
                }
                
                .notification-priority-high {
                    border-left-color: #f59e0b !important;
                }
                
                .connection-status {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background: #1e293b;
                    color: #cbd5e1;
                    padding: 0.5rem 1rem;
                    border-radius: 8px;
                    font-size: 0.8rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    border: 1px solid #334155;
                }
                
                .status-indicator {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #6b7280;
                }
                
                .status-indicator.connected {
                    background: #10b981;
                    animation: status-blink 2s infinite;
                }
                
                .status-indicator.disconnected {
                    background: #ef4444;
                }
                
                @keyframes status-blink {
                    0%, 50% { opacity: 1; }
                    51%, 100% { opacity: 0.3; }
                }
                
                .btn-small {
                    background: #2563eb;
                    color: white;
                    border: none;
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    font-size: 0.8rem;
                    cursor: pointer;
                }
                
                .btn-small:hover {
                    background: #1d4ed8;
                }
                
                .no-notifications {
                    padding: 2rem;
                    text-align: center;
                    color: #64748b;
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(container);
            
            // Event listeners
            document.getElementById('notification-bell').addEventListener('click', () => {
                this.toggleDropdown();
            });
            
            document.getElementById('mark-all-read').addEventListener('click', () => {
                this.markAllAsRead();
            });
            
            // Cerrar dropdown al hacer clic fuera
            document.addEventListener('click', (e) => {
                if (!e.target.closest('#notification-container')) {
                    this.closeDropdown();
                }
            });
        }
    }
    
    updateNotificationUI() {
        const count = this.notifications.filter(n => !n.is_read).length;
        const countElement = document.getElementById('notification-count');
        const bellElement = document.getElementById('notification-bell');
        const listElement = document.getElementById('notification-list');
        
        if (countElement) {
            countElement.textContent = count;
            countElement.style.display = count > 0 ? 'flex' : 'none';
        }
        
        if (bellElement) {
            bellElement.classList.toggle('has-notifications', count > 0);
        }
        
        if (listElement) {
            if (this.notifications.length === 0) {
                listElement.innerHTML = '<div class="no-notifications">No hay notificaciones</div>';
            } else {
                listElement.innerHTML = this.notifications.map(notification => `
                    <div class="notification-item ${!notification.is_read ? 'unread' : ''} notification-priority-${notification.priority}" 
                         data-id="${notification.id}">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-time">${this.formatTime(notification.created_at)}</div>
                    </div>
                `).join('');
                
                // Agregar event listeners a los items
                listElement.querySelectorAll('.notification-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const id = item.dataset.id;
                        this.markAsRead(id);
                        this.handleNotificationClick(id);
                    });
                });
            }
        }
    }
    
    showNotificationPopup(notification) {
        // Crear popup temporal
        const popup = document.createElement('div');
        popup.className = 'notification-popup';
        popup.innerHTML = `
            <div class="popup-content">
                <div class="popup-title">${notification.title}</div>
                <div class="popup-message">${notification.message}</div>
            </div>
        `;
        
        // Estilos para el popup
        const style = document.createElement('style');
        style.textContent = `
            .notification-popup {
                position: fixed;
                top: 80px;
                right: 20px;
                background: #1e293b;
                color: #f8fafc;
                padding: 1rem;
                border-radius: 8px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                border: 1px solid #334155;
                max-width: 300px;
                z-index: 10001;
                animation: popup-slide-in 0.3s ease;
            }
            
            @keyframes popup-slide-in {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            .popup-title {
                font-weight: 600;
                margin-bottom: 0.5rem;
            }
            
            .popup-message {
                font-size: 0.9rem;
                color: #cbd5e1;
            }
        `;
        
        if (!document.querySelector('style[data-popup-styles]')) {
            style.setAttribute('data-popup-styles', 'true');
            document.head.appendChild(style);
        }
        
        document.body.appendChild(popup);
        
        // Remover después de 5 segundos
        setTimeout(() => {
            popup.style.animation = 'popup-slide-out 0.3s ease forwards';
            setTimeout(() => popup.remove(), 300);
        }, 5000);
        
        // Agregar animación de salida
        const exitStyle = document.createElement('style');
        exitStyle.textContent = `
            @keyframes popup-slide-out {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(exitStyle);
    }
    
    playNotificationSound(priority = 'normal') {
        // Crear sonido de notificación
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        // Diferentes tonos según prioridad
        const frequencies = {
            urgent: [800, 1000, 800],
            high: [600, 800],
            normal: [500],
            low: [400]
        };
        
        const freq = frequencies[priority] || frequencies.normal;
        
        oscillator.frequency.setValueAtTime(freq[0], audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }
    
    toggleDropdown() {
        const dropdown = document.getElementById('notification-dropdown');
        dropdown.classList.toggle('show');
    }
    
    closeDropdown() {
        const dropdown = document.getElementById('notification-dropdown');
        dropdown.classList.remove('show');
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');
        
        if (connected) {
            indicator.className = 'status-indicator connected';
            text.textContent = 'Conectado';
        } else {
            indicator.className = 'status-indicator disconnected';
            text.textContent = 'Desconectado';
        }
    }
    
    updateStatsUI(stats) {
        // Actualizar elementos de estadísticas en la página
        const elements = {
            'pending-tickets': stats.pending_tickets,
            'active-chats': stats.active_chats,
            'pending-channels': stats.pending_channels,
            'new-chat-messages': stats.new_chat_messages
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value || 0;
            }
        });
    }
    
    async loadExistingNotifications() {
        try {
            const response = await fetch('api_notifications.php?action=get_unread');
            const data = await response.json();
            
            if (data.success) {
                this.notifications = data.notifications;
                this.updateNotificationUI();
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    }
    
    async markAsRead(notificationId) {
        try {
            const formData = new FormData();
            formData.append('action', 'mark_read');
            formData.append('notification_id', notificationId);
            
            const response = await fetch('api_notifications.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Actualizar localmente
                const notification = this.notifications.find(n => n.id == notificationId);
                if (notification) {
                    notification.is_read = true;
                }
                this.updateNotificationUI();
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
    
    async markAllAsRead() {
        try {
            const formData = new FormData();
            formData.append('action', 'mark_all_read');
            formData.append('target_admin', 'all');
            
            const response = await fetch('api_notifications.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Actualizar localmente
                this.notifications.forEach(n => n.is_read = true);
                this.updateNotificationUI();
                this.closeDropdown();
            }
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    }
    
    handleNotificationClick(notificationId) {
        const notification = this.notifications.find(n => n.id == notificationId);
        if (!notification) return;
        
        // Redirigir según el tipo de notificación
        switch (notification.type) {
            case 'chat_message':
                if (this.panel === 'admin') {
                    window.location.href = 'admin2.php#chat';
                } else {
                    window.location.href = 'admin_chat_real.php';
                }
                break;
            case 'support_ticket':
                if (this.panel === 'admin') {
                    window.location.href = 'admin2.php#tickets';
                } else {
                    window.location.href = 'tickets_admin.php';
                }
                break;
            case 'channel_request':
                if (this.panel === 'admin') {
                    window.location.href = 'admin2.php#channels';
                } else {
                    window.location.href = 'admin_channels_real.php';
                }
                break;
        }
    }
    
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // menos de 1 minuto
            return 'Ahora';
        } else if (diff < 3600000) { // menos de 1 hora
            return `${Math.floor(diff / 60000)}m`;
        } else if (diff < 86400000) { // menos de 1 día
            return `${Math.floor(diff / 3600000)}h`;
        } else {
            return date.toLocaleDateString();
        }
    }
    
    showReconnectButton() {
        const statusElement = document.getElementById('connection-status');
        statusElement.innerHTML = `
            <span class="status-indicator disconnected"></span>
            <span class="status-text">Desconectado</span>
            <button onclick="window.realtimeNotifications.connect()" class="btn-small" style="margin-left: 0.5rem;">
                Reconectar
            </button>
        `;
    }
    
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
        this.updateConnectionStatus(false);
    }
}

// Función para crear notificación desde cualquier parte del código
async function createNotification(type, title, message, options = {}) {
    try {
        const formData = new FormData();
        formData.append('action', 'create');
        formData.append('type', type);
        formData.append('title', title);
        formData.append('message', message);
        formData.append('priority', options.priority || 'normal');
        formData.append('target_admin', options.target || 'all');
        
        if (options.reference_id) {
            formData.append('reference_id', options.reference_id);
        }
        if (options.reference_type) {
            formData.append('reference_type', options.reference_type);
        }
        
        const response = await fetch('api_notifications.php', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error creating notification:', error);
        return { success: false, error: error.message };
    }
}

// Inicializar automáticamente si estamos en una página de admin
document.addEventListener('DOMContentLoaded', function() {
    // Detectar si estamos en admin.php o admin2.php
    const currentPage = window.location.pathname;
    let panel = 'admin';
    
    if (currentPage.includes('admin2.php')) {
        panel = 'admin2';
    }
    
    // Solo inicializar en páginas de admin
    if (currentPage.includes('admin')) {
        window.realtimeNotifications = new RealtimeNotifications({
            panel: panel,
            onNotification: (notification) => {
                console.log('📢 Notification received:', notification);
            },
            onStatsUpdate: (stats) => {
                console.log('📊 Stats updated:', stats);
            }
        });
    }
});
