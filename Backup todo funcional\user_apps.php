<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';

// Obtener aplicaciones publicadas
try {
    $stmt = $pdo->prepare("
        SELECT * FROM support_apps
        WHERE (status = 'active' OR status IS NULL) AND is_active = 1
        ORDER BY platform, name
    ");
    $stmt->execute();
    $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug: mostrar cuántas apps se encontraron
    error_log("Apps encontradas: " . count($apps));

} catch (Exception $e) {
    $apps = [];
    $error_message = "Error al cargar aplicaciones: " . $e->getMessage();
    error_log("Error en user_apps: " . $e->getMessage());
}

// Agrupar por plataforma
$apps_by_platform = [];
foreach ($apps as $app) {
    $apps_by_platform[$app['platform']][] = $app;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Aplicaciones IPTV - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .platform-section {
            margin-bottom: 3rem;
        }

        .platform-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        .platform-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-color);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .platform-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }

        .app-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .app-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .app-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            border-color: var(--accent-color);
        }

        .app-card:hover::before {
            transform: scaleX(1);
        }

        .app-header {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .app-icon {
            width: 64px;
            height: 64px;
            background: var(--accent-color);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            flex-shrink: 0;
        }

        .app-info {
            flex: 1;
        }

        .app-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .app-version {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .app-size {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .app-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .app-features {
            margin-bottom: 1.5rem;
        }

        .features-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .features-list {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .features-list li::before {
            content: '✓';
            color: var(--success-color);
            font-weight: bold;
        }

        .app-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            flex: 1;
            justify-content: center;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .empty-platform {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .empty-platform i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .download-info {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 3rem;
            color: var(--success-color);
        }

        .download-info h3 {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .download-info ul {
            list-style: none;
            padding: 0;
        }

        .download-info li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .download-info li::before {
            content: '•';
            color: var(--success-color);
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .apps-grid {
                grid-template-columns: 1fr;
            }

            .app-header {
                flex-direction: column;
                text-align: center;
            }

            .app-actions {
                flex-direction: column;
            }

            .btn {
                flex: none;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_apps.php" class="logo">
                <i class="fas fa-mobile-alt"></i>
                <span>Aplicaciones IPTV</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-mobile-alt" style="color: var(--primary-color);"></i>
                Aplicaciones IPTV Oficiales
            </h1>
            <p class="page-subtitle">
                Descarga las aplicaciones oficiales para disfrutar de tu servicio IPTV en todos tus dispositivos
            </p>
        </div>

        <div class="download-info">
            <h3>
                <i class="fas fa-info-circle"></i>
                Información de Descarga
            </h3>
            <ul>
                <li>Todas las aplicaciones son oficiales y seguras</li>
                <li>Compatibles con tu servicio IPTV actual</li>
                <li>Actualizaciones automáticas disponibles</li>
                <li>Soporte técnico incluido para todas las plataformas</li>
                <li>Configuración automática con tus credenciales</li>
            </ul>
        </div>

        <?php if (empty($apps_by_platform)): ?>
        <div class="empty-platform">
            <i class="fas fa-mobile-alt"></i>
            <h3>No hay aplicaciones disponibles</h3>
            <p>Las aplicaciones estarán disponibles próximamente</p>
        </div>
        <?php else: ?>
            <?php 
            $platform_icons = [
                'android' => 'fab fa-android',
                'ios' => 'fab fa-apple',
                'windows' => 'fab fa-windows',
                'macos' => 'fab fa-apple',
                'smart_tv' => 'fas fa-tv',
                'firestick' => 'fas fa-fire',
                'web' => 'fas fa-globe',
                'other' => 'fas fa-desktop'
            ];
            
            $platform_names = [
                'android' => 'Android',
                'ios' => 'iOS / iPhone / iPad',
                'windows' => 'Windows',
                'macos' => 'macOS',
                'smart_tv' => 'Smart TV',
                'firestick' => 'Amazon Fire TV / Firestick',
                'web' => 'Navegador Web',
                'other' => 'Otras Plataformas'
            ];
            ?>
            
            <?php foreach ($apps_by_platform as $platform => $platform_apps): ?>
            <div class="platform-section">
                <div class="platform-header">
                    <div class="platform-icon">
                        <i class="<?php echo $platform_icons[$platform] ?? 'fas fa-desktop'; ?>"></i>
                    </div>
                    <h2 class="platform-title">
                        <?php echo $platform_names[$platform] ?? ucfirst($platform); ?>
                    </h2>
                </div>
                
                <div class="apps-grid">
                    <?php foreach ($platform_apps as $app): ?>
                    <div class="app-card">
                        <div class="app-header">
                            <div class="app-icon">
                                <i class="<?php echo $platform_icons[$platform] ?? 'fas fa-mobile-alt'; ?>"></i>
                            </div>
                            <div class="app-info">
                                <h3 class="app-name"><?php echo htmlspecialchars($app['name']); ?></h3>
                                <div class="app-version">Versión <?php echo htmlspecialchars($app['version']); ?></div>
                                <?php if ($app['file_size']): ?>
                                <div class="app-size">Tamaño: <?php echo format_file_size($app['file_size']); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="app-description">
                            <?php echo nl2br(htmlspecialchars($app['description'])); ?>
                        </div>
                        
                        <?php if ($app['features']): ?>
                        <div class="app-features">
                            <div class="features-title">Características:</div>
                            <ul class="features-list">
                                <?php 
                                $features = explode("\n", $app['features']);
                                foreach ($features as $feature): 
                                    $feature = trim($feature);
                                    if ($feature):
                                ?>
                                <li><?php echo htmlspecialchars($feature); ?></li>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        
                        <div class="app-actions">
                            <?php if ($app['download_url']): ?>
                            <a href="<?php echo htmlspecialchars($app['download_url']); ?>" 
                               class="btn btn-success" 
                               target="_blank"
                               onclick="trackDownload('<?php echo $app['id']; ?>')">
                                <i class="fas fa-download"></i>
                                Descargar
                            </a>
                            <?php endif; ?>
                            
                            <?php if ($app['external_url']): ?>
                            <a href="<?php echo htmlspecialchars($app['external_url']); ?>" 
                               class="btn btn-primary" 
                               target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                Ver en Tienda
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </main>

    <script>
        // Función para rastrear descargas
        function trackDownload(appId) {
            // En una implementación real, esto enviaría estadísticas al servidor
            console.log('Descarga iniciada para app ID:', appId);
            
            // Opcional: mostrar mensaje de descarga
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: var(--border-radius);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = '<i class="fas fa-download"></i> Descarga iniciada...';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Animación de entrada para las tarjetas
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.app-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Detectar plataforma del usuario y resaltar
        function detectPlatform() {
            const userAgent = navigator.userAgent.toLowerCase();
            let platform = 'other';
            
            if (userAgent.includes('android')) {
                platform = 'android';
            } else if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
                platform = 'ios';
            } else if (userAgent.includes('windows')) {
                platform = 'windows';
            } else if (userAgent.includes('mac')) {
                platform = 'macos';
            }
            
            // Resaltar la sección de la plataforma detectada
            const platformSection = document.querySelector(`[data-platform="${platform}"]`);
            if (platformSection) {
                platformSection.style.border = '2px solid var(--accent-color)';
                platformSection.style.background = 'rgba(16, 185, 129, 0.05)';
                
                // Scroll suave a la sección
                setTimeout(() => {
                    platformSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }, 500);
            }
        }

        // Ejecutar detección de plataforma
        detectPlatform();

        // Agregar atributos de plataforma a las secciones
        document.querySelectorAll('.platform-section').forEach((section, index) => {
            const platforms = ['android', 'ios', 'windows', 'macos', 'smart_tv', 'firestick', 'web', 'other'];
            if (platforms[index]) {
                section.setAttribute('data-platform', platforms[index]);
            }
        });
    </script>
</body>
</html>
