<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Obtener estadísticas generales
try {
    // Estadísticas de tickets
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
    $tickets_open = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status IN ('open', 'in_progress')");
    $tickets_pending = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de chat
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
    $chat_active = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'waiting'");
    $chat_waiting = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de aplicaciones
    $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE status = 'published'");
    $apps_published = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de ayuda
    $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles WHERE status = 'published'");
    $help_articles = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de canales
    $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
    $channels_pending = $stmt->fetchColumn() ?: 0;
    
    // Estadísticas de activaciones
    $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE status = 'active'");
    $codes_active = $stmt->fetchColumn() ?: 0;
    
    // Actividades recientes
    $stmt = $pdo->query("
        SELECT 'ticket' as type, subject as title, created_at, 'open' as status 
        FROM support_tickets 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        UNION ALL
        SELECT 'chat' as type, CONCAT('Chat con usuario ', user_id) as title, started_at as created_at, status 
        FROM chat_sessions 
        WHERE started_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        UNION ALL
        SELECT 'channel' as type, channel_name as title, created_at, status 
        FROM channel_requests 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = "Error al obtener estadísticas: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ Panel de Administración Profesional - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --light-bg: #f8fafc;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #f8fafc;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--dark-bg);
            color: var(--text-light);
            z-index: 1000;
            overflow-y: auto;
            transition: var(--transition);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
            text-decoration: none;
        }

        .sidebar-nav {
            padding: 1.5rem 0;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            padding: 0 1.5rem 0.5rem;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: rgba(255, 255, 255, 0.6);
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            position: relative;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-light);
        }

        .nav-item.active {
            background: var(--primary-color);
            color: var(--text-light);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--accent-color);
        }

        .nav-badge {
            margin-left: auto;
            background: var(--error-color);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            min-width: 20px;
            text-align: center;
        }

        .nav-badge.success {
            background: var(--success-color);
        }

        .nav-badge.warning {
            background: var(--warning-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .top-bar {
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow-sm);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .top-bar-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background: white;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .content-area {
            padding: 2rem;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .stat-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .stat-icon.tickets {
            background: rgba(30, 64, 175, 0.1);
            color: var(--primary-color);
        }

        .stat-icon.chat {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-icon.apps {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.channels {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stat-change.positive {
            color: var(--success-color);
        }

        .stat-change.negative {
            color: var(--error-color);
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-content {
            padding: 1.5rem;
        }

        /* Activity List */
        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .quick-action {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: var(--light-bg);
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--text-primary);
            transition: var(--transition);
        }

        .quick-action:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .quick-action-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-action:hover .quick-action-icon {
            background: white;
            color: var(--primary-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-left: 0;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading Animation */
        .loading {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="admin_professional.php" class="sidebar-logo">
                <i class="fas fa-shield-alt"></i>
                <span>RGS Admin</span>
            </a>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Principal</div>
                <a href="admin_professional.php" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin2_professional.php" class="nav-item">
                    <i class="fas fa-cogs"></i>
                    <span>Servicios de Soporte</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Soporte</div>
                <a href="tickets_admin.php" class="nav-item">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Tickets</span>
                    <span class="nav-badge"><?php echo $tickets_pending; ?></span>
                </a>
                <a href="admin_chat_real.php" class="nav-item">
                    <i class="fas fa-comments"></i>
                    <span>Chat en Vivo</span>
                    <?php if ($chat_waiting > 0): ?>
                    <span class="nav-badge warning"><?php echo $chat_waiting; ?></span>
                    <?php endif; ?>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Contenido</div>
                <a href="apps_admin.php" class="nav-item">
                    <i class="fas fa-mobile-alt"></i>
                    <span>Aplicaciones</span>
                    <span class="nav-badge success"><?php echo $apps_published; ?></span>
                </a>
                <a href="help_admin.php" class="nav-item">
                    <i class="fas fa-question-circle"></i>
                    <span>Centro de Ayuda</span>
                    <span class="nav-badge success"><?php echo $help_articles; ?></span>
                </a>
                <a href="admin_channels_real.php" class="nav-item">
                    <i class="fas fa-tv"></i>
                    <span>Canales</span>
                    <?php if ($channels_pending > 0): ?>
                    <span class="nav-badge"><?php echo $channels_pending; ?></span>
                    <?php endif; ?>
                </a>
                <a href="activations_admin.php" class="nav-item">
                    <i class="fas fa-key"></i>
                    <span>Activaciones</span>
                    <span class="nav-badge success"><?php echo $codes_active; ?></span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Sistema</div>
                <a href="system_status.php" class="nav-item">
                    <i class="fas fa-server"></i>
                    <span>Estado del Sistema</span>
                </a>
                <a href="security_admin.php" class="nav-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>Seguridad</span>
                </a>
                <a href="notifications_admin.php" class="nav-item">
                    <i class="fas fa-bell"></i>
                    <span>Notificaciones</span>
                </a>
            </div>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Dashboard Principal</h1>
            <div class="top-bar-actions">
                <button class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    Exportar Datos
                </button>
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Ver Sitio
                </a>
            </div>
        </div>

        <div class="content-area">
            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Tickets de Soporte</div>
                        <div class="stat-icon tickets">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                    </div>
                    <div class="stat-number" id="ticketsCount"><?php echo $tickets_open; ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <?php echo $tickets_pending; ?> pendientes
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Chat en Vivo</div>
                        <div class="stat-icon chat">
                            <i class="fas fa-comments"></i>
                        </div>
                    </div>
                    <div class="stat-number" id="chatCount"><?php echo $chat_active; ?></div>
                    <div class="stat-change <?php echo $chat_waiting > 0 ? 'negative' : 'positive'; ?>">
                        <i class="fas fa-<?php echo $chat_waiting > 0 ? 'clock' : 'check'; ?>"></i>
                        <?php echo $chat_waiting; ?> en espera
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Aplicaciones</div>
                        <div class="stat-icon apps">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                    </div>
                    <div class="stat-number" id="appsCount"><?php echo $apps_published; ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-check"></i>
                        Publicadas
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Solicitudes de Canales</div>
                        <div class="stat-icon channels">
                            <i class="fas fa-tv"></i>
                        </div>
                    </div>
                    <div class="stat-number" id="channelsCount"><?php echo $channels_pending; ?></div>
                    <div class="stat-change <?php echo $channels_pending > 0 ? 'negative' : 'positive'; ?>">
                        <i class="fas fa-<?php echo $channels_pending > 0 ? 'clock' : 'check'; ?>"></i>
                        Pendientes
                    </div>
                </div>
            </div>

            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock"></i>
                            Actividad Reciente
                        </h3>
                    </div>
                    <div class="card-content">
                        <?php if (empty($recent_activities)): ?>
                        <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>No hay actividad reciente</p>
                        </div>
                        <?php else: ?>
                            <?php foreach ($recent_activities as $activity): ?>
                            <div class="activity-item">
                                <div class="activity-icon <?php echo $activity['type']; ?>">
                                    <i class="fas fa-<?php 
                                        echo $activity['type'] === 'ticket' ? 'ticket-alt' : 
                                            ($activity['type'] === 'chat' ? 'comments' : 'tv'); 
                                    ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title"><?php echo htmlspecialchars($activity['title']); ?></div>
                                    <div class="activity-time"><?php echo date('d/m/Y H:i', strtotime($activity['created_at'])); ?></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i>
                            Acciones Rápidas
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="quick-actions">
                            <a href="tickets_admin.php" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500;">Nuevo Ticket</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">Crear ticket manual</div>
                                </div>
                            </a>

                            <a href="admin_chat_real.php" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500;">Chat Activo</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">Gestionar chats</div>
                                </div>
                            </a>

                            <a href="apps_admin.php" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-upload"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500;">Subir App</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">Nueva aplicación</div>
                                </div>
                            </a>

                            <a href="help_admin.php" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500;">Nuevo Artículo</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">Centro de ayuda</div>
                                </div>
                            </a>

                            <a href="activations_admin.php" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500;">Generar Código</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">Nueva activación</div>
                                </div>
                            </a>

                            <a href="system_status.php" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500;">Estadísticas</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">Ver métricas</div>
                                </div>
                            </a>

                            <a href="admin2_professional.php" class="quick-action" style="border: 2px solid #e91e63; background: rgba(233, 30, 99, 0.05);">
                                <div class="quick-action-icon" style="background: #e91e63;">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500; color: #e91e63;">Soporte Admin2</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">
                                        <span id="admin2-notifications" style="color: #e91e63; font-weight: 600;">3</span> notificaciones importantes
                                    </div>
                                </div>
                            </a>

                            <a href="sports_admin.php" class="quick-action" style="border: 2px solid #10b981; background: rgba(16, 185, 129, 0.05);">
                                <div class="quick-action-icon" style="background: #10b981;">
                                    <i class="fas fa-tv"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 500; color: #10b981;">Deportes Admin</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">Configurar eventos y zona horaria</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Auto-refresh stats every 20 seconds
        setInterval(async function() {
            try {
                const response = await fetch('api_admin_stats.php');
                const data = await response.json();

                if (data.success && data.stats) {
                    // Update counters
                    document.getElementById('ticketsCount').textContent = data.stats.tickets_open || 0;
                    document.getElementById('chatCount').textContent = data.stats.chat_active || 0;
                    document.getElementById('appsCount').textContent = data.stats.apps_published || 0;
                    document.getElementById('channelsCount').textContent = data.stats.channels_pending || 0;

                    // Update badges in sidebar
                    updateSidebarBadges(data.stats);

                    // Show update indicator
                    showUpdateIndicator();
                }
            } catch (error) {
                console.log('Stats update failed:', error);
            }
        }, 20000);

        function updateSidebarBadges(data) {
            const badges = document.querySelectorAll('.nav-badge');
            badges.forEach((badge, index) => {
                const values = [
                    data.tickets_pending,
                    data.chat_waiting,
                    data.apps_published,
                    data.help_articles,
                    data.channels_pending,
                    data.codes_active
                ];
                if (values[index] !== undefined) {
                    badge.textContent = values[index];

                    // Update badge color based on value
                    badge.className = 'nav-badge';
                    if (index === 0 && values[index] > 5) badge.className += ' warning'; // Many pending tickets
                    if (index === 1 && values[index] > 0) badge.className += ' warning'; // Waiting chats
                    if (index === 2 || index === 3) badge.className += ' success'; // Published content
                    if (index === 4 && values[index] > 3) badge.className += ' warning'; // Many pending channels
                    if (index === 5) badge.className += ' success'; // Active codes
                }
            });
        }

        function showUpdateIndicator() {
            let indicator = document.getElementById('updateIndicator');
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.id = 'updateIndicator';
                indicator.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #10b981, #059669);
                    color: white;
                    padding: 8px 16px;
                    border-radius: 25px;
                    font-size: 0.8rem;
                    font-weight: 500;
                    z-index: 1000;
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                    transition: all 0.3s ease;
                    opacity: 0;
                    transform: translateY(10px);
                `;
                document.body.appendChild(indicator);
            }

            const now = new Date();
            const timeString = now.toLocaleTimeString('es-ES', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            indicator.innerHTML = `<i class="fas fa-sync-alt"></i> Actualizado: ${timeString}`;
            indicator.style.opacity = '1';
            indicator.style.transform = 'translateY(0)';

            setTimeout(() => {
                indicator.style.opacity = '0.7';
                indicator.style.transform = 'translateY(5px)';
            }, 3000);
        }

        // Add smooth transitions to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-2px)';
            });
        });

        // Loading states for quick actions
        document.querySelectorAll('.quick-action').forEach(action => {
            action.addEventListener('click', function(e) {
                const icon = this.querySelector('.quick-action-icon i');
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                setTimeout(() => {
                    icon.className = originalClass;
                }, 1000);
            });
        });

        // Update admin2 notifications
        function updateAdmin2Notifications() {
            fetch('api_admin_notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const notificationElement = document.getElementById('admin2-notifications');
                        if (notificationElement) {
                            notificationElement.textContent = data.data.unread_count;

                            // Update notification color based on count
                            const parentCard = notificationElement.closest('.quick-action');
                            if (data.data.unread_count > 5) {
                                parentCard.style.borderColor = '#ef4444';
                                parentCard.style.background = 'rgba(239, 68, 68, 0.1)';
                            } else if (data.data.unread_count > 0) {
                                parentCard.style.borderColor = '#e91e63';
                                parentCard.style.background = 'rgba(233, 30, 99, 0.05)';
                            }
                        }
                    }
                })
                .catch(error => console.log('Admin2 notifications update failed:', error));
        }

        // Update notifications every 30 seconds
        setInterval(updateAdmin2Notifications, 30000);

        // Initial load
        updateAdmin2Notifications();
    </script>
</body>
</html>
