-- Script para corregir la estructura de la base de datos
-- Ejecutar este script para reparar las tablas

-- Corregir tabla support_apps
ALTER TABLE support_apps 
ADD COLUMN IF NOT EXISTS features TEXT AFTER description,
ADD COLUMN IF NOT EXISTS download_url VARCHAR(500) AFTER file_size,
ADD COLUMN IF NOT EXISTS external_url VARCHAR(500) AFTER download_url,
ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active' AFTER is_active;

-- Corregir tabla ticket_responses (eliminar duplicado)
DROP TABLE IF EXISTS ticket_responses;
CREATE TABLE IF NOT EXISTS ticket_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    is_admin_response BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_created_at (created_at)
);

-- Corregir tabla support_tickets (agregar campo subject si no existe)
ALTER TABLE support_tickets 
ADD COLUMN IF NOT EXISTS subject VARCHAR(255) AFTER title;

-- Crear tabla para notificaciones en tiempo real
CREATE TABLE IF NOT EXISTS admin_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('ticket', 'chat', 'channel_request', 'app_download', 'system') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    reference_id INT NULL,
    reference_type VARCHAR(50) NULL,
    is_read BOOLEAN DEFAULT FALSE,
    admin_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_admin_id (admin_id),
    INDEX idx_created_at (created_at)
);

-- Crear tabla para estado de chat en tiempo real
CREATE TABLE IF NOT EXISTS chat_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id INT NOT NULL,
    admin_online BOOLEAN DEFAULT FALSE,
    user_online BOOLEAN DEFAULT FALSE,
    last_admin_activity TIMESTAMP NULL,
    last_user_activity TIMESTAMP NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_session (session_id)
);

-- Crear tabla para códigos de activación (si no existe)
CREATE TABLE IF NOT EXISTS activation_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    list_name VARCHAR(255),
    list_url VARCHAR(500),
    duration_days INT DEFAULT 30,
    max_uses INT DEFAULT 1,
    current_uses INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    INDEX idx_code (code),
    INDEX idx_is_active (is_active)
);

-- Insertar datos de ejemplo si las tablas están vacías
INSERT IGNORE INTO users (id, username, email, password_hash) VALUES
(1, 'usuario', '<EMAIL>', 'demo_hash'),
(2, 'admin', '<EMAIL>', 'admin_hash');

-- Insertar aplicaciones de ejemplo
INSERT IGNORE INTO support_apps (name, description, version, platform, file_path, file_size, features, download_url, status) VALUES
('IPTV Player Android', 'Reproductor IPTV optimizado para Android con soporte para múltiples formatos', '2.1.0', 'android', 'uploads/apps/iptv-player-android-2.1.0.apk', 15728640, 'EPG, Grabación, Favoritos, Control parental', 'https://play.google.com/store/apps/details?id=com.rgs.iptv', 'active'),
('IPTV Player iOS', 'Reproductor IPTV para dispositivos iOS con interfaz intuitiva', '2.0.5', 'ios', 'uploads/apps/iptv-player-ios-2.0.5.ipa', 18874368, 'AirPlay, Chromecast, Favoritos', 'https://apps.apple.com/app/rgs-iptv/id123456789', 'active'),
('IPTV Desktop', 'Cliente de escritorio para Windows y Mac con funciones avanzadas', '1.5.2', 'windows', 'uploads/apps/iptv-desktop-1.5.2.exe', 45678912, 'Múltiples ventanas, Grabación HD, Timeshift', 'https://rgs.com/downloads/desktop', 'active');

-- Insertar tickets de ejemplo
INSERT IGNORE INTO support_tickets (id, user_id, title, description, priority, category, status) VALUES
(6, 1, 'Problema con la reproducción', 'Los canales se cortan constantemente durante la reproducción', 'high', 'technical', 'open'),
(7, 1, 'Solicitud de canal', 'Me gustaría que agreguen CNN en español a la lista', 'medium', 'general', 'in_progress'),
(8, 1, 'Error en la aplicación', 'La app se cierra al abrir ciertos canales HD', 'urgent', 'bug_report', 'resolved');

-- Insertar respuestas de ejemplo para el ticket 6
INSERT IGNORE INTO ticket_responses (ticket_id, user_id, message, is_admin_response) VALUES
(6, 1, 'Hola, tengo problemas con la reproducción de los canales. Se cortan cada pocos minutos y tengo que reiniciar la aplicación.', FALSE),
(6, 2, 'Hola, gracias por contactarnos. ¿Podrías decirnos qué dispositivo estás usando y qué velocidad de internet tienes?', TRUE);

-- Insertar notificaciones de ejemplo
INSERT IGNORE INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES
('ticket', 'Nuevo Ticket #6', 'Usuario reporta problemas de reproducción', 6, 'support_tickets'),
('chat', 'Nueva sesión de chat', 'Usuario solicita ayuda en chat en vivo', 1, 'chat_sessions');
