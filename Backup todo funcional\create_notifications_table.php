<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔔 Creando tabla admin_notifications</h1>";

try {
    // Verificar si la tabla existe
    echo "<h2>🔍 Verificando tabla admin_notifications...</h2>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "<p>✅ La tabla admin_notifications ya existe</p>";
        
        // Verificar estructura
        $stmt = $pdo->query("SHOW COLUMNS FROM admin_notifications");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Columnas actuales:</h3>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li><strong>" . htmlspecialchars($column['Field']) . "</strong> - " . htmlspecialchars($column['Type']) . "</li>";
        }
        echo "</ul>";
        
        $column_names = array_column($columns, 'Field');
        
        // Agregar columnas faltantes
        $columns_to_add = [
            'reference_id' => "ALTER TABLE admin_notifications ADD COLUMN reference_id INT NULL AFTER message",
            'reference_type' => "ALTER TABLE admin_notifications ADD COLUMN reference_type VARCHAR(50) NULL AFTER reference_id"
        ];
        
        foreach ($columns_to_add as $column => $sql) {
            if (!in_array($column, $column_names)) {
                try {
                    $pdo->exec($sql);
                    $success_messages[] = "✅ Columna '$column' agregada a admin_notifications";
                } catch (Exception $e) {
                    $error_messages[] = "❌ Error agregando columna '$column': " . $e->getMessage();
                }
            } else {
                $success_messages[] = "ℹ️ Columna '$column' ya existe";
            }
        }
        
    } else {
        echo "<p>⚠️ La tabla admin_notifications no existe, creándola...</p>";
        
        // Crear tabla completa
        try {
            $pdo->exec("
                CREATE TABLE admin_notifications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    type ENUM('ticket', 'chat', 'channel_request', 'app_download', 'system') NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    reference_id INT NULL,
                    reference_type VARCHAR(50) NULL,
                    is_read BOOLEAN DEFAULT FALSE,
                    admin_id INT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_type (type),
                    INDEX idx_is_read (is_read),
                    INDEX idx_admin_id (admin_id),
                    INDEX idx_created_at (created_at)
                )
            ");
            $success_messages[] = "✅ Tabla admin_notifications creada correctamente";
        } catch (Exception $e) {
            $error_messages[] = "❌ Error creando tabla admin_notifications: " . $e->getMessage();
        }
    }
    
    // Insertar notificaciones de ejemplo
    echo "<h2>📝 Insertando notificaciones de ejemplo...</h2>";
    try {
        $stmt = $pdo->prepare("INSERT IGNORE INTO admin_notifications (id, type, title, message, reference_id, reference_type, is_read) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        // Notificación de ticket
        $stmt->execute([1, 'ticket', 'Nuevo Ticket #6', 'Usuario reporta problemas de reproducción', 6, 'support_tickets', 0]);
        
        // Notificación de chat
        $stmt->execute([2, 'chat', 'Nueva sesión de chat', 'Usuario solicita ayuda en chat en vivo', 1, 'chat_sessions', 0]);
        
        // Notificación de app
        $stmt->execute([3, 'app_download', 'Nueva descarga de app', 'Se descargó IPTV Player Android', 1, 'support_apps', 1]);
        
        // Notificación del sistema
        $stmt->execute([4, 'system', 'Sistema actualizado', 'Se aplicaron correcciones a la base de datos', NULL, NULL, 0]);
        
        $success_messages[] = "✅ Notificaciones de ejemplo insertadas";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error insertando notificaciones: " . $e->getMessage();
    }
    
    // Verificar estructura final
    echo "<h2>🔍 Verificación final...</h2>";
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM admin_notifications");
        $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Estructura final:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: #2a2a2a; color: white;'>";
        echo "<tr style='background: #333;'><th>Campo</th><th>Tipo</th><th>Null</th><th>Default</th></tr>";
        foreach ($final_columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Contar notificaciones
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_notifications");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $success_messages[] = "✅ Total de notificaciones en la tabla: $count";
        
        // Mostrar notificaciones no leídas
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_notifications WHERE is_read = 0");
        $unread = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $success_messages[] = "✅ Notificaciones no leídas: $unread";
        
    } catch (Exception $e) {
        $error_messages[] = "❌ Error en verificación final: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 Crear admin_notifications - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        h3 {
            color: #f59e0b;
            margin-top: 1.5rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
        table {
            margin: 1rem 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #555;
        }
        ul {
            background: #0f172a;
            padding: 1rem;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Tabla admin_notifications Configurada</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Operaciones Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="ticket_detail.php?id=6" class="btn">🎫 Probar Respuesta a Ticket</a>
            <a href="api_admin_notifications.php?action=get_notifications" class="btn">🔔 Ver API Notificaciones</a>
            <a href="admin2.php" class="btn">🏠 Panel Admin</a>
        </div>
    </div>
</body>
</html>
