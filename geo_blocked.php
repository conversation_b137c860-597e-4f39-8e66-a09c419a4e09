<?php
session_start();
require_once 'geo_restriction.php';

// Obtener información del país
$country_code = $_GET['country'] ?? 'XX';
$country_name = GeoRestriction::getCountryName($country_code);
$country_flag = GeoRestriction::getCountryFlag($country_code);
$user_ip = GeoRestriction::getUserIP();

// Lista de países permitidos
$allowed_countries = GeoRestriction::getAllowedCountriesList();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚫 Acceso Restringido - RGS Media TV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .container {
            max-width: 600px;
            width: 100%;
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 3rem;
            border: 1px solid var(--border-color);
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .blocked-icon {
            font-size: 4rem;
            color: var(--error-color);
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--error-color);
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .user-info {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid var(--error-color);
        }

        .user-location {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .country-flag {
            font-size: 2rem;
        }

        .ip-info {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-family: 'Courier New', monospace;
        }

        .allowed-countries {
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 2rem 0;
            text-align: left;
        }

        .allowed-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            text-align: center;
        }

        .countries-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .country-item {
            padding: 0.5rem;
            background: rgba(37, 99, 235, 0.1);
            border-radius: 6px;
            color: var(--text-secondary);
        }

        .contact-info {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid var(--warning-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin: 2rem 0;
            color: var(--warning-color);
        }

        .contact-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .footer {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .countries-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="blocked-icon">
            <i class="fas fa-globe-americas"></i>
            <i class="fas fa-ban" style="position: absolute; margin-left: -1rem; color: var(--error-color);"></i>
        </div>
        
        <h1 class="title">Acceso Restringido</h1>
        <p class="subtitle">Lo sentimos, nuestro servicio no está disponible en tu ubicación actual</p>
        
        <div class="user-info">
            <div class="user-location">
                <span class="country-flag"><?php echo $country_flag; ?></span>
                <span>Detectado desde: <strong><?php echo htmlspecialchars($country_name); ?></strong></span>
            </div>
            <div class="ip-info">
                IP: <?php echo htmlspecialchars($user_ip); ?>
            </div>
        </div>
        
        <div class="allowed-countries">
            <h3 class="allowed-title">
                <i class="fas fa-check-circle" style="color: var(--primary-color);"></i>
                Países con Acceso Disponible
            </h3>
            <div class="countries-grid">
                <?php foreach ($allowed_countries as $code => $name): ?>
                <div class="country-item"><?php echo htmlspecialchars($name); ?></div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="contact-info">
            <div class="contact-title">
                <i class="fas fa-envelope"></i>
                ¿Necesitas Ayuda?
            </div>
            <p>Si crees que esto es un error o necesitas acceso especial, contáctanos:</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>WhatsApp:</strong> +1 (555) 123-4567</p>
        </div>
        
        <div style="margin: 2rem 0;">
            <a href="mailto:<EMAIL>" class="btn">
                <i class="fas fa-envelope"></i>
                Contactar Soporte
            </a>
        </div>
        
        <div class="footer">
            <div class="logo">
                <i class="fas fa-tv"></i>
                RGS Media TV
            </div>
            <p>Servicio de IPTV Premium para Latinoamérica y Estados Unidos</p>
            <p style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.7;">
                Esta restricción se basa en tu ubicación geográfica detectada automáticamente.
            </p>
        </div>
    </div>

    <script>
        // Mostrar información adicional en consola para debugging
        console.log('🌍 Geo Restriction Info:');
        console.log('Country:', '<?php echo $country_code; ?>');
        console.log('IP:', '<?php echo $user_ip; ?>');
        console.log('Timestamp:', new Date().toISOString());
        
        // Efecto de partículas en el fondo
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: rgba(37, 99, 235, 0.3);
                border-radius: 50%;
                pointer-events: none;
                z-index: -1;
                left: ${Math.random() * 100}vw;
                top: 100vh;
                animation: float ${3 + Math.random() * 4}s linear forwards;
            `;
            
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 7000);
        }
        
        // Crear partículas cada 500ms
        setInterval(createParticle, 500);
        
        // CSS para animación de partículas
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                to {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
