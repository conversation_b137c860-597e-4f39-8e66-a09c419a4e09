<?php
// Este archivo solo imprime la tabla de pedidos para AJAX y para la vista principal
if (!isset($pdo)) {
    $db_host = 'localhost'; // Cambia por el host de tu hosting
    $db_name = 'u170528143_php'; // Cambia por el nombre de tu base de datos
    $db_user = 'u170528143_php'; // Cambia por tu usuario de MySQL
    $db_pass = '&T4v!$=i'; // Cambia por tu contraseña de MySQL

    try {
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } catch(PDOException $e) {
        die("Error de conexión a la base de datos: " . $e->getMessage());
    }
}
// Asegurar que la columna notif_seen existe (solo si migras desde SQLite y la tabla ya existe)
$cols = $pdo->query("SHOW COLUMNS FROM orders LIKE 'notif_seen'")->fetchAll(PDO::FETCH_ASSOC);
$has_notif_seen = count($cols) > 0;
if (!$has_notif_seen) {
    $pdo->exec("ALTER TABLE orders ADD COLUMN notif_seen TINYINT(1) DEFAULT 0");
}
// Cambia la consulta para traer también el username del usuario
$pedidos = $pdo->query("
    SELECT o.*, u.username 
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    ORDER BY o.created_at DESC
")->fetchAll(PDO::FETCH_ASSOC);

// Separar pedidos nuevos (no leídos, status 'Listo' y notif_seen=0) y pedidos viejos (status 'Disponible' o notif_seen=1)
$nuevos_pedidos = [];
$viejos_pedidos = [];
foreach ($pedidos as $pedido) {
    if ($pedido['status'] === 'Listo' && empty($pedido['notif_seen'])) {
        $nuevos_pedidos[] = $pedido;
    } elseif ($pedido['status'] === 'Disponible' || (!empty($pedido['notif_seen']) && $pedido['status'] === 'Listo')) {
        $viejos_pedidos[] = $pedido;
    }
}
?>
<style>
    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: #1e293b;
        margin-top: 1rem;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0,0,0,0.4);
        border: 1px solid #334155;
    }
    .admin-table th, .admin-table td {
        padding: 12px 16px;
        border: 1px solid #334155;
        text-align: center;
    }
    .admin-table th {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: #f8fafc;
        font-size: 1rem;
        font-weight: 600;
    }
    .admin-table tr:nth-child(even) { background: #0f172a; }
    .badge {
        display: inline-block;
        padding: 0.3em 0.8em;
        border-radius: 12px;
        font-size: 0.9em;
        font-weight: bold;
        color: #fff;
    }
    .badge-Recibido { background: #2563eb; }
    .badge-En\ cola { background: #f59e0b; }
    .badge-Procesado { background: #8b5cf6; }
    .badge-Listo { background: #10b981; }
    .user-id {
        background: #2563eb;
        color: #f8fafc;
        border-radius: 8px;
        padding: 2px 8px;
        font-size: 0.95em;
        font-weight: bold;
    }
    .btn-archivar {
        background: #ef4444;
        color: #f8fafc;
        border: none;
        border-radius: 8px;
        padding: 6px 14px;
        cursor: pointer;
        margin-left: 5px;
        font-weight: bold;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .btn-archivar:hover { background: #dc2626; transform: translateY(-1px); }
    .btn-actualizar {
        background: #2563eb;
        color: #f8fafc;
        border: none;
        border-radius: 8px;
        padding: 6px 14px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .btn-actualizar:hover { background: #1d4ed8; transform: translateY(-1px); }
    select {
        background: #0f172a;
        color: #f8fafc;
        border-radius: 8px;
        border: 1px solid #334155;
        padding: 6px 10px;
        font-size: 0.9rem;
    }
    select:focus {
        outline: none;
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
    }
    /* --- Responsive Table for Admin --- */
    @media (max-width: 900px) {
        .admin-table, .admin-table thead, .admin-table tbody, .admin-table th, .admin-table td, .admin-table tr {
            display: block;
            width: 100%;
        }
        .admin-table thead { display: none; }
        .admin-table tr {
            margin-bottom: 1.2em;
            background: #232323;
            border-radius: 8px;
            box-shadow: 0 1px 4px #0003;
        }
        .admin-table td {
            border: none;
            border-bottom: 1px solid #333;
            position: relative;
            padding-left: 48%;
            min-height: 40px;
            text-align: left;
            white-space: normal;
        }
        .admin-table td:last-child { border-bottom: 0; }
        .admin-table td:before {
            position: absolute;
            top: 0; left: 0; width: 46%; height: 100%;
            padding-left: 12px;
            font-weight: bold;
            color: #90cea1;
            white-space: nowrap;
            content: attr(data-label);
            display: flex; align-items: center;
        }
    }
</style>

<!-- NUEVOS PEDIDOS (No leídos, status Listo y notif_seen=0) -->
<h3 style="color:#01b4e4;margin-top:1.5em;">Pedidos nuevos (no leídos)</h3>
<table class="admin-table">
    <tr>
        <th>ID</th>
        <th>Título</th>
        <th>Tipo</th>
        <th>Año</th>
        <th>País</th>
        <th>Ciudad</th>
        <th>IP</th>
        <th>User</th>
        <th>Estado</th>
        <th>Fecha</th>
        <th>Notificar</th>
        <th>Acción</th>
    </tr>
    <?php if (count($nuevos_pedidos) === 0): ?>
        <tr><td colspan="12" style="color:#aaa;">No hay pedidos nuevos.</td></tr>
    <?php endif; ?>
    <?php foreach ($nuevos_pedidos as $pedido): ?>
        <tr>
            <td data-label="ID"><?php echo $pedido['id']; ?></td>
            <td data-label="Título">
                <a href="https://www.themoviedb.org/<?php echo htmlspecialchars($pedido['media_type']); ?>/<?php echo htmlspecialchars($pedido['tmdb_id']); ?>?language=es-MX" target="_blank" style="color:#01b4e4;text-decoration:underline;">
                    <?php echo htmlspecialchars($pedido['title']); ?>
                </a>
            </td>
            <td data-label="Tipo"><?php echo htmlspecialchars($pedido['media_type']); ?></td>
            <td data-label="Año"><?php echo htmlspecialchars($pedido['year']); ?></td>
            <td data-label="País"><?php echo htmlspecialchars($pedido['country']); ?></td>
            <td data-label="Ciudad"><?php echo htmlspecialchars($pedido['city']); ?></td>
            <td data-label="IP"><?php echo htmlspecialchars($pedido['ip_address']); ?></td>
            <td data-label="User">
                <span class="user-id"><?php echo htmlspecialchars($pedido['user_id']); ?></span>
                <?php if (!empty($pedido['username'])): ?>
                    <span style="color:#90cea1;font-weight:bold;">(<?php echo htmlspecialchars($pedido['username']); ?>)</span>
                <?php endif; ?>
            </td>
            <td data-label="Estado"><span class="badge badge-Listo">Listo</span></td>
            <td data-label="Fecha"><?php echo htmlspecialchars($pedido['created_at']); ?></td>
            <td data-label="Notificar"><span style="color:#f39c12;font-weight:bold;">Pendiente</span></td>
            <td data-label="Acción">
                <form method="POST" style="display:inline;">
                    <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                    <select name="nuevo_estado">
                        <option value="Recibido" <?php if($pedido['status']==='Recibido') echo 'selected'; ?>>Recibido</option>
                        <option value="En cola" <?php if($pedido['status']==='En cola') echo 'selected'; ?>>En cola</option>
                        <option value="Procesado" <?php if($pedido['status']==='Procesado') echo 'selected'; ?>>Procesado</option>
                        <option value="Listo" <?php if($pedido['status']==='Listo') echo 'selected'; ?>>Listo</option>
                        <option value="Disponible" <?php if($pedido['status']==='Disponible') echo 'selected'; ?>>Disponible</option>
                    </select>
                    <button type="submit" class="btn-actualizar">Actualizar</button>
                </form>
                <form method="POST" style="display:inline;">
                    <input type="hidden" name="borrar_pedido_id" value="<?php echo $pedido['id']; ?>">
                    <button type="submit" class="btn-archivar">Archivar</button>
                </form>
            </td>
        </tr>
    <?php endforeach; ?>
</table>

<!-- VIEJOS PEDIDOS (Vistos, status Disponible o notif_seen=1) -->
<h3 style="color:#90cea1;margin-top:2.5em;">Pedidos vistos (disponibles)</h3>
<table class="admin-table">
    <tr>
        <th>ID</th>
        <th>Título</th>
        <th>Tipo</th>
        <th>Año</th>
        <th>País</th>
        <th>Ciudad</th>
        <th>IP</th>
        <th>User</th>
        <th>Estado</th>
        <th>Fecha</th>
        <th>Notificar</th>
        <th>Acción</th>
    </tr>
    <?php if (count($viejos_pedidos) === 0): ?>
        <tr><td colspan="12" style="color:#aaa;">No hay pedidos vistos/disponibles.</td></tr>
    <?php endif; ?>
    <?php foreach ($viejos_pedidos as $pedido): ?>
        <tr>
            <td data-label="ID"><?php echo $pedido['id']; ?></td>
            <td data-label="Título">
                <a href="https://www.themoviedb.org/<?php echo htmlspecialchars($pedido['media_type']); ?>/<?php echo htmlspecialchars($pedido['tmdb_id']); ?>?language=es-MX" target="_blank" style="color:#01b4e4;text-decoration:underline;">
                    <?php echo htmlspecialchars($pedido['title']); ?>
                </a>
            </td>
            <td data-label="Tipo"><?php echo htmlspecialchars($pedido['media_type']); ?></td>
            <td data-label="Año"><?php echo htmlspecialchars($pedido['year']); ?></td>
            <td data-label="País"><?php echo htmlspecialchars($pedido['country']); ?></td>
            <td data-label="Ciudad"><?php echo htmlspecialchars($pedido['city']); ?></td>
            <td data-label="IP"><?php echo htmlspecialchars($pedido['ip_address']); ?></td>
            <td data-label="User">
                <span class="user-id"><?php echo htmlspecialchars($pedido['user_id']); ?></span>
                <?php if (!empty($pedido['username'])): ?>
                    <span style="color:#90cea1;font-weight:bold;">(<?php echo htmlspecialchars($pedido['username']); ?>)</span>
                <?php endif; ?>
            </td>
            <td data-label="Estado"><span class="badge badge-Disponible">Disponible</span></td>
            <td data-label="Fecha"><?php echo htmlspecialchars($pedido['created_at']); ?></td>
            <td data-label="Notificar"><span style="color:#01b4e4;font-weight:bold;">Disponible</span></td>
            <td data-label="Acción">
                <form method="POST" style="display:inline;">
                    <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                    <select name="nuevo_estado">
                        <option value="Recibido" <?php if($pedido['status']==='Recibido') echo 'selected'; ?>>Recibido</option>
                        <option value="En cola" <?php if($pedido['status']==='En cola') echo 'selected'; ?>>En cola</option>
                        <option value="Procesado" <?php if($pedido['status']==='Procesado') echo 'selected'; ?>>Procesado</option>
                        <option value="Listo" <?php if($pedido['status']==='Listo') echo 'selected'; ?>>Listo</option>
                        <option value="Disponible" <?php if($pedido['status']==='Disponible') echo 'selected'; ?>>Disponible</option>
                    </select>
                    <button type="submit" class="btn-actualizar">Actualizar</button>
                </form>
                <form method="POST" style="display:inline;">
                    <input type="hidden" name="borrar_pedido_id" value="<?php echo $pedido['id']; ?>">
                    <button type="submit" class="btn-archivar">Archivar</button>
                </form>
            </td>
        </tr>
    <?php endforeach; ?>
</table>
<?php
// --- ARCHIVAR PEDIDO EN HISTORIAL ANTES DE BORRAR ---
if (isset($_POST['borrar_pedido_id'])) {
    $pedido_id = $_POST['borrar_pedido_id'];
    // Obtener el pedido antes de borrar
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$pedido_id]);
    $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($pedido) {
        // Crear tabla historial si no existe
        $pdo->exec("CREATE TABLE IF NOT EXISTS orders_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            title TEXT,
            media_type TEXT,
            year TEXT,
            country TEXT,
            city TEXT,
            ip_address TEXT,
            status TEXT,
            created_at TEXT,
            tmdb_id TEXT,
            notif_seen INTEGER DEFAULT 0,
            archived_at TEXT
        )");
        // Insertar en historial
        $stmt2 = $pdo->prepare("INSERT INTO orders_history (user_id, title, media_type, year, country, city, ip_address, status, created_at, tmdb_id, notif_seen, archived_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))");
        $stmt2->execute([
            $pedido['user_id'], $pedido['title'], $pedido['media_type'], $pedido['year'],
            $pedido['country'], $pedido['city'], $pedido['ip_address'], $pedido['status'],
            $pedido['created_at'], $pedido['tmdb_id'], $pedido['notif_seen']
        ]);
    }
    // Borrar el pedido original
    $stmt = $pdo->prepare("DELETE FROM orders WHERE id = ?");
    $stmt->execute([$pedido_id]);
}
?>
<script>
    // Función para obtener y actualizar la tabla de pedidos
    function fetchPedidosAdmin() {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', 'fetch_pedidos_admin.php', true);
        xhr.onload = function() {
            if (this.status === 200) {
                // Actualizar el contenido de la tabla
                document.querySelector('.admin-table').outerHTML = this.responseText;
            }
        };
        xhr.send();
    }
    setInterval(fetchPedidosAdmin, 2000); // Refresh cada 2 segundos (antes 5000)
</script>
