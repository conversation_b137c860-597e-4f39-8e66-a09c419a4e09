<?php
/**
 * Configuración TMDB API
 * RGS TOOL - The Movie Database Integration
 *
 * Para obtener una API key gratuita:
 * 1. Ve a https://www.themoviedb.org/
 * 2. Crea una cuenta gratuita
 * 3. Ve a Settings > API
 * 4. Solicita una API key (es gratis)
 * 5. Reemplaza la key de abajo con la tuya
 */

// API Key de TMDB - Configurada correctamente
define('TMDB_API_KEY', '201066b4b17391d478e55247f43eed64');

// URL base de TMDB
define('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
define('TMDB_IMAGE_BASE_URL', 'https://image.tmdb.org/t/p');

// Configuración de idioma
define('TMDB_LANGUAGE', 'es-ES');

// Función para verificar si TMDB está configurado
function isTMDBConfigured() {
    return TMDB_API_KEY !== 'tu_api_key_aqui' && !empty(TMDB_API_KEY);
}

// Función para obtener URL de imagen TMDB
function getTMDBImageUrl($path, $size = 'w500') {
    if (empty($path)) return null;
    return TMDB_IMAGE_BASE_URL . '/' . $size . $path;
}

// Función para hacer peticiones a TMDB
function makeTMDBRequest($endpoint, $params = []) {
    if (!isTMDBConfigured()) {
        return null;
    }
    
    $params['api_key'] = TMDB_API_KEY;
    $params['language'] = TMDB_LANGUAGE;
    
    $url = TMDB_BASE_URL . $endpoint . '?' . http_build_query($params);
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (compatible; RGS-TOOL/1.0)'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response === false) {
        return null;
    }
    
    return json_decode($response, true);
}

// Función para buscar en TMDB
function searchTMDBContent($query, $type = 'multi') {
    return makeTMDBRequest("/search/$type", ['query' => $query]);
}

// Función para obtener detalles de contenido
function getTMDBContentDetails($id, $type) {
    return makeTMDBRequest("/$type/$id");
}
?>
