<?php
/**
 * API para gestión de notificaciones entre paneles de administración
 */

session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Verificar que sea un administrador
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    // Crear tabla de notificaciones si no existe
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            reference_id INT,
            reference_type VARCHAR(50),
            source_admin VARCHAR(50) DEFAULT 'system',
            target_admin VARCHAR(50) DEFAULT 'all',
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL,
            INDEX idx_type (type),
            INDEX idx_read (is_read),
            INDEX idx_created (created_at),
            INDEX idx_target (target_admin)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    switch ($action) {
        case 'create':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            $type = $_POST['type'] ?? '';
            $title = $_POST['title'] ?? '';
            $message = $_POST['message'] ?? '';
            $reference_id = $_POST['reference_id'] ?? null;
            $reference_type = $_POST['reference_type'] ?? null;
            $priority = $_POST['priority'] ?? 'normal';
            $target_admin = $_POST['target_admin'] ?? 'all';
            
            if (empty($type) || empty($title) || empty($message)) {
                throw new Exception('Missing required fields');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO admin_notifications 
                (type, title, message, reference_id, reference_type, priority, target_admin, source_admin) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $type, $title, $message, $reference_id, $reference_type, 
                $priority, $target_admin, $_SESSION['admin_username'] ?? 'system'
            ]);
            
            $notification_id = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'notification_id' => $notification_id,
                'message' => 'Notification created successfully'
            ]);
            break;
            
        case 'get_unread':
            $limit = (int)($_GET['limit'] ?? 20);
            $target_admin = $_GET['target_admin'] ?? 'all';
            
            $where_clause = "WHERE is_read = FALSE";
            $params = [];
            
            if ($target_admin !== 'all') {
                $where_clause .= " AND (target_admin = ? OR target_admin = 'all')";
                $params[] = $target_admin;
            }
            
            $stmt = $pdo->prepare("
                SELECT * FROM admin_notifications 
                $where_clause
                ORDER BY 
                    CASE priority 
                        WHEN 'urgent' THEN 1 
                        WHEN 'high' THEN 2 
                        WHEN 'normal' THEN 3 
                        WHEN 'low' THEN 4 
                    END,
                    created_at DESC
                LIMIT ?
            ");
            $params[] = $limit;
            $stmt->execute($params);
            
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'count' => count($notifications)
            ]);
            break;
            
        case 'mark_read':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            $notification_id = (int)($_POST['notification_id'] ?? 0);
            
            if (!$notification_id) {
                throw new Exception('Invalid notification ID');
            }
            
            $stmt = $pdo->prepare("
                UPDATE admin_notifications 
                SET is_read = TRUE, read_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$notification_id]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
            break;
            
        case 'mark_all_read':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            $target_admin = $_POST['target_admin'] ?? 'all';
            
            $where_clause = "WHERE is_read = FALSE";
            $params = [];
            
            if ($target_admin !== 'all') {
                $where_clause .= " AND (target_admin = ? OR target_admin = 'all')";
                $params[] = $target_admin;
            }
            
            $stmt = $pdo->prepare("
                UPDATE admin_notifications 
                SET is_read = TRUE, read_at = NOW() 
                $where_clause
            ");
            $stmt->execute($params);
            
            $affected = $stmt->rowCount();
            
            echo json_encode([
                'success' => true,
                'marked_count' => $affected,
                'message' => "Marked $affected notifications as read"
            ]);
            break;
            
        case 'get_stats':
            // Estadísticas de notificaciones
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total_notifications,
                    SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN priority = 'urgent' AND is_read = FALSE THEN 1 ELSE 0 END) as urgent_count,
                    SUM(CASE WHEN priority = 'high' AND is_read = FALSE THEN 1 ELSE 0 END) as high_count,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_count
                FROM admin_notifications
            ");
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Estadísticas por tipo
            $stmt = $pdo->query("
                SELECT type, COUNT(*) as count
                FROM admin_notifications 
                WHERE is_read = FALSE
                GROUP BY type
                ORDER BY count DESC
            ");
            $by_type = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'stats' => $stats,
                'by_type' => $by_type
            ]);
            break;
            
        case 'create_chat_notification':
            // Notificación específica para nuevos chats
            $user_id = $_POST['user_id'] ?? '';
            $message_preview = $_POST['message_preview'] ?? '';
            $chat_id = $_POST['chat_id'] ?? '';
            
            if (empty($user_id) || empty($message_preview)) {
                throw new Exception('Missing chat data');
            }
            
            $title = "💬 Nuevo mensaje de chat";
            $message = "Usuario ID: $user_id\nMensaje: " . substr($message_preview, 0, 100) . "...";
            
            $stmt = $pdo->prepare("
                INSERT INTO admin_notifications 
                (type, title, message, reference_id, reference_type, priority, target_admin) 
                VALUES ('chat_message', ?, ?, ?, 'chat', 'high', 'all')
            ");
            $stmt->execute([$title, $message, $chat_id]);
            
            echo json_encode([
                'success' => true,
                'notification_id' => $pdo->lastInsertId(),
                'message' => 'Chat notification created'
            ]);
            break;
            
        case 'create_ticket_notification':
            // Notificación específica para tickets
            $ticket_id = $_POST['ticket_id'] ?? '';
            $ticket_subject = $_POST['ticket_subject'] ?? '';
            $user_name = $_POST['user_name'] ?? '';
            
            if (empty($ticket_id) || empty($ticket_subject)) {
                throw new Exception('Missing ticket data');
            }
            
            $title = "🎫 Nuevo ticket de soporte";
            $message = "Ticket #$ticket_id\nUsuario: $user_name\nAsunto: $ticket_subject";
            
            $stmt = $pdo->prepare("
                INSERT INTO admin_notifications 
                (type, title, message, reference_id, reference_type, priority, target_admin) 
                VALUES ('support_ticket', ?, ?, ?, 'ticket', 'normal', 'all')
            ");
            $stmt->execute([$title, $message, $ticket_id]);
            
            echo json_encode([
                'success' => true,
                'notification_id' => $pdo->lastInsertId(),
                'message' => 'Ticket notification created'
            ]);
            break;
            
        case 'delete':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            $notification_id = (int)($_POST['notification_id'] ?? 0);
            
            if (!$notification_id) {
                throw new Exception('Invalid notification ID');
            }
            
            $stmt = $pdo->prepare("DELETE FROM admin_notifications WHERE id = ?");
            $stmt->execute([$notification_id]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Notification deleted'
            ]);
            break;
            
        case 'cleanup':
            // Limpiar notificaciones antiguas (más de 30 días)
            $stmt = $pdo->prepare("
                DELETE FROM admin_notifications 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");
            $stmt->execute();
            
            $deleted = $stmt->rowCount();
            
            echo json_encode([
                'success' => true,
                'deleted_count' => $deleted,
                'message' => "Cleaned up $deleted old notifications"
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
