<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔄 Recreando Tabla help_articles</h1>";

try {
    // 1. Hacer backup de datos existentes si la tabla existe
    echo "<h2>💾 Haciendo backup de datos existentes...</h2>";
    
    $backup_data = [];
    try {
        $stmt = $pdo->query("SELECT * FROM help_articles");
        $backup_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $success_messages[] = "✅ Backup de " . count($backup_data) . " registros realizado";
    } catch (Exception $e) {
        $success_messages[] = "ℹ️ No hay datos existentes para hacer backup";
    }
    
    // 2. Eliminar tabla existente
    echo "<h2>🗑️ Eliminando tabla existente...</h2>";
    try {
        $pdo->exec("DROP TABLE IF EXISTS help_articles");
        $success_messages[] = "✅ Tabla help_articles eliminada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error eliminando tabla: " . $e->getMessage();
    }
    
    // 3. Crear tabla nueva con estructura completa
    echo "<h2>🏗️ Creando tabla nueva...</h2>";
    try {
        $pdo->exec("
            CREATE TABLE help_articles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                excerpt TEXT,
                category ENUM('faq', 'tutorial', 'guide', 'troubleshooting', 'setup', 'billing') DEFAULT 'faq',
                subcategory VARCHAR(100),
                status ENUM('draft', 'published', 'archived') DEFAULT 'published',
                is_featured BOOLEAN DEFAULT FALSE,
                view_count INT DEFAULT 0,
                author_id INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_category (category),
                INDEX idx_status (status),
                INDEX idx_featured (is_featured),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $success_messages[] = "✅ Tabla help_articles creada con estructura completa";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando tabla: " . $e->getMessage();
        throw $e;
    }
    
    // 4. Verificar estructura
    echo "<h2>🔍 Verificando estructura...</h2>";
    $stmt = $pdo->query("DESCRIBE help_articles");
    $columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
    }
    
    $required_columns = ['id', 'title', 'content', 'excerpt', 'category', 'subcategory', 'status', 'is_featured', 'view_count'];
    $missing_columns = array_diff($required_columns, $columns);
    
    if (empty($missing_columns)) {
        $success_messages[] = "✅ Estructura verificada: " . implode(', ', $columns);
    } else {
        $error_messages[] = "❌ Columnas faltantes: " . implode(', ', $missing_columns);
    }
    
    // 5. Restaurar datos del backup si existen
    echo "<h2>📥 Restaurando datos del backup...</h2>";
    if (!empty($backup_data)) {
        $stmt = $pdo->prepare("
            INSERT INTO help_articles (title, content, excerpt, category, subcategory, status, is_featured, view_count, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $restored = 0;
        foreach ($backup_data as $row) {
            try {
                $excerpt = isset($row['excerpt']) ? $row['excerpt'] : substr(strip_tags($row['content']), 0, 200) . '...';
                $subcategory = isset($row['subcategory']) ? $row['subcategory'] : null;
                $status = isset($row['status']) ? $row['status'] : 'published';
                
                $stmt->execute([
                    $row['title'],
                    $row['content'],
                    $excerpt,
                    $row['category'] ?? 'faq',
                    $subcategory,
                    $status,
                    $row['is_featured'] ?? 0,
                    $row['view_count'] ?? 0,
                    $row['created_at'] ?? date('Y-m-d H:i:s'),
                    $row['updated_at'] ?? date('Y-m-d H:i:s')
                ]);
                $restored++;
            } catch (Exception $e) {
                $error_messages[] = "❌ Error restaurando '{$row['title']}': " . $e->getMessage();
            }
        }
        $success_messages[] = "✅ Restaurados $restored registros del backup";
    }
    
    // 6. Insertar artículos de ejemplo si no hay datos
    echo "<h2>📝 Insertando artículos de ejemplo...</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles");
    $existing_count = $stmt->fetchColumn();
    
    if ($existing_count == 0) {
        $sample_articles = [
            [
                'title' => '¿Cómo configurar IPTV en Android?',
                'content' => "Para configurar IPTV en Android, sigue estos pasos:\n\n1. **Descarga la aplicación IPTV** desde Play Store o desde nuestro centro de descargas\n2. **Abre la aplicación** una vez instalada\n3. **Ve a Configuración** > Agregar lista\n4. **Introduce la URL de tu lista M3U** que recibiste por email\n5. **Guarda la configuración** y espera a que cargue\n\n¡Tu lista IPTV estará lista para usar! Si tienes problemas, contacta nuestro soporte.",
                'excerpt' => 'Guía paso a paso para configurar IPTV en dispositivos Android con aplicaciones compatibles',
                'category' => 'tutorial',
                'subcategory' => 'android',
                'status' => 'published',
                'is_featured' => 1
            ],
            [
                'title' => '¿Qué hacer si los canales no cargan?',
                'content' => "Si los canales no cargan, verifica lo siguiente:\n\n**1. Conexión a Internet**\n- Verifica que tengas conexión estable\n- Velocidad mínima recomendada: 10 Mbps para HD\n\n**2. URL de la Lista**\n- Asegúrate de que la URL sea correcta\n- Verifica que no haya espacios extra\n\n**3. Suscripción Activa**\n- Confirma que tu suscripción no haya expirado\n- Revisa tu email de activación\n\n**4. Reiniciar Aplicación**\n- Cierra completamente la app\n- Vuelve a abrirla\n\n**5. Contactar Soporte**\n- Si el problema persiste, contáctanos",
                'excerpt' => 'Soluciones paso a paso para problemas de carga de canales IPTV',
                'category' => 'troubleshooting',
                'subcategory' => 'playback',
                'status' => 'published',
                'is_featured' => 1
            ],
            [
                'title' => '¿Cómo activar mi código de suscripción?',
                'content' => "Para activar tu código de suscripción:\n\n**Paso 1: Acceder al Panel**\n- Ve a la sección \"Activación\" en nuestro sitio web\n- O usa el enlace que recibiste por email\n\n**Paso 2: Introducir Código**\n- Introduce tu código de activación (12 dígitos)\n- Asegúrate de escribirlo correctamente\n\n**Paso 3: Seleccionar Duración**\n- Elige la duración de tu suscripción\n- Confirma los detalles\n\n**Paso 4: Activar**\n- Haz clic en \"Activar\"\n- Espera la confirmación\n\n**Paso 5: Recibir Lista**\n- Recibirás tu lista M3U por email\n- Guarda el enlace en lugar seguro",
                'excerpt' => 'Proceso completo de activación de códigos de suscripción IPTV',
                'category' => 'setup',
                'subcategory' => 'activation',
                'status' => 'published',
                'is_featured' => 0
            ],
            [
                'title' => 'Requisitos del Sistema',
                'content' => "**Requisitos Mínimos para IPTV:**\n\n**📱 Android:**\n- Android 5.0 o superior\n- 2GB RAM mínimo\n- Conexión a internet estable\n- Aplicación IPTV compatible\n\n**🍎 iOS:**\n- iOS 12.0 o superior\n- iPhone 6s o superior\n- iPad Air 2 o superior\n\n**📺 Smart TV:**\n- Android TV 7.0+\n- Samsung Tizen 4.0+\n- LG webOS 4.0+\n- Roku OS 9.0+\n\n**💻 PC/Mac:**\n- Windows 10 o macOS 10.14+\n- VLC Media Player o similar\n- Navegador web moderno\n\n**🌐 Internet:**\n- Velocidad mínima: 5 Mbps para SD\n- Recomendado: 10 Mbps para HD\n- Óptimo: 25 Mbps para 4K",
                'excerpt' => 'Requisitos técnicos completos para usar servicios IPTV en diferentes dispositivos',
                'category' => 'faq',
                'subcategory' => 'requirements',
                'status' => 'published',
                'is_featured' => 0
            ],
            [
                'title' => 'Solucionar Problemas de Buffering',
                'content' => "**Para solucionar problemas de buffering:**\n\n**🔍 Diagnóstico:**\n1. **Verifica tu velocidad de internet**\n   - Usa speedtest.net\n   - Mínimo 10 Mbps para HD\n\n2. **Prueba en diferentes horarios**\n   - El buffering puede ser por congestión\n\n**🔧 Soluciones:**\n\n**Inmediatas:**\n- Cambia la calidad de video a una menor\n- Pausa el video 30 segundos para que cargue\n- Cierra otras aplicaciones que usen internet\n\n**Técnicas:**\n- Reinicia tu router/modem\n- Usa conexión por cable en lugar de WiFi\n- Cambia el servidor DNS (*******)\n\n**Avanzadas:**\n- Contacta a tu ISP si el problema persiste\n- Considera cambiar de proveedor de internet\n- Verifica que no haya descargas activas",
                'excerpt' => 'Guía completa para solucionar problemas de buffering y interrupciones en IPTV',
                'category' => 'troubleshooting',
                'subcategory' => 'buffering',
                'status' => 'published',
                'is_featured' => 1
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO help_articles (title, content, excerpt, category, subcategory, status, is_featured) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $inserted = 0;
        foreach ($sample_articles as $article) {
            try {
                $stmt->execute([
                    $article['title'],
                    $article['content'],
                    $article['excerpt'],
                    $article['category'],
                    $article['subcategory'],
                    $article['status'],
                    $article['is_featured']
                ]);
                $inserted++;
            } catch (Exception $e) {
                $error_messages[] = "❌ Error insertando '{$article['title']}': " . $e->getMessage();
            }
        }
        
        $success_messages[] = "✅ Insertados $inserted artículos de ejemplo";
    } else {
        $success_messages[] = "ℹ️ Ya existen $existing_count artículos, no se insertan ejemplos";
    }
    
    // 7. Estadísticas finales
    echo "<h2>📊 Estadísticas finales...</h2>";
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
            SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured
        FROM help_articles
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $success_messages[] = "📊 Total de artículos: " . $stats['total'];
    $success_messages[] = "📊 Artículos publicados: " . $stats['published'];
    $success_messages[] = "📊 Artículos destacados: " . $stats['featured'];
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Recrear Tabla - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Tabla help_articles Recreada</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Operaciones Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="user_help.php" class="btn">👥 Centro de Ayuda</a>
            <a href="help_admin.php" class="btn">⚙️ Admin Ayuda</a>
            <a href="api_help_system.php?action=get_articles" class="btn">🔌 Test API</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
        
        <?php if (count($error_messages) == 0): ?>
        <div style="background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; border-radius: 12px; padding: 2rem; margin: 2rem 0; text-align: center;">
            <h2 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Tabla Recreada Exitosamente!</h2>
            <p style="color: #10b981; font-size: 1.1rem;">
                La tabla help_articles ha sido recreada con la estructura correcta y los datos han sido restaurados.
            </p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
