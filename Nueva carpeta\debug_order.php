<?php
session_start();

// Verificar si el usuario está logueado (igual que en index.php)
if (!isset($_SESSION['user_id'])) {
    echo "<h1>Debug de Pedidos - Usuario no logueado</h1>";
    echo "<p style='color: red;'>Error: Usuario no está logueado. <a href='login.php'>Ir a login</a></p>";
    echo "<p>Información de sesión:</p>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    exit;
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? '';

echo "<h1>Debug de Pedidos - Usuario Logueado</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }</style>";

echo "<div class='debug'>";
echo "<h3>Información de Sesión Actual:</h3>";
echo "<p><strong>User ID:</strong> $user_id</p>";
echo "<p><strong>Username:</strong> $username</p>";
echo "<p><strong>Sesión completa:</strong></p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div>";

// Configuración MySQL (igual que en index.php)
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✓ Conexión a base de datos exitosa</p>";
} catch(PDOException $e) {
    echo "<p class='error'>✗ Error de conexión: " . $e->getMessage() . "</p>";
    exit;
}

// Verificar que el usuario existe en la base de datos
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user_data) {
        echo "<div class='debug'>";
        echo "<h3>Datos del Usuario en BD:</h3>";
        echo "<pre>" . print_r($user_data, true) . "</pre>";
        echo "</div>";
    } else {
        echo "<p class='error'>✗ Error: Usuario con ID $user_id no encontrado en la base de datos</p>";
    }
} catch(PDOException $e) {
    echo "<p class='error'>✗ Error al verificar usuario: " . $e->getMessage() . "</p>";
}

// Funciones necesarias (copiadas de index.php)
function getRealIP() {
    $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

function getLocationInfo($ip) {
    // Si es IP local, devolver valores por defecto
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        return ['country' => 'Local', 'city' => 'Local'];
    }
    
    return ['country' => 'Debug', 'city' => 'Debug']; // Simplificado para debug
}

// Procesar pedido si se envía el formulario (código exacto de index.php)
if (isset($_POST['pedido_tmdb_id'])) {
    echo "<h2>🔍 Procesando Pedido (Código Real de index.php)...</h2>";
    
    error_log("DEBUG - Pedido recibido - POST data: " . print_r($_POST, true));
    
    $tmdb_id = $_POST['pedido_tmdb_id'];
    $title = $_POST['pedido_title'];
    $media_type = $_POST['pedido_media_type'];
    $year = $_POST['pedido_year'];

    error_log("DEBUG - Datos del pedido - TMDB ID: $tmdb_id, Title: $title, Type: $media_type, Year: $year, User ID: $user_id");

    echo "<div class='debug'>";
    echo "<h3>Datos Recibidos del Formulario:</h3>";
    echo "<p><strong>TMDB ID:</strong> $tmdb_id</p>";
    echo "<p><strong>Título:</strong> $title</p>";
    echo "<p><strong>Tipo:</strong> $media_type</p>";
    echo "<p><strong>Año:</strong> $year</p>";
    echo "<p><strong>User ID:</strong> $user_id</p>";
    echo "</div>";

    // Obtener información de geolocalización
    $ip = getRealIP();
    $location = getLocationInfo($ip);

    error_log("DEBUG - Información de ubicación - IP: $ip, Country: " . $location['country'] . ", City: " . $location['city']);

    echo "<div class='debug'>";
    echo "<h3>Información de Ubicación:</h3>";
    echo "<p><strong>IP:</strong> $ip</p>";
    echo "<p><strong>País:</strong> " . $location['country'] . "</p>";
    echo "<p><strong>Ciudad:</strong> " . $location['city'] . "</p>";
    echo "</div>";

    try {
        // Insertar pedido en la base de datos
        $stmt = $pdo->prepare("INSERT INTO orders (user_id, tmdb_id, title, media_type, year, country, city, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([
            $user_id,
            $tmdb_id,
            $title,
            $media_type,
            $year,
            $location['country'],
            $location['city'],
            $ip
        ]);

        if ($result) {
            $order_id = $pdo->lastInsertId();
            error_log("DEBUG - Pedido insertado exitosamente con ID: $order_id");
            
            echo "<p class='success'>✓ Pedido insertado exitosamente con ID: $order_id</p>";
            
            // Verificar que se insertó
            $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
            $stmt->execute([$order_id]);
            $inserted_order = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<div class='debug'>";
            echo "<h3>Pedido Insertado en BD:</h3>";
            echo "<pre>" . print_r($inserted_order, true) . "</pre>";
            echo "</div>";
            
            $_SESSION['pedido_exitoso'] = [
                'tmdb_id' => $tmdb_id,
                'title' => $title,
                'media_type' => $media_type,
                'year' => $year
            ];

            echo "<p class='info'>✓ Sesión actualizada con pedido exitoso</p>";
            echo "<p class='info'>En index.php normal, aquí se haría: header('Location: index.php?pedido=ok');</p>";

        } else {
            error_log("DEBUG - Error: No se pudo insertar el pedido");
            $error_pedido = "Error al procesar el pedido. Inténtalo de nuevo.";
            echo "<p class='error'>✗ Error: No se pudo insertar el pedido</p>";
        }

    } catch(PDOException $e) {
        error_log("DEBUG - Error al insertar pedido: " . $e->getMessage());
        error_log("DEBUG - SQL State: " . $e->getCode());
        $error_pedido = "Error al procesar el pedido. Inténtalo de nuevo.";
        echo "<p class='error'>✗ Error al insertar pedido: " . $e->getMessage() . "</p>";
        echo "<p class='error'>Código SQL: " . $e->getCode() . "</p>";
    }
}

// Mostrar pedidos existentes del usuario
echo "<h2>📋 Pedidos Existentes del Usuario</h2>";
try {
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$user_id]);
    $user_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($user_orders) > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr><th>ID</th><th>Título</th><th>Tipo</th><th>Año</th><th>Estado</th><th>Fecha</th></tr>";
        foreach ($user_orders as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . htmlspecialchars($order['title']) . "</td>";
            echo "<td>" . $order['media_type'] . "</td>";
            echo "<td>" . $order['year'] . "</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='info'>No tienes pedidos registrados.</p>";
    }
} catch(PDOException $e) {
    echo "<p class='error'>Error al obtener pedidos: " . $e->getMessage() . "</p>";
}
?>

<h2>🧪 Formulario de Prueba (Simulando index.php)</h2>
<form method="POST" style="background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>Simular Pedido desde la Web Real</h3>
    
    <label for="pedido_tmdb_id">TMDB ID:</label><br>
    <input type="number" name="pedido_tmdb_id" id="pedido_tmdb_id" value="550" required><br><br>
    
    <label for="pedido_title">Título:</label><br>
    <input type="text" name="pedido_title" id="pedido_title" value="Fight Club (Debug Test)" required><br><br>
    
    <label for="pedido_media_type">Tipo de Media:</label><br>
    <select name="pedido_media_type" id="pedido_media_type" required>
        <option value="movie">Película</option>
        <option value="tv">Serie</option>
    </select><br><br>
    
    <label for="pedido_year">Año:</label><br>
    <input type="text" name="pedido_year" id="pedido_year" value="1999" required><br><br>
    
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        Enviar Pedido de Debug
    </button>
</form>

<hr>
<p><a href="index.php">← Volver al índice</a> | <a href="admin.php">Ver admin</a></p>

<script>
console.log('Debug script loaded');
console.log('Session user_id: <?php echo $user_id; ?>');
console.log('Session username: <?php echo addslashes($username); ?>');
</script>
