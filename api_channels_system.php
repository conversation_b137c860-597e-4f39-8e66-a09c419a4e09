<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

$action = $_GET['action'] ?? '';

// Función para limpiar input
function clean_input($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

try {
    switch ($action) {
        case 'submit_request':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Método no permitido');
            }
            
            $user_id = $_SESSION['user_id'] ?? 1; // Usuario por defecto para demo
            $channel_name = clean_input($_POST['channel_name'] ?? '');
            $channel_url = clean_input($_POST['channel_url'] ?? '');
            $country = clean_input($_POST['country'] ?? '');
            $language = clean_input($_POST['language'] ?? '');
            $category = clean_input($_POST['category'] ?? '');
            $description = clean_input($_POST['description'] ?? '');
            
            // Validar campos requeridos
            if (empty($channel_name) || empty($country) || empty($language) || empty($category)) {
                throw new Exception('Por favor completa todos los campos requeridos');
            }
            
            // Crear tabla si no existe
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS channel_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    channel_name VARCHAR(255) NOT NULL,
                    channel_url VARCHAR(500),
                    country VARCHAR(100) NOT NULL,
                    language VARCHAR(100) NOT NULL,
                    category VARCHAR(100) NOT NULL,
                    description TEXT,
                    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                    admin_notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_user (user_id),
                    INDEX idx_status (status),
                    INDEX idx_created (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            $stmt = $pdo->prepare("INSERT INTO channel_requests (user_id, channel_name, channel_url, country, language, category, description, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')");
            $stmt->execute([$user_id, $channel_name, $channel_url, $country, $language, $category, $description]);
            
            $request_id = $pdo->lastInsertId();
            
            // Crear notificación para admin
            try {
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS admin_notifications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        type VARCHAR(50) NOT NULL,
                        title VARCHAR(255) NOT NULL,
                        message TEXT NOT NULL,
                        reference_id INT,
                        reference_type VARCHAR(50),
                        is_read BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_type (type),
                        INDEX idx_read (is_read),
                        INDEX idx_created (created_at)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                
                $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([
                    'channel_request',
                    'Nueva solicitud de canal',
                    "Se ha solicitado el canal: {$channel_name} ({$country})",
                    $request_id,
                    'channel_requests'
                ]);
            } catch (Exception $e) {
                // Continuar sin notificación si hay error
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Solicitud de canal enviada correctamente. La revisaremos y te notificaremos el resultado.',
                'request_id' => $request_id
            ]);
            break;
            
        case 'get_user_requests':
            $user_id = $_SESSION['user_id'] ?? 1;
            
            $stmt = $pdo->prepare("
                SELECT * FROM channel_requests
                WHERE user_id = ?
                ORDER BY created_at DESC
            ");
            $stmt->execute([$user_id]);
            $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'requests' => $requests,
                'total' => count($requests)
            ]);
            break;
            
        case 'get_all_requests':
            // Solo para admins
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $status_filter = $_GET['status'] ?? 'all';
            $where_clause = '';
            $params = [];
            
            if ($status_filter !== 'all') {
                $where_clause = 'WHERE cr.status = ?';
                $params[] = $status_filter;
            }
            
            $stmt = $pdo->prepare("
                SELECT cr.*, u.username 
                FROM channel_requests cr 
                LEFT JOIN users u ON cr.user_id = u.id 
                $where_clause
                ORDER BY 
                    CASE cr.status 
                        WHEN 'pending' THEN 1 
                        WHEN 'approved' THEN 2 
                        WHEN 'rejected' THEN 3 
                    END,
                    cr.created_at DESC
            ");
            $stmt->execute($params);
            $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'requests' => $requests,
                'total' => count($requests)
            ]);
            break;
            
        case 'update_request_status':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Método no permitido');
            }
            
            // Solo para admins
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $request_id = (int)($_POST['request_id'] ?? 0);
            $status = $_POST['status'] ?? '';
            $admin_notes = clean_input($_POST['admin_notes'] ?? '');
            
            if (!$request_id || !in_array($status, ['approved', 'rejected'])) {
                throw new Exception('Datos inválidos');
            }
            
            $stmt = $pdo->prepare("UPDATE channel_requests SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $admin_notes, $request_id]);
            
            // Obtener información de la solicitud para notificación
            $stmt = $pdo->prepare("SELECT channel_name, user_id FROM channel_requests WHERE id = ?");
            $stmt->execute([$request_id]);
            $request_info = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($request_info) {
                // Crear notificación para el usuario
                try {
                    $status_text = $status === 'approved' ? 'aprobada' : 'rechazada';
                    $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([
                        'channel_response',
                        "Solicitud de canal {$status_text}",
                        "Tu solicitud del canal '{$request_info['channel_name']}' ha sido {$status_text}",
                        $request_id,
                        'channel_requests'
                    ]);
                } catch (Exception $e) {
                    // Continuar sin notificación si hay error
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => "Solicitud {$status} correctamente"
            ]);
            break;
            
        case 'get_stats':
            // Solo para admins
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_requests
                FROM channel_requests
            ");
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Estadísticas por categoría
            $stmt = $pdo->query("
                SELECT category, COUNT(*) as count
                FROM channel_requests 
                GROUP BY category
                ORDER BY count DESC
                LIMIT 5
            ");
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Estadísticas por país
            $stmt = $pdo->query("
                SELECT country, COUNT(*) as count
                FROM channel_requests 
                GROUP BY country
                ORDER BY count DESC
                LIMIT 5
            ");
            $countries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'stats' => $stats,
                'top_categories' => $categories,
                'top_countries' => $countries
            ]);
            break;
            
        case 'get_notifications':
            // Solo para admins
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $limit = (int)($_GET['limit'] ?? 10);
            $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
            
            $where_clause = $unread_only ? 'WHERE is_read = FALSE' : '';
            
            $stmt = $pdo->prepare("
                SELECT * FROM admin_notifications 
                $where_clause
                ORDER BY created_at DESC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'total' => count($notifications)
            ]);
            break;
            
        case 'mark_notification_read':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Método no permitido');
            }
            
            // Solo para admins
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $notification_id = (int)($_POST['notification_id'] ?? 0);
            
            if (!$notification_id) {
                throw new Exception('ID de notificación requerido');
            }
            
            $stmt = $pdo->prepare("UPDATE admin_notifications SET is_read = TRUE WHERE id = ?");
            $stmt->execute([$notification_id]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Notificación marcada como leída'
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
