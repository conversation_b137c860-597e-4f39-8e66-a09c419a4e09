<?php
// Archivo para probar y verificar los datos en las tablas
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🔍 Verificación de Datos del Sistema</h1>";

try {
    // 1. Verificar usuarios
    echo "<h2>👥 Usuarios</h2>";
    $stmt = $pdo->query("SELECT * FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Total usuarios: " . count($users) . "</p>";
    foreach ($users as $user) {
        echo "• ID: {$user['id']}, Usuario: {$user['username']}, Email: {$user['email']}<br>";
    }
    
    // 2. Verificar aplicaciones
    echo "<h2>📱 Aplicaciones</h2>";
    $stmt = $pdo->query("SELECT * FROM support_apps");
    $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Total aplicaciones: " . count($apps) . "</p>";
    foreach ($apps as $app) {
        echo "• ID: {$app['id']}, Nombre: {$app['name']}, Plataforma: {$app['platform']}, Estado: {$app['status']}<br>";
    }
    
    // 3. Verificar artículos de ayuda
    echo "<h2>❓ Artículos de Ayuda</h2>";
    $stmt = $pdo->query("SELECT * FROM help_articles");
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Total artículos: " . count($articles) . "</p>";
    foreach ($articles as $article) {
        echo "• ID: {$article['id']}, Título: {$article['title']}, Categoría: {$article['category']}, Estado: {$article['status']}<br>";
    }
    
    // 4. Verificar tickets
    echo "<h2>🎫 Tickets de Soporte</h2>";
    $stmt = $pdo->query("SELECT * FROM support_tickets");
    $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Total tickets: " . count($tickets) . "</p>";
    foreach ($tickets as $ticket) {
        echo "• ID: {$ticket['id']}, Usuario: {$ticket['user_id']}, Asunto: {$ticket['subject']}, Estado: {$ticket['status']}<br>";
    }
    
    // 5. Verificar sesiones de chat
    echo "<h2>💬 Sesiones de Chat</h2>";
    $stmt = $pdo->query("SELECT * FROM chat_sessions");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Total sesiones: " . count($sessions) . "</p>";
    foreach ($sessions as $session) {
        echo "• ID: {$session['id']}, Usuario: {$session['user_id']}, Estado: {$session['status']}, Iniciado: {$session['started_at']}<br>";
    }
    
    // 6. Verificar solicitudes de canales
    echo "<h2>📺 Solicitudes de Canales</h2>";
    $stmt = $pdo->query("SELECT * FROM channel_requests");
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Total solicitudes: " . count($requests) . "</p>";
    foreach ($requests as $request) {
        echo "• ID: {$request['id']}, Usuario: {$request['user_id']}, Canal: {$request['channel_name']}, Estado: {$request['status']}<br>";
    }
    
    // 7. Verificar códigos de activación
    echo "<h2>🔑 Códigos de Activación</h2>";
    $stmt = $pdo->query("SELECT * FROM activation_codes");
    $codes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Total códigos: " . count($codes) . "</p>";
    foreach ($codes as $code) {
        echo "• ID: {$code['id']}, Código: {$code['code']}, Lista: {$code['list_name']}, Estado: {$code['status']}<br>";
    }
    
    echo "<hr>";
    echo "<h2>🧪 Pruebas de Inserción</h2>";
    
    // Probar inserción de ticket
    echo "<h3>Probando inserción de ticket...</h3>";
    try {
        $stmt = $pdo->prepare("INSERT INTO support_tickets (user_id, subject, description, priority, category, status) VALUES (?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([1, 'Ticket de prueba', 'Descripción de prueba', 'medium', 'technical', 'open']);
        if ($result) {
            echo "✅ Ticket insertado correctamente<br>";
            $ticket_id = $pdo->lastInsertId();
            echo "ID del ticket: $ticket_id<br>";
        } else {
            echo "❌ Error al insertar ticket<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error al insertar ticket: " . $e->getMessage() . "<br>";
    }
    
    // Probar inserción de sesión de chat
    echo "<h3>Probando inserción de sesión de chat...</h3>";
    try {
        $stmt = $pdo->prepare("INSERT INTO chat_sessions (user_id, status) VALUES (?, ?)");
        $result = $stmt->execute([1, 'waiting']);
        if ($result) {
            echo "✅ Sesión de chat insertada correctamente<br>";
            $session_id = $pdo->lastInsertId();
            echo "ID de la sesión: $session_id<br>";
            
            // Insertar mensaje de prueba
            $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, ?)");
            $result = $stmt->execute([$session_id, 1, 'Mensaje de prueba', 0]);
            if ($result) {
                echo "✅ Mensaje de chat insertado correctamente<br>";
            }
        } else {
            echo "❌ Error al insertar sesión de chat<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error al insertar sesión de chat: " . $e->getMessage() . "<br>";
    }
    
    // Probar inserción de solicitud de canal
    echo "<h3>Probando inserción de solicitud de canal...</h3>";
    try {
        $stmt = $pdo->prepare("INSERT INTO channel_requests (user_id, channel_name, country, language, category, description, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([1, 'Canal de Prueba', 'ES', 'spanish', 'entertainment', 'Canal de prueba para verificar funcionamiento', 'pending']);
        if ($result) {
            echo "✅ Solicitud de canal insertada correctamente<br>";
            $request_id = $pdo->lastInsertId();
            echo "ID de la solicitud: $request_id<br>";
        } else {
            echo "❌ Error al insertar solicitud de canal<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error al insertar solicitud de canal: " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
    echo "<h2>🔗 Enlaces de Prueba</h2>";
    echo "<p><a href='user_tickets.php'>🎫 Probar Tickets</a></p>";
    echo "<p><a href='user_chat.php'>💬 Probar Chat</a></p>";
    echo "<p><a href='user_apps.php'>📱 Probar Apps</a></p>";
    echo "<p><a href='user_help.php'>❓ Probar Ayuda</a></p>";
    echo "<p><a href='user_channels.php'>📺 Probar Canales</a></p>";
    echo "<p><a href='user_activation.php'>🔑 Probar Activación</a></p>";
    echo "<p><a href='admin2.php'>⚙️ Panel Admin</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h2>❌ Error de Conexión</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
