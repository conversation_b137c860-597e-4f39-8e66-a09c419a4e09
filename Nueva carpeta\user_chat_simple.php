<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'usuario';

$error_message = '';
$success_message = '';
$current_session = null;
$messages = [];

// Procesar inicio de chat
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_chat'])) {
    try {
        $initial_message = clean_input($_POST['initial_message'] ?? 'Hola, necesito ayuda');
        
        // Crear nueva sesión de chat
        $stmt = $pdo->prepare("INSERT INTO chat_sessions (user_id, status) VALUES (?, 'waiting')");
        $stmt->execute([$user_id]);
        $session_id = $pdo->lastInsertId();
        
        // Mensaje inicial del usuario
        $stmt = $pdo->prepare("INSERT INTO chat_messages (session_id, sender_id, message, is_admin) VALUES (?, ?, ?, 0)");
        $stmt->execute([$session_id, $user_id, $initial_message]);
        
        $success_message = "Chat iniciado correctamente. Un agente se conectará pronto.";
        
    } catch (Exception $e) {
        $error_message = "Error al iniciar chat: " . $e->getMessage();
    }
}

// Obtener sesión activa del usuario
try {
    $stmt = $pdo->prepare("SELECT * FROM chat_sessions WHERE user_id = ? AND status IN ('waiting', 'active') ORDER BY started_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    $current_session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($current_session) {
        // Obtener mensajes de la sesión
        $stmt = $pdo->prepare("
            SELECT cm.*, u.username 
            FROM chat_messages cm 
            LEFT JOIN users u ON cm.sender_id = u.id 
            WHERE cm.session_id = ? 
            ORDER BY cm.sent_at ASC
        ");
        $stmt->execute([$current_session['id']]);
        $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    $error_message = "Error al cargar chat: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 Chat en Vivo - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .chat-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            max-width: 800px;
            margin: 0 auto;
            overflow: hidden;
        }

        .chat-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .chat-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .chat-status {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .start-chat-form {
            padding: 2rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
            text-align: left;
        }

        .form-textarea {
            width: 100%;
            padding: 1rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 1rem;
            resize: vertical;
            min-height: 100px;
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.15);
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .message {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .chat-messages {
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
            background: var(--dark-bg);
        }

        .chat-message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            max-width: 80%;
        }

        .chat-message.user {
            background: rgba(37, 99, 235, 0.2);
            margin-left: auto;
            text-align: right;
        }

        .chat-message.admin {
            background: rgba(16, 185, 129, 0.2);
            margin-right: auto;
        }

        .message-sender {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .message-text {
            color: var(--text-primary);
        }

        .message-time {
            font-size: 0.7rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .info-box {
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid rgba(37, 99, 235, 0.3);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        .info-box h3 {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-box ul {
            list-style: none;
            padding: 0;
        }

        .info-box li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .info-box li::before {
            content: '•';
            color: var(--primary-color);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_chat_simple.php" class="logo">
                <i class="fas fa-comments"></i>
                <span>Chat en Vivo</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-comments" style="color: var(--accent-color);"></i>
                Chat en Vivo
            </h1>
            <p class="page-subtitle">
                Obtén ayuda inmediata de nuestros agentes especializados en IPTV
            </p>
        </div>

        <?php if (isset($success_message) && $success_message): ?>
        <div class="success-message message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message) && $error_message): ?>
        <div class="error-message message">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <div class="info-box">
            <h3>
                <i class="fas fa-info-circle"></i>
                Información sobre el Chat en Vivo
            </h3>
            <ul>
                <li>Nuestros agentes están disponibles 24/7</li>
                <li>Tiempo de respuesta promedio: 2-5 minutos</li>
                <li>Soporte especializado en IPTV y streaming</li>
                <li>Resolución inmediata de problemas técnicos</li>
                <li>Asistencia en configuración de dispositivos</li>
            </ul>
        </div>

        <div class="chat-container">
            <?php if (!$current_session): ?>
            <!-- Formulario para iniciar chat -->
            <div class="chat-header">
                <h2 class="chat-title">
                    <i class="fas fa-play-circle"></i>
                    Iniciar Nueva Conversación
                </h2>
                <div class="chat-status">
                    Describe tu problema para conectarte con un agente
                </div>
            </div>
            
            <form method="POST" class="start-chat-form">
                <div class="form-group">
                    <label class="form-label">¿En qué podemos ayudarte?</label>
                    <textarea 
                        name="initial_message" 
                        class="form-textarea" 
                        placeholder="Describe tu problema o pregunta..."
                        required
                    ></textarea>
                </div>
                
                <button type="submit" name="start_chat" class="btn btn-primary">
                    <i class="fas fa-comments"></i>
                    Iniciar Chat
                </button>
            </form>
            
            <?php else: ?>
            <!-- Chat activo -->
            <div class="chat-header">
                <h2 class="chat-title">
                    <i class="fas fa-comment-dots"></i>
                    Chat en Curso
                </h2>
                <div class="chat-status">
                    Estado: <?php 
                    $status_labels = [
                        'waiting' => 'Esperando agente...',
                        'active' => 'Conectado con agente',
                        'ended' => 'Conversación finalizada'
                    ];
                    echo $status_labels[$current_session['status']] ?? $current_session['status'];
                    ?>
                    • Iniciado: <?php echo date('d/m/Y H:i', strtotime($current_session['started_at'])); ?>
                </div>
            </div>
            
            <div class="chat-messages">
                <?php if (empty($messages)): ?>
                <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                    <i class="fas fa-clock"></i><br>
                    Esperando respuesta del agente...
                </div>
                <?php else: ?>
                    <?php foreach ($messages as $msg): ?>
                    <div class="chat-message <?php echo $msg['is_admin'] ? 'admin' : 'user'; ?>">
                        <div class="message-sender">
                            <?php echo $msg['is_admin'] ? 'Agente de Soporte' : ($msg['username'] ?? 'Tú'); ?>
                        </div>
                        <div class="message-text">
                            <?php echo nl2br(htmlspecialchars($msg['message'])); ?>
                        </div>
                        <div class="message-time">
                            <?php echo date('H:i', strtotime($msg['sent_at'])); ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        // Auto-refresh cada 10 segundos si hay una sesión activa
        <?php if ($current_session): ?>
        setTimeout(() => {
            location.reload();
        }, 10000);
        <?php endif; ?>

        // Auto-focus en el textarea
        document.querySelector('textarea')?.focus();
    </script>
</body>
</html>
