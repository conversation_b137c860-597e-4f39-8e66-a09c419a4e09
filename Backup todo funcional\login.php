<?php
session_start();

// Si ya está logueado, redirigir al index
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Crear tabla de usuarios si no existe
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(64) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        is_admin TINYINT(1) DEFAULT 0,
        is_cliente_actual TINYINT(1) DEFAULT 0,
        is_mavistv TINYINT(1) DEFAULT 0,
        is_tvdigital TINYINT(1) DEFAULT 0,
        is_limites507 TINYINT(1) DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    
    // Asegurar que los usuarios admin tengan el flag is_admin
    $pdo->exec("UPDATE users SET is_admin = 1 WHERE username IN ('admin', 'Infest84')");
    
} catch(PDOException $e) {
    error_log('Error de base de datos: ' . $e->getMessage());
    die('Error de conexión a la base de datos.');
}

$login_error = '';
$success_message = '';

// Verificar si viene de logout
if (isset($_GET['logout']) && $_GET['logout'] === 'success') {
    $success_message = 'Sesión cerrada exitosamente.';
}

// Cargar credenciales guardadas si existen
$remembered_user = '';
$remembered_pass = '';
if (isset($_COOKIE['remember_user']) && isset($_COOKIE['remember_pass'])) {
    $remembered_user = base64_decode($_COOKIE['remember_user']);
    $remembered_pass = base64_decode($_COOKIE['remember_pass']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['reset_password'])) {
        // RESETEAR CONTRASEÑA
        $username = trim($_POST['reset_user']);
        $master_password = $_POST['master_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_new_password'];

        if (empty($username) || empty($master_password) || empty($new_password)) {
            $login_error = 'Por favor completa todos los campos.';
        } elseif ($master_password !== '96321') {
            $login_error = 'Contraseña maestra incorrecta. Solo el administrador puede resetear contraseñas.';
        } elseif (strlen($new_password) < 6) {
            $login_error = 'La nueva contraseña debe tener al menos 6 caracteres.';
        } elseif ($new_password !== $confirm_password) {
            $login_error = 'Las contraseñas no coinciden.';
        } else {
            // Verificar si el usuario existe
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);

            if ($stmt->fetch()) {
                // Actualizar contraseña
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = ?");
                $success = $stmt->execute([$hashed_password, $username]);

                if ($success) {
                    $success_message = 'Contraseña actualizada exitosamente. Ya puedes iniciar sesión.';
                    // Limpiar cookies si existen
                    setcookie('remember_user', '', time() - 3600, "/");
                    setcookie('remember_pass', '', time() - 3600, "/");
                } else {
                    $login_error = 'Error al actualizar la contraseña. Inténtalo de nuevo.';
                }
            } else {
                $login_error = 'El usuario no existe.';
            }
        }
    } elseif (isset($_POST['login_user'], $_POST['login_pass'])) {
        // LOGIN
        $username = trim($_POST['login_user']);
        $password = $_POST['login_pass'];
        
        if (empty($username) || empty($password)) {
            $login_error = 'Por favor completa todos los campos.';
        } else {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                
                // Verificar si es admin
                if ($user['is_admin'] == 1) {
                    $_SESSION['admin_logged_in'] = true;
                }

                // Guardar cookies si "recordarme" está marcado
                if (!empty($_POST['remember_me'])) {
                    setcookie('remember_user', base64_encode($username), time() + (86400 * 30), "/");
                    setcookie('remember_pass', base64_encode($password), time() + (86400 * 30), "/");
                }
                
                if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
                    header('Location: admin.php');
                } else {
                    header('Location: index.php');
                }
                exit;
            } else {
                $login_error = 'Usuario o contraseña incorrectos.';
            }
        }
    } elseif (isset($_POST['reg_user'], $_POST['reg_pass'])) {
        // REGISTRO NUEVO USUARIO
        $username = trim($_POST['reg_user']);
        $password = $_POST['reg_pass'];
        $confirm_password = $_POST['reg_pass_confirm'] ?? '';
        
        if (empty($username) || empty($password)) {
            $login_error = 'Por favor completa todos los campos.';
        } elseif (strlen($username) < 3) {
            $login_error = 'El usuario debe tener al menos 3 caracteres.';
        } elseif (strlen($password) < 6) {
            $login_error = 'La contraseña debe tener al menos 6 caracteres.';
        } elseif ($password !== $confirm_password) {
            $login_error = 'Las contraseñas no coinciden.';
        } else {
            // Verificar si el usuario ya existe
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            
            if ($stmt->fetch()) {
                $login_error = 'El usuario ya existe. Elige otro nombre.';
            } else {
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $is_cliente = !empty($_POST['is_cliente_actual']) ? 1 : 0;
                $is_mavistv = !empty($_POST['is_mavistv']) ? 1 : 0;
                $is_tvdigital = !empty($_POST['is_tvdigital']) ? 1 : 0;
                $is_limites507 = !empty($_POST['is_limites507']) ? 1 : 0;
                $is_worldtv = !empty($_POST['is_worldtv']) ? 1 : 0;

                $stmt = $pdo->prepare("INSERT INTO users (username, password, is_cliente_actual, is_mavistv, is_tvdigital, is_limites507, is_worldtv) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $success = $stmt->execute([
                    $username,
                    $hashed_password,
                    $is_cliente,
                    $is_mavistv,
                    $is_tvdigital,
                    $is_limites507,
                    $is_worldtv
                ]);
                
                if ($success) {
                    $_SESSION['user_id'] = $pdo->lastInsertId();
                    $_SESSION['username'] = $username;
                    header('Location: index.php');
                    exit;
                } else {
                    $login_error = 'Error al registrar usuario. Inténtalo de nuevo.';
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ RGS TOOL - Acceso al Soporte Técnico</title>
<parameter name="description" content="Accede a la plataforma de soporte técnico profesional para servicios IPTV">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛠️</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
        }

        .login-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius-lg);
            padding: 2.5rem;
            width: 100%;
            max-width: 420px;
            box-shadow: var(--shadow-heavy);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .logo p {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        .form-tabs {
            display: flex;
            margin-bottom: 2rem;
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            padding: 0.25rem;
            gap: 0.25rem;
        }

        .tab-btn {
            flex: 1;
            padding: 0.6rem 0.8rem;
            background: transparent;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: calc(var(--border-radius) - 2px);
            transition: var(--transition);
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.4rem;
        }

        .tab-btn.active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .form-content {
            position: relative;
        }

        .form-section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .form-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            background: var(--dark-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .form-input::placeholder {
            color: var(--text-secondary);
        }

        .checkbox-group {
            margin-bottom: 1.5rem;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            background: var(--dark-bg);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .checkbox-item:hover {
            background: rgba(255,255,255,0.05);
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 0.75rem;
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .checkbox-item label {
            margin: 0;
            font-size: 0.85rem;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .submit-btn {
            width: 100%;
            padding: 0.875rem 1rem;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-error {
            background: rgba(220, 53, 69, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .form-footer {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .form-footer p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .logo h1 {
                font-size: 1.75rem;
            }

            .tab-btn {
                padding: 0.6rem 0.75rem;
                font-size: 0.9rem;
            }
        }

        /* Animaciones adicionales */
        .login-container {
            animation: slideUp 0.5s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>
                <i class="fas fa-headset"></i>
                RGS TOOL
            </h1>
            <p>Plataforma de Soporte Técnico IPTV</p>
        </div>

        <?php if (!empty($login_error)): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($login_error); ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <div class="form-tabs">
            <button class="tab-btn active" onclick="switchTab('login')">
                <i class="fas fa-sign-in-alt"></i>
                Iniciar Sesión
            </button>
            <button class="tab-btn" onclick="switchTab('register')">
                <i class="fas fa-user-plus"></i>
                Registrarse
            </button>
            <button class="tab-btn" onclick="switchTab('reset')">
                <i class="fas fa-key"></i>
                Olvidé mi Contraseña
            </button>
        </div>

        <div class="form-content">
            <!-- Formulario de Login -->
            <div id="loginForm" class="form-section active">
                <form method="POST" onsubmit="return handleSubmit(this)">
                    <div class="form-group">
                        <label for="login_user">
                            <i class="fas fa-user"></i>
                            Usuario
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="text"
                                id="login_user"
                                name="login_user"
                                class="form-input"
                                placeholder="Ingresa tu usuario"
                                value="<?php echo htmlspecialchars($remembered_user); ?>"
                                required
                                autocomplete="username"
                            >
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="login_pass">
                            <i class="fas fa-lock"></i>
                            Contraseña
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="password"
                                id="login_pass"
                                name="login_pass"
                                class="form-input"
                                placeholder="Ingresa tu contraseña"
                                value="<?php echo htmlspecialchars($remembered_pass); ?>"
                                required
                                autocomplete="current-password"
                            >
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="checkbox" name="remember_me" id="remember_me" class="checkbox-input" <?php echo !empty($remembered_user) ? 'checked' : ''; ?>>
                        <label for="remember_me" class="checkbox-label">
                            <i class="fas fa-check"></i>
                            Recordarme
                        </label>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        Iniciar Sesión
                    </button>
                </form>
            </div>

            <!-- Formulario de Registro -->
            <div id="registerForm" class="form-section">
                <form method="POST" onsubmit="return handleSubmit(this)">
                    <div class="form-group">
                        <label for="reg_user">
                            <i class="fas fa-user"></i>
                            Usuario
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="text"
                                id="reg_user"
                                name="reg_user"
                                class="form-input"
                                placeholder="Elige un nombre de usuario"
                                required
                                minlength="3"
                                autocomplete="username"
                            >
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="reg_pass">
                            <i class="fas fa-lock"></i>
                            Contraseña
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="password"
                                id="reg_pass"
                                name="reg_pass"
                                class="form-input"
                                placeholder="Crea una contraseña segura"
                                required
                                minlength="6"
                                autocomplete="new-password"
                            >
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="reg_pass_confirm">
                            <i class="fas fa-lock"></i>
                            Confirmar Contraseña
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="password"
                                id="reg_pass_confirm"
                                name="reg_pass_confirm"
                                class="form-input"
                                placeholder="Confirma tu contraseña"
                                required
                                minlength="6"
                                autocomplete="new-password"
                            >
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" name="is_cliente_actual" value="1" id="is_cliente_actual">
                            <label for="is_cliente_actual">
                                <i class="fas fa-tv"></i>
                                Soy cliente de rogsmediatv
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="is_mavistv" value="1" id="is_mavistv">
                            <label for="is_mavistv">
                                <i class="fas fa-satellite-dish"></i>
                                Soy cliente de MavisTV
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="is_tvdigital" value="1" id="is_tvdigital">
                            <label for="is_tvdigital">
                                <i class="fas fa-broadcast-tower"></i>
                                Soy cliente de TVDigital
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="is_limites507" value="1" id="is_limites507">
                            <label for="is_limites507">
                                <i class="fas fa-globe"></i>
                                Soy cliente de Limites507
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="is_worldtv" value="1" id="is_worldtv">
                            <label for="is_worldtv">
                                <i class="fas fa-globe-americas"></i>
                                Soy cliente de WorldTV
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-user-plus"></i>
                        Crear Cuenta
                    </button>
                </form>
            </div>

            <!-- Formulario de Resetear Contraseña -->
            <div id="resetForm" class="form-section">
                <form method="POST" onsubmit="return handleSubmit(this)">
                    <div class="form-group">
                        <label for="reset_user">
                            <i class="fas fa-user"></i>
                            Usuario a Resetear
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="text"
                                id="reset_user"
                                name="reset_user"
                                class="form-input"
                                placeholder="Ingresa el usuario a resetear"
                                required
                                autocomplete="username"
                            >
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="master_password">
                            <i class="fas fa-shield-alt"></i>
                            Contraseña Maestra (Solo Admin)
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="password"
                                id="master_password"
                                name="master_password"
                                class="form-input"
                                placeholder="Contraseña maestra del administrador"
                                required
                            >
                        </div>
                        <small style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                            <i class="fas fa-info-circle"></i>
                            Solo el administrador tiene acceso a esta función
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="new_password">
                            <i class="fas fa-lock"></i>
                            Nueva Contraseña
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="password"
                                id="new_password"
                                name="new_password"
                                class="form-input"
                                placeholder="Nueva contraseña (mínimo 6 caracteres)"
                                required
                                minlength="6"
                                autocomplete="new-password"
                            >
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_new_password">
                            <i class="fas fa-lock"></i>
                            Confirmar Nueva Contraseña
                        </label>
                        <div class="input-wrapper">
                            <input
                                type="password"
                                id="confirm_new_password"
                                name="confirm_new_password"
                                class="form-input"
                                placeholder="Confirma la nueva contraseña"
                                required
                                minlength="6"
                                autocomplete="new-password"
                            >
                        </div>
                    </div>

                    <button type="submit" name="reset_password" class="submit-btn">
                        <i class="fas fa-key"></i>
                        Resetear Contraseña
                    </button>
                </form>
            </div>
        </div>

        <div class="form-footer">
            <p>
                <i class="fas fa-shield-alt"></i>
                Tus datos están protegidos y seguros
            </p>
        </div>
    </div>

    <script>
        function switchTab(tab) {
            // Actualizar botones
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Ocultar formularios
            document.querySelectorAll('.form-section').forEach(section => {
                section.classList.remove('active');
            });

            if (tab === 'login') {
                document.querySelector('.tab-btn:nth-child(1)').classList.add('active');
                document.getElementById('loginForm').classList.add('active');
            } else if (tab === 'register') {
                document.querySelector('.tab-btn:nth-child(2)').classList.add('active');
                document.getElementById('registerForm').classList.add('active');
            } else if (tab === 'reset') {
                document.querySelector('.tab-btn:nth-child(3)').classList.add('active');
                document.getElementById('resetForm').classList.add('active');
            }
        }

        function handleSubmit(form) {
            const submitBtn = form.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;

            // Validaciones adicionales para registro
            if (form.querySelector('#reg_pass')) {
                const password = form.querySelector('#reg_pass').value;
                const confirmPassword = form.querySelector('#reg_pass_confirm').value;

                if (password !== confirmPassword) {
                    alert('Las contraseñas no coinciden');
                    return false;
                }

                if (password.length < 6) {
                    alert('La contraseña debe tener al menos 6 caracteres');
                    return false;
                }
            }

            // Validaciones para resetear contraseña
            if (form.querySelector('#new_password')) {
                const newPassword = form.querySelector('#new_password').value;
                const confirmNewPassword = form.querySelector('#confirm_new_password').value;

                if (newPassword !== confirmNewPassword) {
                    alert('Las contraseñas no coinciden');
                    return false;
                }

                if (newPassword.length < 6) {
                    alert('La nueva contraseña debe tener al menos 6 caracteres');
                    return false;
                }
            }

            // Mostrar loading
            submitBtn.innerHTML = '<div class="loading"><div class="spinner"></div>Procesando...</div>';
            submitBtn.disabled = true;

            return true;
        }

        // Validación en tiempo real para confirmación de contraseña
        document.addEventListener('DOMContentLoaded', function() {
            // Validación para registro
            const regPass = document.getElementById('reg_pass');
            const regPassConfirm = document.getElementById('reg_pass_confirm');

            if (regPass && regPassConfirm) {
                function validatePasswords() {
                    if (regPassConfirm.value && regPass.value !== regPassConfirm.value) {
                        regPassConfirm.style.borderColor = 'var(--error-color)';
                    } else {
                        regPassConfirm.style.borderColor = 'var(--border-color)';
                    }
                }

                regPass.addEventListener('input', validatePasswords);
                regPassConfirm.addEventListener('input', validatePasswords);
            }

            // Validación para resetear contraseña
            const newPass = document.getElementById('new_password');
            const confirmNewPass = document.getElementById('confirm_new_password');

            if (newPass && confirmNewPass) {
                function validateNewPasswords() {
                    if (confirmNewPass.value && newPass.value !== confirmNewPass.value) {
                        confirmNewPass.style.borderColor = 'var(--error-color)';
                    } else {
                        confirmNewPass.style.borderColor = 'var(--border-color)';
                    }
                }

                newPass.addEventListener('input', validateNewPasswords);
                confirmNewPass.addEventListener('input', validateNewPasswords);
            }
        });

        // Auto-focus en el primer campo
        document.addEventListener('DOMContentLoaded', function() {
            const firstInput = document.querySelector('.form-section.active .form-input');
            if (firstInput) {
                firstInput.focus();
            }
        });

        // Limpiar mensajes de error al cambiar de tab
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    alert.style.display = 'none';
                });
            });
        });
    </script>
</body>
</html>
