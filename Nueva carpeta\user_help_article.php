<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

$article_id = (int)($_GET['id'] ?? 0);

if (!$article_id) {
    header('Location: user_help.php');
    exit;
}

// Obtener artículo
$stmt = $pdo->prepare("SELECT * FROM help_articles WHERE id = ? AND status = 'published'");
$stmt->execute([$article_id]);
$article = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$article) {
    header('Location: user_help.php');
    exit;
}

// Incrementar contador de vistas
$stmt = $pdo->prepare("UPDATE help_articles SET views = views + 1 WHERE id = ?");
$stmt->execute([$article_id]);

// Obtener artículos relacionados
$stmt = $pdo->prepare("
    SELECT id, title, views FROM help_articles 
    WHERE category = ? AND id != ? AND status = 'published' 
    ORDER BY views DESC 
    LIMIT 5
");
$stmt->execute([$article['category'], $article_id]);
$related_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['title']); ?> - Centro de Ayuda RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--warning-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 3rem;
        }

        .article-content {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .article-header {
            padding: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .article-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .article-meta {
            display: flex;
            gap: 1.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .category-badge {
            background: rgba(37, 99, 235, 0.2);
            color: var(--primary-color);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .featured-badge {
            background: var(--warning-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .article-body {
            padding: 2rem;
        }

        .article-text {
            color: var(--text-primary);
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .article-text h1, .article-text h2, .article-text h3 {
            color: var(--text-primary);
            margin: 2rem 0 1rem 0;
        }

        .article-text h1 {
            font-size: 1.8rem;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        .article-text h2 {
            font-size: 1.5rem;
        }

        .article-text h3 {
            font-size: 1.3rem;
        }

        .article-text p {
            margin-bottom: 1.5rem;
        }

        .article-text ul, .article-text ol {
            margin: 1rem 0 1.5rem 2rem;
        }

        .article-text li {
            margin-bottom: 0.5rem;
        }

        .article-text code {
            background: var(--dark-bg);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: var(--accent-color);
        }

        .article-text pre {
            background: var(--dark-bg);
            padding: 1rem;
            border-radius: var(--border-radius);
            overflow-x: auto;
            margin: 1rem 0;
            border: 1px solid var(--border-color);
        }

        .article-text blockquote {
            border-left: 4px solid var(--primary-color);
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: var(--text-secondary);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .sidebar-section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .sidebar-header {
            background: var(--dark-bg);
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-content {
            padding: 1rem;
        }

        .related-article {
            display: block;
            padding: 0.75rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            transition: var(--transition);
        }

        .related-article:hover {
            background: var(--primary-color);
            transform: translateX(5px);
        }

        .related-article:last-child {
            margin-bottom: 0;
        }

        .related-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .related-views {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--text-primary);
            transition: var(--transition);
            font-size: 0.9rem;
        }

        .action-btn:hover {
            background: var(--accent-color);
        }

        .action-btn i {
            color: var(--accent-color);
        }

        .action-btn:hover i {
            color: white;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .article-header {
                padding: 1.5rem;
            }

            .article-body {
                padding: 1.5rem;
            }

            .article-title {
                font-size: 1.5rem;
            }

            .article-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_help_article.php?id=<?php echo $article['id']; ?>" class="logo">
                <i class="fas fa-question-circle"></i>
                <span>Centro de Ayuda</span>
            </a>
            
            <div class="nav-buttons">
                <a href="user_help.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Ayuda</span>
                </a>
                <a href="index2.php" class="nav-btn">
                    <i class="fas fa-headset"></i>
                    <span>Servicios</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <!-- Contenido del Artículo -->
        <div class="article-content">
            <div class="article-header">
                <h1 class="article-title">
                    <?php echo htmlspecialchars($article['title']); ?>
                </h1>
                
                <div class="article-meta">
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>Publicado: <?php echo date('d/m/Y', strtotime($article['created_at'])); ?></span>
                    </div>
                    
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <span><?php echo number_format($article['views']); ?> vistas</span>
                    </div>
                    
                    <span class="category-badge">
                        <?php 
                        $category_names = [
                            'setup' => 'Configuración',
                            'troubleshooting' => 'Solución de Problemas',
                            'apps' => 'Aplicaciones',
                            'channels' => 'Canales',
                            'billing' => 'Facturación',
                            'account' => 'Cuenta de Usuario',
                            'general' => 'General'
                        ];
                        echo $category_names[$article['category']] ?? ucfirst($article['category']);
                        ?>
                    </span>
                    
                    <?php if ($article['is_featured']): ?>
                    <span class="featured-badge">Destacado</span>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="article-body">
                <div class="article-text">
                    <?php echo nl2br($article['content']); ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Acciones Rápidas -->
            <div class="sidebar-section">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">
                        <i class="fas fa-bolt"></i>
                        ¿Necesitas más ayuda?
                    </h3>
                </div>
                <div class="sidebar-content">
                    <div class="quick-actions">
                        <a href="user_tickets.php" class="action-btn">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Crear Ticket de Soporte</span>
                        </a>
                        <a href="user_chat.php" class="action-btn">
                            <i class="fas fa-comments"></i>
                            <span>Chat en Vivo</span>
                        </a>
                        <a href="user_help.php" class="action-btn">
                            <i class="fas fa-search"></i>
                            <span>Buscar más artículos</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Artículos Relacionados -->
            <?php if (!empty($related_articles)): ?>
            <div class="sidebar-section">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">
                        <i class="fas fa-link"></i>
                        Artículos Relacionados
                    </h3>
                </div>
                <div class="sidebar-content">
                    <?php foreach ($related_articles as $related): ?>
                    <a href="user_help_article.php?id=<?php echo $related['id']; ?>" class="related-article">
                        <div class="related-title"><?php echo htmlspecialchars($related['title']); ?></div>
                        <div class="related-views">
                            <i class="fas fa-eye"></i> <?php echo number_format($related['views']); ?> vistas
                        </div>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Información Adicional -->
            <div class="sidebar-section">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">
                        <i class="fas fa-info-circle"></i>
                        ¿Te fue útil este artículo?
                    </h3>
                </div>
                <div class="sidebar-content">
                    <p style="color: var(--text-secondary); margin-bottom: 1rem; font-size: 0.9rem;">
                        Si este artículo no resolvió tu problema, no dudes en contactar nuestro soporte técnico.
                    </p>
                    <div class="quick-actions">
                        <a href="user_tickets.php" class="action-btn">
                            <i class="fas fa-thumbs-up"></i>
                            <span>Sí, me ayudó</span>
                        </a>
                        <a href="user_chat.php" class="action-btn">
                            <i class="fas fa-thumbs-down"></i>
                            <span>No, necesito más ayuda</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Smooth scroll para enlaces internos
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Resaltar código
        document.querySelectorAll('pre code').forEach(block => {
            block.style.background = 'var(--dark-bg)';
            block.style.color = 'var(--accent-color)';
        });

        // Animación de entrada para el contenido
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.article-content, .sidebar-section').forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(element);
        });

        // Copiar código al hacer clic
        document.querySelectorAll('pre').forEach(pre => {
            pre.style.position = 'relative';
            pre.style.cursor = 'pointer';
            pre.title = 'Clic para copiar';
            
            pre.addEventListener('click', function() {
                const code = this.textContent;
                navigator.clipboard.writeText(code).then(() => {
                    // Mostrar feedback visual
                    const feedback = document.createElement('div');
                    feedback.textContent = '¡Copiado!';
                    feedback.style.cssText = `
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        background: var(--success-color);
                        color: white;
                        padding: 0.5rem;
                        border-radius: 4px;
                        font-size: 0.8rem;
                        z-index: 10;
                    `;
                    this.appendChild(feedback);
                    
                    setTimeout(() => {
                        feedback.remove();
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
