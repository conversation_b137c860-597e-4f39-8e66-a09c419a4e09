<?php
/**
 * Componente para mostrar información geográfica del usuario
 * Incluye bandera y país de forma sutil
 */

require_once 'geo_restriction.php';

// Activar modo test si se especifica
if (isset($_GET['geo_test'])) {
    GeoRestriction::setTestMode();
}

// Obtener información del usuario
$geo_info = getUserGeoInfo();

/**
 * Generar HTML para mostrar la bandera del usuario
 */
function renderUserGeoFlag($show_country_name = false, $style = 'header') {
    global $geo_info;
    
    $flag = $geo_info['flag'];
    $country_name = $geo_info['country_name'];
    $country_code = $geo_info['country_code'];
    
    $styles = [
        'header' => [
            'container' => 'display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0.5rem; background: rgba(255,255,255,0.1); border-radius: 6px; font-size: 0.9rem;',
            'flag' => 'font-size: 1.2rem;',
            'text' => 'color: #cbd5e1; font-weight: 500;'
        ],
        'sidebar' => [
            'container' => 'display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; background: rgba(37, 99, 235, 0.1); border-radius: 8px; margin-bottom: 1rem;',
            'flag' => 'font-size: 1.5rem;',
            'text' => 'color: #f8fafc; font-weight: 600;'
        ],
        'tooltip' => [
            'container' => 'display: inline-flex; align-items: center; gap: 0.3rem; cursor: help;',
            'flag' => 'font-size: 1rem;',
            'text' => 'color: #cbd5e1; font-size: 0.8rem;'
        ]
    ];
    
    $current_style = $styles[$style] ?? $styles['header'];
    
    $html = '<div style="' . $current_style['container'] . '" title="Conectado desde ' . htmlspecialchars($country_name) . ' (' . $geo_info['ip'] . ')">';
    $html .= '<span style="' . $current_style['flag'] . '">' . $flag . '</span>';
    
    if ($show_country_name) {
        $html .= '<span style="' . $current_style['text'] . '">' . htmlspecialchars($country_name) . '</span>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Generar CSS para el componente geo
 */
function renderUserGeoCss() {
    return '
    <style>
    .user-geo-info {
        position: relative;
        display: inline-block;
    }
    
    .user-geo-info:hover .geo-tooltip {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .geo-tooltip {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%) translateY(-5px);
        background: #1e293b;
        color: #f8fafc;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        font-size: 0.8rem;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        border: 1px solid #334155;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }
    
    .geo-tooltip::before {
        content: "";
        position: absolute;
        top: -5px;
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid #1e293b;
    }
    
    .geo-flag-pulse {
        animation: geo-pulse 3s infinite;
    }
    
    @keyframes geo-pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
    
    .geo-status-indicator {
        position: relative;
    }
    
    .geo-status-indicator::after {
        content: "";
        position: absolute;
        top: -2px;
        right: -2px;
        width: 8px;
        height: 8px;
        background: #10b981;
        border-radius: 50%;
        border: 2px solid #1e293b;
        animation: geo-blink 2s infinite;
    }
    
    @keyframes geo-blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }
    </style>';
}

/**
 * Generar componente completo con tooltip
 */
function renderUserGeoComponent($username = 'Usuario', $show_details = true) {
    global $geo_info;
    
    $flag = $geo_info['flag'];
    $country_name = $geo_info['country_name'];
    $country_code = $geo_info['country_code'];
    $ip = $geo_info['ip'];
    
    $html = renderUserGeoCss();
    
    $html .= '<div class="user-geo-info">';
    $html .= '<div style="display: flex; align-items: center; gap: 0.75rem;">';
    
    // Bandera con indicador de estado
    $html .= '<div class="geo-status-indicator">';
    $html .= '<span class="geo-flag-pulse" style="font-size: 1.3rem;">' . $flag . '</span>';
    $html .= '</div>';
    
    // Nombre de usuario
    $html .= '<span style="color: #f8fafc; font-weight: 600;">' . htmlspecialchars($username) . '</span>';
    
    $html .= '</div>';
    
    if ($show_details) {
        // Tooltip con información detallada
        $html .= '<div class="geo-tooltip">';
        $html .= '<div style="font-weight: 600; margin-bottom: 0.25rem;">' . $flag . ' ' . htmlspecialchars($country_name) . '</div>';
        $html .= '<div style="opacity: 0.8;">IP: ' . htmlspecialchars($ip) . '</div>';
        $html .= '<div style="opacity: 0.6; font-size: 0.7rem; margin-top: 0.25rem;">Ubicación detectada automáticamente</div>';
        $html .= '</div>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Versión simple solo con bandera
 */
function renderSimpleGeoFlag() {
    global $geo_info;
    return '<span style="font-size: 1.2rem; margin-right: 0.5rem;" title="' . htmlspecialchars($geo_info['country_name']) . '">' . $geo_info['flag'] . '</span>';
}

/**
 * JavaScript para funcionalidades adicionales
 */
function renderGeoJavaScript() {
    global $geo_info;
    
    return '
    <script>
    // Información geo para JavaScript
    window.userGeoInfo = {
        countryCode: "' . $geo_info['country_code'] . '",
        countryName: "' . htmlspecialchars($geo_info['country_name']) . '",
        flag: "' . $geo_info['flag'] . '",
        ip: "' . htmlspecialchars($geo_info['ip']) . '",
        allowed: ' . ($geo_info['allowed'] ? 'true' : 'false') . '
    };
    
    // Log silencioso para tracking
    console.log("🌍 User Location:", window.userGeoInfo.flag, window.userGeoInfo.countryName);
    
    // Función para actualizar elementos geo dinámicamente
    function updateGeoElements() {
        const geoElements = document.querySelectorAll("[data-geo-update]");
        geoElements.forEach(element => {
            const type = element.dataset.geoUpdate;
            switch(type) {
                case "flag":
                    element.textContent = window.userGeoInfo.flag;
                    break;
                case "country":
                    element.textContent = window.userGeoInfo.countryName;
                    break;
                case "both":
                    element.textContent = window.userGeoInfo.flag + " " + window.userGeoInfo.countryName;
                    break;
            }
        });
    }
    
    // Ejecutar al cargar
    document.addEventListener("DOMContentLoaded", updateGeoElements);
    </script>';
}

// Verificar acceso si se requiere
function checkAndEnforceGeoAccess() {
    global $geo_info;
    
    if (!$geo_info['allowed']) {
        header('Location: geo_blocked.php?country=' . urlencode($geo_info['country_code']));
        exit;
    }
}
?>
