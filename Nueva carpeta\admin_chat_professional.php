<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Obtener sesiones de chat
try {
    $stmt = $pdo->query("
        SELECT cs.*, u.username 
        FROM chat_sessions cs 
        LEFT JOIN users u ON cs.user_id = u.id 
        WHERE cs.status IN ('waiting', 'active')
        ORDER BY 
            CASE cs.status 
                WHEN 'waiting' THEN 1 
                WHEN 'active' THEN 2 
            END,
            cs.started_at DESC
    ");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Obtener estadísticas
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'");
    $active_count = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'waiting'");
    $waiting_count = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE DATE(started_at) = CURDATE()");
    $today_count = $stmt->fetchColumn() ?: 0;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM chat_sessions WHERE status = 'ended' AND DATE(ended_at) = CURDATE()");
    $resolved_today = $stmt->fetchColumn() ?: 0;
    
} catch (Exception $e) {
    $error_message = "Error al obtener datos: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 Chat en Tiempo Real Profesional - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #10b981;
            --primary-light: #34d399;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --light-bg: #f8fafc;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #f8fafc;
            --accent-color: #06b6d4;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
            --gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .header-nav {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--light-bg);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        .nav-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .nav-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Main Layout */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 2rem;
            height: calc(100vh - 120px);
        }

        /* Page Header */
        .page-header {
            grid-column: 1 / -1;
            margin-bottom: 1rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Stats Grid */
        .stats-grid {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .stat-card.active::before {
            background: var(--gradient-primary);
        }

        .stat-card.waiting::before {
            background: var(--gradient-warning);
        }

        .stat-card.today::before {
            background: var(--gradient-info);
        }

        .stat-card.resolved::before {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .stat-icon.active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--primary-color);
        }

        .stat-icon.waiting {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.today {
            background: rgba(6, 182, 212, 0.1);
            color: var(--accent-color);
        }

        .stat-icon.resolved {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        /* Sessions Panel */
        .sessions-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
        }

        .panel-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sessions-list {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }

        .session-item {
            padding: 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-light);
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: var(--transition);
            background: white;
        }

        .session-item:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
            transform: translateY(-1px);
        }

        .session-item.selected {
            border-color: var(--primary-color);
            background: rgba(16, 185, 129, 0.05);
        }

        .session-item.waiting {
            border-left: 4px solid var(--warning-color);
        }

        .session-item.active {
            border-left: 4px solid var(--primary-color);
        }

        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .session-user {
            font-weight: 600;
            color: var(--text-primary);
        }

        .session-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .session-status.waiting {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .session-status.active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--primary-color);
        }

        .session-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        /* Chat Panel */
        .chat-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .chat-actions {
            display: flex;
            gap: 0.5rem;
        }

        .chat-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .chat-btn.end {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .chat-btn.end:hover {
            background: var(--error-color);
            color: white;
        }

        .chat-messages {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
            background: var(--light-bg);
            min-height: 400px;
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            max-width: 80%;
            animation: fadeIn 0.3s ease;
        }

        .message.user {
            background: rgba(6, 182, 212, 0.1);
            margin-left: auto;
            text-align: right;
            border: 1px solid rgba(6, 182, 212, 0.2);
        }

        .message.admin {
            background: rgba(16, 185, 129, 0.1);
            margin-right: auto;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-sender {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            font-weight: 500;
        }

        .message-text {
            color: var(--text-primary);
            line-height: 1.5;
        }

        .message-time {
            font-size: 0.7rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .chat-input {
            padding: 1.5rem;
            border-top: 1px solid var(--border-color);
            background: white;
        }

        .input-form {
            display: flex;
            gap: 0.75rem;
        }

        .message-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            resize: none;
            font-family: inherit;
            transition: var(--transition);
        }

        .message-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .send-btn {
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
        }

        .send-btn:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-secondary);
            text-align: center;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
                padding: 1rem;
                height: auto;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading {
            animation: pulse 2s infinite;
        }

        /* Online indicator */
        .online-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--gradient-primary);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 1000;
            box-shadow: var(--shadow-lg);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .online-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="admin_chat_professional.php" class="logo">
                <i class="fas fa-comments"></i>
                <span>Chat en Tiempo Real</span>
            </a>

            <nav class="header-nav">
                <a href="admin2_professional.php" class="nav-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin_professional.php" class="nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="user_chat_realtime.php" class="nav-btn primary">
                    <i class="fas fa-play"></i>
                    <span>Iniciar Chat</span>
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-comments" style="color: var(--primary-color);"></i>
                Panel de Chat en Tiempo Real
            </h1>
            <p class="page-subtitle">
                Gestión de sesiones de chat en vivo con soporte instantáneo para usuarios
            </p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card active">
                <div class="stat-header">
                    <div class="stat-title">Chats Activos</div>
                    <div class="stat-icon active">
                        <i class="fas fa-comment-dots"></i>
                    </div>
                </div>
                <div class="stat-number" id="activeCount"><?php echo $active_count; ?></div>
                <div class="stat-change">En conversación</div>
            </div>

            <div class="stat-card waiting">
                <div class="stat-header">
                    <div class="stat-title">En Espera</div>
                    <div class="stat-icon waiting">
                        <i class="fas fa-hourglass-half"></i>
                    </div>
                </div>
                <div class="stat-number" id="waitingCount"><?php echo $waiting_count; ?></div>
                <div class="stat-change">Esperando agente</div>
            </div>

            <div class="stat-card today">
                <div class="stat-header">
                    <div class="stat-title">Chats Hoy</div>
                    <div class="stat-icon today">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                </div>
                <div class="stat-number" id="todayCount"><?php echo $today_count; ?></div>
                <div class="stat-change">Sesiones iniciadas</div>
            </div>

            <div class="stat-card resolved">
                <div class="stat-header">
                    <div class="stat-title">Resueltos Hoy</div>
                    <div class="stat-icon resolved">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-number" id="resolvedCount"><?php echo $resolved_today; ?></div>
                <div class="stat-change">Completados</div>
            </div>
        </div>

        <!-- Sessions Panel -->
        <div class="sessions-panel">
            <div class="panel-header">
                <h2 class="panel-title">
                    <i class="fas fa-list"></i>
                    Sesiones Activas (<?php echo count($sessions); ?>)
                </h2>
            </div>

            <div class="sessions-list">
                <?php if (empty($sessions)): ?>
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <h3>No hay sesiones activas</h3>
                    <p>Las nuevas sesiones de chat aparecerán aquí</p>
                </div>
                <?php else: ?>
                    <?php foreach ($sessions as $session): ?>
                    <div class="session-item <?php echo $session['status']; ?>" onclick="selectSession(<?php echo $session['id']; ?>)">
                        <div class="session-header">
                            <div class="session-user">
                                <i class="fas fa-user"></i>
                                <?php echo htmlspecialchars($session['username'] ?? 'Usuario #' . $session['user_id']); ?>
                            </div>
                            <div class="session-status <?php echo $session['status']; ?>">
                                <?php echo $session['status'] === 'waiting' ? 'Esperando' : 'Activo'; ?>
                            </div>
                        </div>
                        <div class="session-time">
                            Iniciado: <?php
                            $time_diff = time() - strtotime($session['started_at']);
                            if ($time_diff < 60) {
                                echo "Hace " . $time_diff . " segundos";
                            } elseif ($time_diff < 3600) {
                                echo "Hace " . floor($time_diff / 60) . " minutos";
                            } else {
                                echo "Hace " . floor($time_diff / 3600) . " horas";
                            }
                            ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Chat Panel -->
        <div class="chat-panel">
            <div class="chat-header">
                <div class="chat-title" id="chatTitle">
                    <i class="fas fa-comment"></i>
                    Selecciona una sesión
                </div>
                <div class="chat-actions" id="chatActions" style="display: none;">
                    <button class="chat-btn end" onclick="endSession()">
                        <i class="fas fa-times"></i>
                        Finalizar Chat
                    </button>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="empty-state">
                    <i class="fas fa-mouse-pointer"></i>
                    <h3>Selecciona una sesión de chat</h3>
                    <p>Haz clic en una sesión de la lista para comenzar a chatear</p>
                </div>
            </div>

            <div class="chat-input" id="chatInput" style="display: none;">
                <form class="input-form" onsubmit="sendMessage(event)">
                    <input type="hidden" id="sessionId" value="">
                    <textarea
                        id="messageInput"
                        class="message-input"
                        placeholder="Escribe tu respuesta..."
                        rows="2"
                        required
                    ></textarea>
                    <button type="submit" class="send-btn" id="sendBtn">
                        <i class="fas fa-paper-plane"></i>
                        Enviar
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Online Indicator -->
    <div class="online-indicator">
        <div class="online-dot"></div>
        <span>En línea</span>
    </div>

    <script>
        let currentSessionId = null;
        let messageInterval = null;
        let sessionsInterval = null;

        // Auto-refresh sessions every 10 seconds
        sessionsInterval = setInterval(refreshSessions, 10000);

        // Auto-refresh stats every 30 seconds
        setInterval(refreshStats, 30000);

        function refreshSessions() {
            // Reload page to get updated sessions
            if (!currentSessionId) {
                location.reload();
            }
        }

        function refreshStats() {
            fetch('api_chat_stats.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateElement('activeCount', data.active || 0);
                        updateElement('waitingCount', data.waiting || 0);
                        updateElement('todayCount', data.today || 0);
                        updateElement('resolvedCount', data.resolved_today || 0);
                    }
                })
                .catch(error => console.error('Error updating stats:', error));
        }

        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element && element.textContent != value) {
                element.textContent = value;
                element.style.transform = 'scale(1.1)';
                element.style.color = 'var(--primary-color)';

                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 300);
            }
        }

        function selectSession(sessionId) {
            currentSessionId = sessionId;
            document.getElementById('sessionId').value = sessionId;

            // Mark session as selected
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.session-item').classList.add('selected');

            // Update chat header
            const sessionUser = event.target.closest('.session-item').querySelector('.session-user').textContent.trim();
            document.getElementById('chatTitle').innerHTML = `<i class="fas fa-comment"></i> Chat con ${sessionUser}`;

            // Show chat actions and input
            document.getElementById('chatActions').style.display = 'flex';
            document.getElementById('chatInput').style.display = 'block';

            // Load messages
            loadMessages(sessionId);

            // Start message polling
            startMessagePolling();
        }

        function loadMessages(sessionId) {
            if (!sessionId) return;

            fetch(`api_chat.php?action=get_messages&session_id=${sessionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayMessages(data.messages);
                    } else {
                        console.error('Error loading messages:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('chatMessages').innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>Error cargando mensajes</h3>
                            <p>Intenta seleccionar la sesión nuevamente</p>
                        </div>
                    `;
                });
        }

        function displayMessages(messages) {
            const container = document.getElementById('chatMessages');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-comments"></i>
                        <h3>No hay mensajes aún</h3>
                        <p>Inicia la conversación con el usuario</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';
            messages.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.is_admin == 1 ? 'admin' : 'user'}`;

                const time = new Date(msg.sent_at).toLocaleTimeString('es-ES', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                messageDiv.innerHTML = `
                    <div class="message-sender">${msg.is_admin == 1 ? 'Agente de Soporte' : (msg.username || 'Usuario')}</div>
                    <div class="message-text">${msg.message.replace(/\n/g, '<br>')}</div>
                    <div class="message-time">${time}</div>
                `;

                container.appendChild(messageDiv);
            });

            // Scroll to bottom
            container.scrollTop = container.scrollHeight;
        }

        function sendMessage(event) {
            event.preventDefault();

            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message || !currentSessionId) return;

            const formData = new FormData();
            formData.append('session_id', currentSessionId);
            formData.append('message', message);

            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';

            fetch('api_chat.php?action=send_message', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    loadMessages(currentSessionId);
                } else {
                    alert('Error al enviar mensaje: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error de conexión');
            })
            .finally(() => {
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Enviar';
            });
        }

        function endSession() {
            if (!currentSessionId) return;

            if (!confirm('¿Estás seguro de que quieres finalizar esta sesión de chat?')) return;

            const formData = new FormData();
            formData.append('session_id', currentSessionId);

            fetch('api_chat.php?action=end_chat', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Sesión finalizada correctamente');
                    location.reload();
                } else {
                    alert('Error al finalizar sesión: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error de conexión');
            });
        }

        function startMessagePolling() {
            if (messageInterval) clearInterval(messageInterval);
            messageInterval = setInterval(() => {
                if (currentSessionId) {
                    loadMessages(currentSessionId);
                }
            }, 3000);
        }

        function stopMessagePolling() {
            if (messageInterval) {
                clearInterval(messageInterval);
                messageInterval = null;
            }
        }

        // Send message with Enter (Shift+Enter for new line)
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                document.querySelector('.input-form').dispatchEvent(new Event('submit'));
            }
        });

        // Cleanup intervals on page unload
        window.addEventListener('beforeunload', function() {
            stopMessagePolling();
            if (sessionsInterval) clearInterval(sessionsInterval);
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add animations to stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-2px)';
                });
            });
        });
    </script>
</body>
</html>
