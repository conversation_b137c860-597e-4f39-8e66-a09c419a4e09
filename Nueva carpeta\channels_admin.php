<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'approve_request':
                $request_id = (int)$_POST['request_id'];
                $admin_id = $_SESSION['admin_id'] ?? 1;
                
                $stmt = $pdo->prepare("UPDATE channel_requests SET status = 'approved', processed_by = ?, processed_at = NOW() WHERE id = ?");
                $stmt->execute([$admin_id, $request_id]);
                
                $success_message = "Solicitud de canal aprobada";
                break;
                
            case 'reject_request':
                $request_id = (int)$_POST['request_id'];
                $admin_notes = $_POST['admin_notes'] ?? '';
                $admin_id = $_SESSION['admin_id'] ?? 1;
                
                $stmt = $pdo->prepare("UPDATE channel_requests SET status = 'rejected', admin_notes = ?, processed_by = ?, processed_at = NOW() WHERE id = ?");
                $stmt->execute([$admin_notes, $admin_id, $request_id]);
                
                $success_message = "Solicitud de canal rechazada";
                break;
                
            case 'add_channel':
                $channel_name = $_POST['channel_name'];
                $channel_description = $_POST['channel_description'];
                $channel_url = $_POST['channel_url'];
                $category = $_POST['category'];
                $country = $_POST['country'];
                $language = $_POST['language'];
                $admin_id = $_SESSION['admin_id'] ?? 1;
                
                $stmt = $pdo->prepare("INSERT INTO channel_requests (user_id, channel_name, channel_description, channel_url, category, country, language, status, processed_by, processed_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'approved', ?, NOW())");
                $stmt->execute([$admin_id, $channel_name, $channel_description, $channel_url, $category, $country, $language, $admin_id]);
                
                $success_message = "Canal agregado correctamente";
                break;
                
            case 'update_notes':
                $request_id = (int)$_POST['request_id'];
                $admin_notes = $_POST['admin_notes'];
                
                $stmt = $pdo->prepare("UPDATE channel_requests SET admin_notes = ? WHERE id = ?");
                $stmt->execute([$admin_notes, $request_id]);
                
                $success_message = "Notas actualizadas";
                break;
        }
    }
}

// Obtener filtros
$status_filter = $_GET['status'] ?? 'all';
$category_filter = $_GET['category'] ?? 'all';
$country_filter = $_GET['country'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "cr.status = ?";
    $params[] = $status_filter;
}

if ($category_filter !== 'all') {
    $where_conditions[] = "cr.category = ?";
    $params[] = $category_filter;
}

if ($country_filter !== 'all') {
    $where_conditions[] = "cr.country = ?";
    $params[] = $country_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener solicitudes de canales
$stmt = $pdo->prepare("
    SELECT cr.*, u.username, 
           admin_u.username as processed_by_name
    FROM channel_requests cr 
    LEFT JOIN users u ON cr.user_id = u.id 
    LEFT JOIN users admin_u ON cr.processed_by = admin_u.id
    $where_clause
    ORDER BY 
        CASE 
            WHEN cr.status = 'pending' THEN 1
            WHEN cr.status = 'investigating' THEN 2
            WHEN cr.status = 'approved' THEN 3
            WHEN cr.status = 'rejected' THEN 4
        END,
        cr.created_at DESC
");
$stmt->execute($params);
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN status = 'investigating' THEN 1 ELSE 0 END) as investigating,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today
    FROM channel_requests
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Obtener categorías y países únicos para filtros
$categories_stmt = $pdo->query("SELECT DISTINCT category FROM channel_requests WHERE category IS NOT NULL AND category != '' ORDER BY category");
$categories = $categories_stmt->fetchAll(PDO::FETCH_COLUMN);

$countries_stmt = $pdo->query("SELECT DISTINCT country FROM channel_requests WHERE country IS NOT NULL AND country != '' ORDER BY country");
$countries = $countries_stmt->fetchAll(PDO::FETCH_COLUMN);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📺 Solicitudes de Canales - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
            position: relative;
        }

        .stat-card.pending::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--warning-color);
        }

        .stat-card.approved::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--success-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .filters {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .requests-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .request-item {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .request-item:hover {
            background: rgba(255,255,255,0.05);
        }

        .request-item:last-child {
            border-bottom: none;
        }

        .request-pending {
            border-left: 4px solid var(--warning-color);
        }

        .request-approved {
            border-left: 4px solid var(--success-color);
        }

        .request-rejected {
            border-left: 4px solid var(--error-color);
        }

        .request-investigating {
            border-left: 4px solid var(--primary-color);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .request-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .request-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending { 
            background: rgba(245, 158, 11, 0.2); 
            color: #f59e0b; 
        }

        .status-approved { 
            background: rgba(16, 185, 129, 0.2); 
            color: #10b981; 
        }

        .status-rejected { 
            background: rgba(239, 68, 68, 0.2); 
            color: #ef4444; 
        }

        .status-investigating { 
            background: rgba(37, 99, 235, 0.2); 
            color: #2563eb; 
        }

        .request-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .request-details {
            background: rgba(255,255,255,0.05);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin: 1rem 0;
        }

        .detail-row {
            display: flex;
            margin-bottom: 0.5rem;
        }

        .detail-label {
            font-weight: 500;
            color: var(--text-primary);
            min-width: 120px;
        }

        .detail-value {
            color: var(--text-secondary);
        }

        .admin-notes {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-top: 1rem;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .add-channel-form {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            display: none;
        }

        .add-channel-form.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .request-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .request-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="channels_admin.php" class="logo">
                <i class="fas fa-tv"></i>
                <span>Solicitudes de Canales</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-tv"></i>
                Solicitudes de Canales
            </h1>
            
            <button onclick="toggleAddForm()" class="btn btn-success">
                <i class="fas fa-plus"></i>
                Agregar Canal
            </button>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Solicitudes</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number"><?php echo $stats['pending']; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>
            <div class="stat-card approved">
                <div class="stat-number"><?php echo $stats['approved']; ?></div>
                <div class="stat-label">Aprobadas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['rejected']; ?></div>
                <div class="stat-label">Rechazadas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['investigating']; ?></div>
                <div class="stat-label">Investigando</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today']; ?></div>
                <div class="stat-label">Hoy</div>
            </div>
        </div>

        <!-- Formulario para Agregar Canal -->
        <div id="addChannelForm" class="add-channel-form">
            <h2 style="margin-bottom: 1.5rem; color: var(--text-primary);">
                <i class="fas fa-plus"></i>
                Agregar Nuevo Canal
            </h2>

            <form method="POST">
                <input type="hidden" name="action" value="add_channel">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Nombre del Canal</label>
                        <input type="text" name="channel_name" class="form-input" required placeholder="Ej: CNN en Español">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Categoría</label>
                        <input type="text" name="category" class="form-input" placeholder="Ej: Noticias, Deportes, Entretenimiento">
                    </div>

                    <div class="form-group">
                        <label class="form-label">País</label>
                        <input type="text" name="country" class="form-input" placeholder="Ej: España, México, Argentina">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Idioma</label>
                        <input type="text" name="language" class="form-input" placeholder="Ej: Español, Inglés, Francés">
                    </div>
                </div>

                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">URL del Canal</label>
                    <input type="url" name="channel_url" class="form-input" placeholder="https://ejemplo.com/canal.m3u8">
                </div>

                <div class="form-group" style="margin-bottom: 2rem;">
                    <label class="form-label">Descripción</label>
                    <textarea name="channel_description" class="form-textarea" placeholder="Descripción del canal..."></textarea>
                </div>

                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus"></i>
                        Agregar Canal
                    </button>
                    <button type="button" onclick="toggleAddForm()" class="btn btn-warning">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pendientes</option>
                        <option value="investigating" <?php echo $status_filter === 'investigating' ? 'selected' : ''; ?>>Investigando</option>
                        <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Aprobadas</option>
                        <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rechazadas</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Categoría</label>
                    <select name="category" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo htmlspecialchars($category); ?>" <?php echo $category_filter === $category ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">País</label>
                    <select name="country" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $country_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <?php foreach ($countries as $country): ?>
                        <option value="<?php echo htmlspecialchars($country); ?>" <?php echo $country_filter === $country ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($country); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </form>
        </div>

        <!-- Lista de Solicitudes -->
        <div class="requests-container">
            <?php if (empty($requests)): ?>
            <div class="request-item" style="text-align: center; color: var(--text-secondary);">
                <i class="fas fa-tv" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No hay solicitudes de canales que coincidan con los filtros seleccionados</p>
            </div>
            <?php else: ?>
                <?php foreach ($requests as $request): ?>
                <div class="request-item request-<?php echo $request['status']; ?>">
                    <div class="request-header">
                        <div>
                            <div class="request-title">
                                📺 <?php echo htmlspecialchars($request['channel_name']); ?>
                            </div>
                            <div class="request-meta">
                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($request['username'] ?? 'Usuario desconocido'); ?></span>
                                <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($request['created_at'])); ?></span>
                                <?php if ($request['processed_at']): ?>
                                <span><i class="fas fa-check"></i> Procesado: <?php echo date('d/m/Y H:i', strtotime($request['processed_at'])); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge status-<?php echo $request['status']; ?>">
                                <?php
                                $status_labels = [
                                    'pending' => 'Pendiente',
                                    'investigating' => 'Investigando',
                                    'approved' => 'Aprobada',
                                    'rejected' => 'Rechazada'
                                ];
                                echo $status_labels[$request['status']] ?? $request['status'];
                                ?>
                            </span>
                        </div>
                    </div>

                    <div class="request-details">
                        <?php if ($request['channel_description']): ?>
                        <div class="detail-row">
                            <div class="detail-label">Descripción:</div>
                            <div class="detail-value"><?php echo nl2br(htmlspecialchars($request['channel_description'])); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if ($request['channel_url']): ?>
                        <div class="detail-row">
                            <div class="detail-label">URL:</div>
                            <div class="detail-value">
                                <a href="<?php echo htmlspecialchars($request['channel_url']); ?>" target="_blank" style="color: var(--accent-color);">
                                    <?php echo htmlspecialchars($request['channel_url']); ?>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($request['category']): ?>
                        <div class="detail-row">
                            <div class="detail-label">Categoría:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($request['category']); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if ($request['country']): ?>
                        <div class="detail-row">
                            <div class="detail-label">País:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($request['country']); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if ($request['language']): ?>
                        <div class="detail-row">
                            <div class="detail-label">Idioma:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($request['language']); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if ($request['processed_by_name']): ?>
                        <div class="detail-row">
                            <div class="detail-label">Procesado por:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($request['processed_by_name']); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($request['admin_notes']): ?>
                    <div class="admin-notes">
                        <strong>Notas del administrador:</strong><br>
                        <?php echo nl2br(htmlspecialchars($request['admin_notes'])); ?>
                    </div>
                    <?php endif; ?>

                    <div class="request-actions">
                        <?php if ($request['status'] === 'pending' || $request['status'] === 'investigating'): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="approve_request">
                            <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i>
                                Aprobar
                            </button>
                        </form>

                        <button onclick="showRejectForm(<?php echo $request['id']; ?>)" class="btn btn-danger">
                            <i class="fas fa-times"></i>
                            Rechazar
                        </button>

                        <button onclick="showNotesForm(<?php echo $request['id']; ?>, '<?php echo addslashes($request['admin_notes']); ?>')" class="btn btn-warning">
                            <i class="fas fa-sticky-note"></i>
                            Notas
                        </button>
                        <?php endif; ?>

                        <?php if ($request['channel_url']): ?>
                        <a href="<?php echo htmlspecialchars($request['channel_url']); ?>" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt"></i>
                            Probar Canal
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>

    <!-- Modal para rechazar solicitud -->
    <div id="rejectModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--secondary-color); padding: 2rem; border-radius: var(--border-radius); width: 90%; max-width: 500px;">
            <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Rechazar Solicitud</h3>
            <form method="POST">
                <input type="hidden" name="action" value="reject_request">
                <input type="hidden" name="request_id" id="rejectRequestId">
                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">Motivo del rechazo</label>
                    <textarea name="admin_notes" class="form-textarea" placeholder="Explica por qué se rechaza esta solicitud..." required></textarea>
                </div>
                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        Rechazar
                    </button>
                    <button type="button" onclick="closeRejectModal()" class="btn btn-warning">
                        <i class="fas fa-arrow-left"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para notas -->
    <div id="notesModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--secondary-color); padding: 2rem; border-radius: var(--border-radius); width: 90%; max-width: 500px;">
            <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Notas del Administrador</h3>
            <form method="POST">
                <input type="hidden" name="action" value="update_notes">
                <input type="hidden" name="request_id" id="notesRequestId">
                <div class="form-group" style="margin-bottom: 1.5rem;">
                    <label class="form-label">Notas</label>
                    <textarea name="admin_notes" id="notesTextarea" class="form-textarea" placeholder="Agrega notas sobre esta solicitud..."></textarea>
                </div>
                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i>
                        Guardar
                    </button>
                    <button type="button" onclick="closeNotesModal()" class="btn btn-warning">
                        <i class="fas fa-arrow-left"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function toggleAddForm() {
            const form = document.getElementById('addChannelForm');
            form.classList.toggle('active');

            if (form.classList.contains('active')) {
                form.scrollIntoView({ behavior: 'smooth' });
                const firstInput = form.querySelector('input[type="text"]');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 300);
                }
            }
        }

        function showRejectForm(requestId) {
            document.getElementById('rejectRequestId').value = requestId;
            document.getElementById('rejectModal').style.display = 'block';
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').style.display = 'none';
        }

        function showNotesForm(requestId, currentNotes) {
            document.getElementById('notesRequestId').value = requestId;
            document.getElementById('notesTextarea').value = currentNotes;
            document.getElementById('notesModal').style.display = 'block';
        }

        function closeNotesModal() {
            document.getElementById('notesModal').style.display = 'none';
        }

        // Cerrar modales al hacer clic fuera
        window.onclick = function(event) {
            const rejectModal = document.getElementById('rejectModal');
            const notesModal = document.getElementById('notesModal');

            if (event.target === rejectModal) {
                closeRejectModal();
            }
            if (event.target === notesModal) {
                closeNotesModal();
            }
        }

        // Auto-refresh cada 30 segundos para solicitudes pendientes
        setInterval(function() {
            if (document.hidden) return;

            const currentStatus = new URLSearchParams(window.location.search).get('status');
            if (currentStatus === 'pending' || currentStatus === 'all') {
                const activeElement = document.activeElement;
                if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                    location.reload();
                }
            }
        }, 30000);

        // Confirmación para acciones críticas
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'approve_request') {
                    if (!confirm('¿Estás seguro de que quieres aprobar esta solicitud de canal?')) {
                        e.preventDefault();
                    }
                }
            });
        });
    </script>
</body>
</html>
