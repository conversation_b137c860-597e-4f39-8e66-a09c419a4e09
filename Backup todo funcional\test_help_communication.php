<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔗 Probando Comunicación del Sistema de Ayuda</h1>";

try {
    // 1. Ejecutar el script de reparación primero
    echo "<h2>🔧 Ejecutando reparación del sistema...</h2>";
    
    // Simular la ejecución del script de reparación
    include_once 'fix_help_system.php';
    
    echo "<h2>🧪 Probando API del Sistema de Ayuda...</h2>";
    
    // 2. Probar obtener artículos
    $api_url = 'api_help_system.php';
    
    // Test 1: Obtener todos los artículos
    $response = file_get_contents($api_url . '?action=get_articles');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $success_messages[] = "✅ API get_articles: " . $data['total'] . " artículos encontrados";
    } else {
        $error_messages[] = "❌ API get_articles falló";
    }
    
    // Test 2: Obtener categorías
    $response = file_get_contents($api_url . '?action=get_categories');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $success_messages[] = "✅ API get_categories: " . count($data['categories']) . " categorías encontradas";
    } else {
        $error_messages[] = "❌ API get_categories falló";
    }
    
    // Test 3: Obtener artículos destacados
    $response = file_get_contents($api_url . '?action=get_featured');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $success_messages[] = "✅ API get_featured: " . count($data['articles']) . " artículos destacados";
    } else {
        $error_messages[] = "❌ API get_featured falló";
    }
    
    // Test 4: Búsqueda
    $response = file_get_contents($api_url . '?action=search_articles&q=IPTV');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $success_messages[] = "✅ API search_articles: " . $data['total'] . " resultados para 'IPTV'";
    } else {
        $error_messages[] = "❌ API search_articles falló";
    }
    
    // Test 5: Artículos populares
    $response = file_get_contents($api_url . '?action=get_popular&limit=3');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $success_messages[] = "✅ API get_popular: " . count($data['articles']) . " artículos populares";
    } else {
        $error_messages[] = "❌ API get_popular falló";
    }
    
    echo "<h2>📊 Probando Estadísticas de Admin...</h2>";
    
    // Test 6: Estadísticas (solo admin)
    $response = file_get_contents($api_url . '?action=get_stats');
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        $stats = $data['stats'];
        $success_messages[] = "✅ API get_stats: {$stats['total_articles']} artículos totales, {$stats['total_views']} vistas";
    } else {
        $error_messages[] = "❌ API get_stats falló";
    }
    
    echo "<h2>🔍 Verificando Archivos del Sistema...</h2>";
    
    // Verificar que los archivos principales existen
    $required_files = [
        'user_help.php' => 'Centro de Ayuda para Usuarios',
        'help_admin.php' => 'Panel de Administración de Ayuda',
        'api_help_system.php' => 'API del Sistema de Ayuda'
    ];
    
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            $success_messages[] = "✅ Archivo encontrado: $file ($description)";
        } else {
            $error_messages[] = "❌ Archivo faltante: $file ($description)";
        }
    }
    
    echo "<h2>🗄️ Verificando Base de Datos...</h2>";
    
    // Verificar estructura de la base de datos
    $stmt = $pdo->query("DESCRIBE help_articles");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = ['id', 'title', 'content', 'excerpt', 'category', 'status', 'is_featured', 'view_count'];
    $missing_columns = array_diff($required_columns, $columns);
    
    if (empty($missing_columns)) {
        $success_messages[] = "✅ Estructura de tabla help_articles correcta";
    } else {
        $error_messages[] = "❌ Columnas faltantes en help_articles: " . implode(', ', $missing_columns);
    }
    
    // Verificar datos de ejemplo
    $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles WHERE status = 'published'");
    $published_count = $stmt->fetchColumn();
    
    if ($published_count > 0) {
        $success_messages[] = "✅ Base de datos tiene $published_count artículos publicados";
    } else {
        $error_messages[] = "❌ No hay artículos publicados en la base de datos";
    }
    
    echo "<h2>🔗 Probando Integración con admin2.php...</h2>";
    
    // Verificar que help_admin.php está enlazado desde admin2.php
    if (file_exists('admin2.php')) {
        $admin2_content = file_get_contents('admin2.php');
        if (strpos($admin2_content, 'help_admin.php') !== false) {
            $success_messages[] = "✅ help_admin.php está enlazado en admin2.php";
        } else {
            $error_messages[] = "❌ help_admin.php NO está enlazado en admin2.php";
        }
    }
    
    // Verificar que user_help.php está enlazado desde index2.php
    if (file_exists('index2.php')) {
        $index2_content = file_get_contents('index2.php');
        if (strpos($index2_content, 'user_help.php') !== false) {
            $success_messages[] = "✅ user_help.php está enlazado en index2.php";
        } else {
            $error_messages[] = "❌ user_help.php NO está enlazado en index2.php";
        }
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Test Comunicación Sistema de Ayuda - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .test-card {
            background: #334155;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Test de Comunicación del Sistema de Ayuda</h1>
        
        <div class="summary">
            <h3>📊 Resumen de Pruebas</h3>
            <div class="grid">
                <div class="test-card">
                    <h4>✅ Pruebas Exitosas</h4>
                    <p><strong><?php echo count($success_messages); ?></strong> tests pasaron</p>
                </div>
                <div class="test-card" style="border-color: #ef4444;">
                    <h4>❌ Pruebas Fallidas</h4>
                    <p><strong><?php echo count($error_messages); ?></strong> tests fallaron</p>
                </div>
            </div>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Pruebas Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Pruebas Fallidas</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="user_help.php" class="btn">👥 Centro de Ayuda</a>
            <a href="help_admin.php" class="btn">⚙️ Admin Ayuda</a>
            <a href="api_help_system.php?action=get_articles" class="btn">🔌 Test API</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
    </div>
</body>
</html>
