<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

session_start();

// Configuración de base de datos
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'check_new_orders':
        // Para admin: verificar nuevos pedidos
        if (!isset($_SESSION['admin_logged_in'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE status = 'Recibido'");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        echo json_encode([
            'new_orders' => (int)$result['count'],
            'timestamp' => time()
        ]);
        break;
        
    case 'mark_orders_seen':
        // Esta acción ya no es necesaria ya que usamos status en lugar de notif_seen
        echo json_encode(['success' => true, 'message' => 'No action needed']);
        break;

    case 'get_section_orders':
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }

        $section = $_GET['section'] ?? '';
        $html = '';

        try {
            // Definir las consultas por sección
            $queries = [
                'new' => "SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE o.status = 'Recibido' ORDER BY o.created_at DESC LIMIT 50",
                'pending' => "SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE o.status IN ('Pendiente', 'En Cola') ORDER BY o.created_at DESC LIMIT 50",
                'processing' => "SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE o.status = 'Procesando' ORDER BY o.created_at DESC LIMIT 50",
                'existing' => "SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE o.status = 'Ya en existencia' ORDER BY o.created_at DESC LIMIT 50",
                'completed' => "SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE o.status = 'Listo' ORDER BY o.created_at DESC LIMIT 50",
                'unavailable' => "SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE o.status = 'No disponible' ORDER BY o.created_at DESC LIMIT 50"
            ];

            if (isset($queries[$section])) {
                $stmt = $pdo->query($queries[$section]);
                $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Generar HTML de la tabla
                $html = generateOrdersTableHTML($orders, $section);
            }

            echo json_encode(['success' => true, 'html' => $html]);

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        break;
        
    case 'check_user_notifications':
        // Para usuario: verificar cambios de estado en sus pedidos
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }

        $user_id = $_SESSION['user_id'];

        // Verificar si hay pedidos con cambios de estado no vistos
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count,
                   GROUP_CONCAT(DISTINCT status) as statuses,
                   MAX(id) as latest_order_id
            FROM orders
            WHERE user_id = ? AND status_notif_seen = 0 AND status != 'Recibido'
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $count = (int)$result['count'];

        echo json_encode([
            'has_notifications' => $count > 0,
            'count' => $count,
            'statuses' => $result['statuses'],
            'latest_order_id' => $result['latest_order_id'],
            'timestamp' => time()
        ]);
        break;
        
    case 'get_user_notifications':
        // Para usuario: obtener detalles de notificaciones
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }
        
        $user_id = $_SESSION['user_id'];
        
        $stmt = $pdo->prepare("
            SELECT id, title, media_type, status, created_at, updated_at
            FROM orders
            WHERE user_id = ? AND status_notif_seen = 0 AND status != 'Recibido'
            ORDER BY updated_at DESC
        ");
        $stmt->execute([$user_id]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'notifications' => $notifications,
            'count' => count($notifications)
        ]);
        break;
        
    case 'mark_user_notifications_seen':
        // Para usuario: marcar notificaciones como vistas
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }

        $user_id = $_SESSION['user_id'];
        $order_ids = $_POST['order_ids'] ?? [];

        if (!empty($order_ids)) {
            $placeholders = str_repeat('?,', count($order_ids) - 1) . '?';
            $stmt = $pdo->prepare("
                UPDATE orders
                SET status_notif_seen = 1, client_notif_seen = 1, client_notif_seen_at = NOW()
                WHERE user_id = ? AND id IN ($placeholders)
            ");
            $params = array_merge([$user_id], $order_ids);
            $stmt->execute($params);
        } else {
            // Marcar todas como vistas
            $stmt = $pdo->prepare("
                UPDATE orders
                SET status_notif_seen = 1, client_notif_seen = 1, client_notif_seen_at = NOW()
                WHERE user_id = ?
            ");
            $stmt->execute([$user_id]);
        }

        echo json_encode(['success' => true]);
        break;
        
    case 'get_latest_orders':
        // Para admin: obtener últimos pedidos
        if (!isset($_SESSION['admin_logged_in'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }
        
        $stmt = $pdo->query("
            SELECT o.*, u.username, u.is_cliente_actual, u.is_mavistv, u.is_tvdigital, u.is_limites507
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            ORDER BY o.created_at DESC
            LIMIT 50
        ");
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'orders' => $orders,
            'timestamp' => time()
        ]);
        break;
        
    case 'update_order_status':
        // Para admin: actualizar estado de pedido
        if (!isset($_SESSION['admin_logged_in'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }
        
        $order_id = $_POST['order_id'] ?? 0;
        $new_status = $_POST['status'] ?? '';
        
        if ($order_id && $new_status) {
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET status = ?, updated_at = NOW(), status_notif_seen = 0 
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $order_id]);
            
            echo json_encode(['success' => true]);
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Missing parameters']);
        }
        break;
        
    case 'archive_old_orders':
        // Para admin: archivar pedidos antiguos (más de 30 días con estado "Listo")
        if (!isset($_SESSION['admin_logged_in'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }
        
        // Crear tabla de historial si no existe
        $pdo->exec("CREATE TABLE IF NOT EXISTS orders_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_order_id INT,
            user_id INT,
            tmdb_id INT,
            title VARCHAR(255),
            media_type VARCHAR(32),
            year VARCHAR(8),
            country VARCHAR(64),
            city VARCHAR(64),
            ip_address VARCHAR(45),
            status VARCHAR(32),
            created_at DATETIME,
            updated_at DATETIME,
            archived_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            notif_seen TINYINT(1) DEFAULT 1
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        
        // Mover pedidos antiguos al historial
        $stmt = $pdo->prepare("
            INSERT INTO orders_history 
            (original_order_id, user_id, tmdb_id, title, media_type, year, country, city, ip_address, status, created_at, updated_at)
            SELECT id, user_id, tmdb_id, title, media_type, year, country, city, ip_address, 'Disponible', created_at, updated_at
            FROM orders 
            WHERE status = 'Listo' AND updated_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");
        $stmt->execute();
        $archived_count = $stmt->rowCount();
        
        // Eliminar de la tabla principal
        $pdo->exec("
            DELETE FROM orders 
            WHERE status = 'Listo' AND updated_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");
        
        echo json_encode([
            'success' => true,
            'archived_count' => $archived_count
        ]);
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
        break;
}

// Función para generar HTML de tabla de pedidos
function generateOrdersTableHTML($orders, $section) {
    if (empty($orders)) {
        $messages = [
            'new' => 'No hay pedidos recibidos en este momento',
            'pending' => 'No hay pedidos pendientes',
            'processing' => 'No hay pedidos en proceso',
            'completed' => 'No hay pedidos completados',
            'existing' => 'No hay pedidos marcados como "Ya en existencia"',
            'unavailable' => 'No hay pedidos marcados como "No disponible"'
        ];

        $message = $messages[$section] ?? 'No hay pedidos en esta categoría';

        return '<div style="padding: 2rem; text-align: center; color: #b0b0b0;">
                    <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>' . $message . '</p>
                    ' . ($section === 'new' ? '<p style="font-size: 0.9rem; margin-top: 1rem; color: #888;">Los nuevos pedidos aparecerán aquí automáticamente</p>' : '') . '
                </div>';
    }

    $html = '';

    // Agregar mensaje informativo para la sección "new"
    if ($section === 'new') {
        $html .= '<div style="background: rgba(70, 211, 71, 0.1); border: 1px solid #46d347; border-radius: 8px; padding: 1rem; margin-bottom: 1rem; font-size: 0.9rem;">
                    <strong>ℹ️ Información:</strong> Esta sección muestra solo pedidos con status "Recibido".
                    Al cambiar el status, el pedido se moverá automáticamente a la sección correspondiente.
                  </div>';
    }

    $html .= '<div class="orders-table-container">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Usuario</th>
                            <th>Título</th>
                            <th>Tipo</th>
                            <th>Estado</th>
                            <th>Fecha</th>
                            <th>TMDB</th>
                            <th>Coincidencias</th>
                        </tr>
                    </thead>
                    <tbody>';

    foreach ($orders as $order) {
        $statusClass = strtolower(str_replace(' ', '-', $order['status']));
        $isNew = $order['status'] === 'Recibido' ? 'new-order' : '';

        $html .= '<tr data-order-id="' . $order['id'] . '" class="' . $isNew . '">
                    <td>' . $order['id'] . '</td>
                    <td>' . htmlspecialchars($order['username'] ?? 'N/A') . '</td>
                    <td>' . htmlspecialchars($order['title']) . '</td>
                    <td>
                        <span style="color: var(--text-primary);">
                            <i class="fas fa-' . ($order['media_type'] === 'movie' ? 'film' : 'tv') . '"></i>
                            ' . ($order['media_type'] === 'movie' ? 'Película' : 'Serie') . '
                        </span>
                    </td>
                    <td>
                        <select class="status-select status-' . $statusClass . '" onchange="updateOrderStatus(' . $order['id'] . ', this.value)">
                            <option value="Recibido"' . ($order['status'] === 'Recibido' ? ' selected' : '') . '>📦 Recibido</option>
                            <option value="Pendiente"' . ($order['status'] === 'Pendiente' ? ' selected' : '') . '>⏳ Pendiente</option>
                            <option value="En Cola"' . ($order['status'] === 'En Cola' ? ' selected' : '') . '>🔄 En Cola</option>
                            <option value="Procesando"' . ($order['status'] === 'Procesando' ? ' selected' : '') . '>⚙️ Procesando</option>
                            <option value="Ya en existencia"' . ($order['status'] === 'Ya en existencia' ? ' selected' : '') . '>🎯 Ya en existencia</option>
                            <option value="Listo"' . ($order['status'] === 'Listo' ? ' selected' : '') . '>✅ Listo</option>
                            <option value="No disponible"' . ($order['status'] === 'No disponible' ? ' selected' : '') . '>❌ No disponible</option>
                        </select>
                    </td>
                    <td>' . date('d/m/Y H:i', strtotime($order['created_at'])) . '</td>
                    <td>
                        <a href="https://www.themoviedb.org/' . $order['media_type'] . '/' . $order['tmdb_id'] . '?language=es-MX"
                           target="_blank" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.5rem 0.75rem; background: #0d7377; color: white; text-decoration: none; border-radius: 4px; font-size: 0.8rem;">
                            <i class="fas fa-external-link-alt"></i>
                            Ver
                        </a>
                    </td>
                    <td>
                        <a href="m3u_search_tmdb.php?search=' . urlencode($order['title']) . '&auto_search=1"
                           target="_blank" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.5rem 0.75rem; background: #46d347; color: #141414; text-decoration: none; border-radius: 4px; font-size: 0.8rem;" title="Buscar coincidencias M3U para: ' . htmlspecialchars($order['title']) . '">
                            <i class="fas fa-search"></i>
                            Coincidencias
                        </a>
                    </td>
                  </tr>';
    }

    $html .= '</tbody></table></div>';

    return $html;
}
?>
