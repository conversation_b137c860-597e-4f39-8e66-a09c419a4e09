<?php
// Script para agregar tracking de notificaciones leídas por el cliente
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h1>🔔 Agregar Tracking de Notificaciones</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; } .success { color: #28a745; } .error { color: #dc3545; } .info { color: #17a2b8; } .warning { color: #ffc107; } pre { background: #333; padding: 10px; border-radius: 5px; }</style>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión a BD exitosa</p>";
    
    // Verificar estructura actual de la tabla orders
    echo "<h2>📋 Estructura Actual de la Tabla Orders:</h2>";
    $stmt = $pdo->query("DESCRIBE orders");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    foreach ($existing_columns as $column) {
        echo $column['Field'] . " - " . $column['Type'] . " - " . $column['Default'] . "\n";
    }
    echo "</pre>";
    
    // Lista de columnas que deberían existir para tracking
    $required_columns = [
        'client_notif_seen' => 'TINYINT(1) DEFAULT 0 COMMENT "Si el cliente ha visto la notificación del status"',
        'client_notif_seen_at' => 'TIMESTAMP NULL DEFAULT NULL COMMENT "Cuándo el cliente vio la notificación"'
    ];
    
    // Verificar qué columnas faltan
    $existing_column_names = array_column($existing_columns, 'Field');
    $missing_columns = [];
    
    echo "<h2>🔍 Verificación de Columnas de Tracking:</h2>";
    foreach ($required_columns as $column_name => $column_definition) {
        if (in_array($column_name, $existing_column_names)) {
            echo "<p class='success'>✅ $column_name - Existe</p>";
        } else {
            echo "<p class='error'>❌ $column_name - FALTA</p>";
            $missing_columns[$column_name] = $column_definition;
        }
    }
    
    // Si hay columnas faltantes, preguntar si agregar
    if (!empty($missing_columns)) {
        echo "<h2>⚠️ Columnas de Tracking Faltantes</h2>";
        echo "<p class='warning'>Se encontraron " . count($missing_columns) . " columnas faltantes para el tracking de notificaciones.</p>";
        
        if (isset($_POST['add_tracking_columns']) && $_POST['add_tracking_columns'] === 'yes') {
            echo "<h3>🔧 Agregando Columnas de Tracking:</h3>";
            
            foreach ($missing_columns as $column_name => $column_definition) {
                try {
                    $sql = "ALTER TABLE orders ADD COLUMN $column_name $column_definition";
                    $pdo->exec($sql);
                    echo "<p class='success'>✅ Agregada: $column_name</p>";
                } catch (PDOException $e) {
                    echo "<p class='error'>❌ Error agregando $column_name: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p class='success'>🎉 ¡Tracking de notificaciones configurado!</p>";
            echo "<p class='info'>Ahora el admin podrá ver cuándo los clientes revisan sus notificaciones.</p>";
            echo "<p><a href='admin.php' style='color: #28a745; font-weight: bold;'>🔄 Ir al Panel Admin</a></p>";
            
        } else {
            echo "<form method='POST'>";
            echo "<p>¿Quieres agregar las columnas de tracking de notificaciones?</p>";
            echo "<p class='info'>Esto permitirá al admin ver cuándo los clientes revisan el status de sus pedidos.</p>";
            echo "<button type='submit' name='add_tracking_columns' value='yes' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>✅ Sí, agregar tracking</button>";
            echo " ";
            echo "<button type='button' onclick='window.location.reload()' style='background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔄 Verificar de nuevo</button>";
            echo "</form>";
        }
    } else {
        echo "<h2>🎉 ¡Tracking de Notificaciones Completo!</h2>";
        echo "<p class='success'>Todas las columnas necesarias están presentes.</p>";
        echo "<p><a href='admin.php' style='color: #28a745; font-weight: bold;'>🔄 Ir al Panel Admin</a></p>";
    }
    
    // Mostrar algunos pedidos de ejemplo
    echo "<h2>📦 Últimos Pedidos (con tracking):</h2>";
    $stmt = $pdo->query("SELECT id, title, status, notif_seen, client_notif_seen, client_notif_seen_at FROM orders ORDER BY created_at DESC LIMIT 5");
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($orders)) {
        echo "<pre>";
        foreach ($orders as $order) {
            echo "ID: " . $order['id'] . " - " . $order['title'] . "\n";
            echo "  Status: " . $order['status'] . "\n";
            echo "  Admin visto: " . ($order['notif_seen'] ? 'Sí' : 'No') . "\n";
            if (isset($order['client_notif_seen'])) {
                echo "  Cliente visto: " . ($order['client_notif_seen'] ? 'Sí' : 'No') . "\n";
                if ($order['client_notif_seen_at']) {
                    echo "  Visto en: " . $order['client_notif_seen_at'] . "\n";
                }
            }
            echo "\n";
        }
        echo "</pre>";
    } else {
        echo "<p class='warning'>No se encontraron pedidos</p>";
    }
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='fix_users_table.php'>🔧 Arreglar tabla users</a> | <a href='admin.php'>📊 Panel Admin</a> | <a href='index.php'>🏠 Inicio</a></p>";
?>
