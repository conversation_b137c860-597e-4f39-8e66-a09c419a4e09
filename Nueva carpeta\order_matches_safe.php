<?php
// Sistema de coincidencias seguro y optimizado
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función segura para limpiar títulos
function safeTitleClean($title) {
    if (empty($title) || !is_string($title)) {
        return '';
    }
    
    $clean = trim($title);
    if (strlen($clean) < 2) {
        return '';
    }
    
    $clean = strtolower($clean);
    $clean = preg_replace('/[^\w\s]/', ' ', $clean);
    $clean = preg_replace('/\s+/', ' ', $clean);
    $clean = trim($clean);
    
    return $clean;
}

// Función para extraer nombre base de serie
function extractSeriesName($title) {
    $clean = $title;

    // Remover patrones de episodios
    $patterns = [
        '/\s*s\d+e\d+.*$/i',           // S01E01...
        '/\s*\d+x\d+.*$/i',            // 1x01...
        '/\s*season\s*\d+.*$/i',       // Season 1...
        '/\s*temporada\s*\d+.*$/i',    // Temporada 1...
        '/\s*cap[ií]tulo\s*\d+.*$/i',  // Capítulo 1...
        '/\s*episode\s*\d+.*$/i',      // Episode 1...
        '/\s*ep\s*\d+.*$/i',           // Ep 1...
        '/\s*\(\d{4}\).*$/i',          // (2023)...
        '/\s*\d{4}.*$/i'               // 2023...
    ];

    foreach ($patterns as $pattern) {
        $clean = preg_replace($pattern, '', $clean);
    }

    return trim($clean);
}

// Función segura para calcular similitud
function safeSimilarity($title1, $title2) {
    $clean1 = safeTitleClean($title1);
    $clean2 = safeTitleClean($title2);

    if (empty($clean1) || empty($clean2) || strlen($clean1) < 2 || strlen($clean2) < 2) {
        return 0;
    }

    // Coincidencia exacta
    if ($clean1 === $clean2) {
        return 100;
    }

    // Verificar contención
    if (strpos($clean1, $clean2) !== false || strpos($clean2, $clean1) !== false) {
        return 85;
    }

    // Similitud de texto
    similar_text($clean1, $clean2, $percent);
    return round($percent, 2);
}

// Función para calcular similitud de series
function seriesSimilarity($order_title, $content_title) {
    // Extraer nombres base de series
    $order_series = extractSeriesName($order_title);
    $content_series = extractSeriesName($content_title);

    return safeSimilarity($order_series, $content_series);
}

// Función para obtener pedidos válidos (solo activos)
function getValidOrders($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT o.id, o.title, o.status, o.created_at, u.username
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND o.title IS NOT NULL
            AND TRIM(o.title) != ''
            AND LENGTH(TRIM(o.title)) >= 3
            AND o.status IN ('Recibido', 'Pendiente', 'En Cola', 'Procesando')
            ORDER BY o.created_at DESC
            LIMIT 50
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

// Función para obtener contenido válido
function getValidContent($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name 
            FROM m3u_content c 
            LEFT JOIN m3u_lists l ON c.list_id = l.id 
            WHERE l.is_active = 1
            AND c.title IS NOT NULL
            AND TRIM(c.title) != ''
            AND LENGTH(TRIM(c.title)) >= 3
            ORDER BY c.title
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

// Función para generar archivo M3U de serie
function generateSeriesFile($pdo, $series_title, $list_id = null) {
    try {
        $clean_title = safeTitleClean($series_title);
        if (empty($clean_title)) {
            return null;
        }

        // Buscar episodios que coincidan con el nombre de la serie
        $where_clause = "(c.title LIKE ? OR c.title LIKE ?) AND l.is_active = 1";
        $params = ["%$series_title%", "%" . extractSeriesName($series_title) . "%"];

        if ($list_id) {
            $where_clause .= " AND c.list_id = ?";
            $params[] = $list_id;
        }

        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name
            FROM m3u_content c
            LEFT JOIN m3u_lists l ON c.list_id = l.id
            WHERE $where_clause
            ORDER BY c.title
        ");
        $stmt->execute($params);
        $all_episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Filtrar episodios que realmente pertenezcan a la serie
        $episodes = [];
        $series_base = extractSeriesName($series_title);

        foreach ($all_episodes as $episode) {
            $episode_base = extractSeriesName($episode['title']);
            if (seriesSimilarity($series_base, $episode['title']) >= 70) {
                $episodes[] = $episode;
            }
        }

        if (empty($episodes)) {
            return null;
        }

        // Generar contenido M3U
        $m3u_content = "#EXTM3U\n";
        $m3u_content .= "#EXTINF:-1,Serie Completa: " . $series_title . "\n";
        $m3u_content .= "# Generado por RogsMediaTV - " . date('Y-m-d H:i:s') . "\n";
        $m3u_content .= "# Total de episodios: " . count($episodes) . "\n";
        if ($list_id) {
            $list_name = $episodes[0]['list_name'] ?? 'Lista';
            $m3u_content .= "# Lista origen: " . $list_name . "\n";
        }
        $m3u_content .= "\n";

        foreach ($episodes as $episode) {
            $m3u_content .= "#EXTINF:-1," . $episode['title'] . "\n";
            $m3u_content .= $episode['url'] . "\n\n";
        }

        return [
            'content' => $m3u_content,
            'filename' => preg_replace('/[^a-zA-Z0-9_-]/', '_', $series_title) . '_completa.m3u',
            'episodes_count' => count($episodes)
        ];

    } catch (Exception $e) {
        return null;
    }
}

// Procesar descarga de serie
if (isset($_GET['download_series'])) {
    $series_title = $_GET['series_title'] ?? '';
    $list_id = $_GET['list_id'] ?? null;
    
    if (!empty($series_title)) {
        $series_data = generateSeriesFile($pdo, $series_title, $list_id);
        
        if ($series_data) {
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $series_data['filename'] . '"');
            header('Content-Length: ' . strlen($series_data['content']));
            echo $series_data['content'];
            exit;
        }
    }
    
    $error_message = "No se encontraron episodios para la serie: $series_title";
}

// Obtener datos
$orders = getValidOrders($pdo);
$content = getValidContent($pdo);
$matches = [];

// Buscar coincidencias agrupadas por series
foreach ($orders as $order) {
    $series_matches = [];

    foreach ($content as $item) {
        // Calcular similitud usando nombres de series
        $similarity = seriesSimilarity($order['title'], $item['title']);

        if ($similarity >= 70) {
            // Extraer nombre base de la serie del contenido
            $series_name = extractSeriesName($item['title']);
            $series_key = strtolower(trim($series_name));

            // Si ya existe esta serie, actualizar con mejor similitud
            if (isset($series_matches[$series_key])) {
                if ($similarity > $series_matches[$series_key]['similarity']) {
                    $series_matches[$series_key]['similarity'] = $similarity;
                    $series_matches[$series_key]['match_type'] = $similarity >= 95 ? 'exact' : ($similarity >= 80 ? 'high' : 'partial');
                }
                $series_matches[$series_key]['episode_count']++;

                // Agregar lista si no existe
                if (!in_array($item['list_name'], $series_matches[$series_key]['available_lists'])) {
                    $series_matches[$series_key]['available_lists'][] = $item['list_name'];
                    $series_matches[$series_key]['list_ids'][] = $item['list_id'];
                }
            } else {
                // Nueva serie encontrada
                $series_matches[$series_key] = [
                    'series_name' => $series_name ?: $item['title'],
                    'similarity' => $similarity,
                    'match_type' => $similarity >= 95 ? 'exact' : ($similarity >= 80 ? 'high' : 'partial'),
                    'media_type' => $item['media_type'],
                    'year' => $item['year'],
                    'episode_count' => 1,
                    'available_lists' => [$item['list_name']],
                    'list_ids' => [$item['list_id']],
                    'sample_content' => $item // Para referencia
                ];
            }
        }
    }

    if (!empty($series_matches)) {
        // Convertir a array y ordenar por similitud
        $order_matches = array_values($series_matches);
        usort($order_matches, function($a, $b) {
            return $b['similarity'] <=> $a['similarity'];
        });

        $matches[] = [
            'order' => $order,
            'matches' => array_slice($order_matches, 0, 5) // Máximo 5 series por pedido
        ];
    }
}

// Estadísticas
$total_orders_with_matches = count($matches);
$total_matches = array_sum(array_map(fn($m) => count($m['matches']), $matches));
$exact_matches = 0;
$high_matches = 0;

foreach ($matches as $match) {
    foreach ($match['matches'] as $m) {
        if ($m['match_type'] === 'exact') $exact_matches++;
        elseif ($m['match_type'] === 'high') $high_matches++;
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Coincidencias Encontradas - RogsMediaTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 2rem; }
        .header h1 { color: #46d347; font-size: 2rem; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .stat-card { background: #2d2d2d; padding: 1.5rem; border-radius: 10px; text-align: center; border: 1px solid #404040; }
        .stat-number { font-size: 2rem; color: #46d347; font-weight: bold; }
        .stat-label { color: #b0b0b0; }
        .match-card { background: #2d2d2d; padding: 1.5rem; margin-bottom: 1.5rem; border-radius: 10px; border: 1px solid #404040; }
        .order-title { color: #46d347; font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem; }
        .order-meta { color: #b0b0b0; margin-bottom: 1rem; }
        .matches-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 1rem; }
        .match-item { background: #1a1a1a; padding: 1rem; border-radius: 8px; border: 1px solid #404040; }
        .match-title { font-weight: bold; margin-bottom: 0.5rem; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; }
        .similarity { padding: 0.2rem 0.6rem; border-radius: 12px; font-size: 0.8rem; font-weight: bold; white-space: nowrap; }
        .similarity-exact { background: rgba(40, 167, 69, 0.2); color: #28a745; }
        .similarity-high { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .similarity-partial { background: rgba(23, 162, 184, 0.2); color: #17a2b8; }
        .btn { padding: 0.5rem 1rem; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; font-size: 0.8rem; margin: 0.2rem; display: inline-block; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #1a1a1a; }
        .btn-info { background: #17a2b8; color: white; }
        .back-link { color: #46d347; text-decoration: none; margin-bottom: 2rem; display: inline-block; }
        .no-matches { text-align: center; padding: 3rem; color: #b0b0b0; }
        .error { background: rgba(220, 53, 69, 0.2); color: #dc3545; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Panel Admin</a>
        
        <div class="header">
            <h1>🎯 Coincidencias Encontradas</h1>
            <p>Pedidos activos que coinciden con contenido disponible en tus listas M3U</p>
            <div style="background: rgba(70, 211, 71, 0.1); border: 1px solid #46d347; border-radius: 8px; padding: 1rem; margin: 1rem 0; font-size: 0.9rem;">
                <strong>ℹ️ Información:</strong> Solo se analizan pedidos con estado:
                <span style="color: #46d347;">Recibido, Pendiente, En Cola, Procesando</span>
                <br>Los pedidos "Listo", "Ya en existencia" y "No disponible" no se incluyen en el análisis.
            </div>
        </div>

        <?php if (isset($error_message)): ?>
        <div class="error"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_orders_with_matches; ?></div>
                <div class="stat-label">Pedidos con Coincidencias</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_matches; ?></div>
                <div class="stat-label">Total Coincidencias</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $exact_matches; ?></div>
                <div class="stat-label">Exactas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $high_matches; ?></div>
                <div class="stat-label">Altas</div>
            </div>
        </div>

        <?php if (empty($matches)): ?>
        <div class="no-matches">
            <h3>No se encontraron coincidencias</h3>
            <p>No hay pedidos recientes que coincidan con el contenido M3U disponible</p>
            <a href="m3u_search.php" class="btn btn-info">Buscar Manualmente</a>
        </div>
        <?php else: ?>
        <?php foreach ($matches as $match): ?>
        <div class="match-card">
            <div class="order-title"><?php echo htmlspecialchars($match['order']['title']); ?></div>
            <div class="order-meta">
                👤 <?php echo htmlspecialchars($match['order']['username'] ?? 'Usuario'); ?> | 
                📅 <?php echo date('d/m/Y', strtotime($match['order']['created_at'])); ?> | 
                📊 <?php echo ucfirst($match['order']['status']); ?> | 
                🎯 <?php echo count($match['matches']); ?> coincidencia(s)
            </div>

            <div class="matches-grid">
                <?php foreach ($match['matches'] as $m): ?>
                <div class="match-item">
                    <div class="match-title">
                        <?php echo htmlspecialchars($m['series_name']); ?>
                        <span class="similarity similarity-<?php echo $m['match_type']; ?>">
                            <?php echo $m['similarity']; ?>%
                        </span>
                    </div>

                    <div style="margin: 0.5rem 0; font-size: 0.8rem; color: #b0b0b0;">
                        📺 <?php echo $m['episode_count']; ?> episodio(s) |
                        <?php echo $m['media_type'] === 'movie' ? '🎬 Película' : ($m['media_type'] === 'tv' ? '📺 Serie' : '❓ Desconocido'); ?>
                        <?php if ($m['year']): ?> | 📅 <?php echo $m['year']; ?><?php endif; ?>
                    </div>

                    <div style="margin: 0.5rem 0; font-size: 0.8rem; color: #46d347;">
                        📡 Disponible en: <?php echo implode(', ', array_unique($m['available_lists'])); ?>
                    </div>

                    <div>
                        <?php if ($m['media_type'] === 'tv' || $m['episode_count'] > 1): ?>
                        <a href="?download_series=1&series_title=<?php echo urlencode($m['series_name']); ?>&list_id=<?php echo $m['list_ids'][0]; ?>"
                           class="btn btn-warning">
                            📁 Serie Completa (<?php echo $m['episode_count']; ?> eps)
                        </a>
                        <?php endif; ?>

                        <a href="m3u_search.php?q=<?php echo urlencode($m['series_name']); ?>" class="btn btn-info">
                            🔍 Ver Episodios
                        </a>

                        <?php if (count($m['available_lists']) > 1): ?>
                        <div style="margin-top: 0.5rem;">
                            <small style="color: #b0b0b0;">Otras listas:</small>
                            <?php for ($i = 1; $i < count($m['available_lists']); $i++): ?>
                            <a href="?download_series=1&series_title=<?php echo urlencode($m['series_name']); ?>&list_id=<?php echo $m['list_ids'][$i]; ?>"
                               class="btn" style="background: #6c757d; color: white; font-size: 0.7rem; padding: 0.3rem 0.6rem;">
                                📁 <?php echo htmlspecialchars($m['available_lists'][$i]); ?>
                            </a>
                            <?php endfor; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <script>
        function copyUrl(url) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showMessage('✅ URL copiada');
                }).catch(() => {
                    fallbackCopy(url);
                });
            } else {
                fallbackCopy(url);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                showMessage('✅ URL copiada');
            } catch (err) {
                showMessage('❌ Error al copiar');
            }
            
            document.body.removeChild(textArea);
        }

        function showMessage(message) {
            const div = document.createElement('div');
            div.style.cssText = 'position:fixed;top:20px;right:20px;background:#28a745;color:white;padding:1rem;border-radius:8px;z-index:1000;';
            div.textContent = message;
            document.body.appendChild(div);
            
            setTimeout(() => {
                document.body.removeChild(div);
            }, 3000);
        }
    </script>
</body>
</html>
