<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>📱 Creando Aplicaciones de Muestra</h1>";

try {
    // 1. Crear directorio apploader si no existe
    echo "<h2>📁 Configurando directorio...</h2>";
    
    $apploader_dir = __DIR__ . '/apploader';
    if (!is_dir($apploader_dir)) {
        if (mkdir($apploader_dir, 0755, true)) {
            $success_messages[] = "✅ Directorio apploader creado";
        } else {
            $error_messages[] = "❌ Error creando directorio apploader";
        }
    } else {
        $success_messages[] = "✅ Directorio apploader ya existe";
    }
    
    // 2. Crear archivos de muestra (archivos vacíos para demo)
    echo "<h2>📱 Creando archivos de muestra...</h2>";
    
    $sample_files = [
        'IPTV_Smarters_Pro_v3.0.9.apk' => 'android',
        'TiviMate_IPTV_Player_v4.6.0.apk' => 'android',
        'GSE_Smart_IPTV_v7.6.ipa' => 'ios',
        'VLC_Media_Player_v3.0.18.exe' => 'windows',
        'IPTV_Player_macOS_v2.1.dmg' => 'macos'
    ];
    
    foreach ($sample_files as $filename => $platform) {
        $file_path = $apploader_dir . '/' . $filename;
        
        if (!file_exists($file_path)) {
            // Crear archivo de muestra con contenido mínimo
            $content = "# Archivo de muestra para $platform\n";
            $content .= "# Este es un archivo de demostración\n";
            $content .= "# Fecha de creación: " . date('Y-m-d H:i:s') . "\n";
            $content .= str_repeat("SAMPLE_DATA_", 1000); // Hacer el archivo más grande
            
            if (file_put_contents($file_path, $content)) {
                $success_messages[] = "✅ Creado: $filename (" . formatBytes(strlen($content)) . ")";
            } else {
                $error_messages[] = "❌ Error creando: $filename";
            }
        } else {
            $success_messages[] = "ℹ️ Ya existe: $filename";
        }
    }
    
    // 3. Insertar aplicaciones en la base de datos
    echo "<h2>🗄️ Insertando en base de datos...</h2>";
    
    try {
        // Verificar si la tabla existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'support_apps'");
        if ($stmt->rowCount() == 0) {
            // Crear tabla si no existe
            $pdo->exec("
                CREATE TABLE support_apps (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    version VARCHAR(50) NOT NULL,
                    platform ENUM('android', 'ios', 'windows', 'macos', 'linux', 'universal') NOT NULL,
                    file_path VARCHAR(500),
                    file_size BIGINT DEFAULT 0,
                    features TEXT,
                    download_url VARCHAR(500),
                    external_url VARCHAR(500),
                    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
                    download_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            $success_messages[] = "✅ Tabla support_apps creada";
        }
        
        // Verificar si ya hay aplicaciones
        $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps");
        $app_count = $stmt->fetchColumn();
        
        if ($app_count == 0) {
            $sample_apps = [
                [
                    'name' => 'IPTV Smarters Pro',
                    'description' => 'Aplicación profesional para ver IPTV con interfaz moderna y funciones avanzadas. Compatible con listas M3U y códigos Xtream.',
                    'version' => '3.0.9',
                    'platform' => 'android',
                    'file_path' => 'apploader/IPTV_Smarters_Pro_v3.0.9.apk',
                    'file_size' => filesize($apploader_dir . '/IPTV_Smarters_Pro_v3.0.9.apk'),
                    'features' => 'EPG, Catch-up TV, Series, VOD, Multi-idioma, Chromecast',
                    'download_url' => 'apploader/IPTV_Smarters_Pro_v3.0.9.apk',
                    'external_url' => 'https://play.google.com/store/apps/details?id=com.nst.iptvsmartersPro',
                    'status' => 'active'
                ],
                [
                    'name' => 'TiviMate IPTV Player',
                    'description' => 'Reproductor IPTV premium con diseño elegante y funciones profesionales. Ideal para Android TV.',
                    'version' => '4.6.0',
                    'platform' => 'android',
                    'file_path' => 'apploader/TiviMate_IPTV_Player_v4.6.0.apk',
                    'file_size' => filesize($apploader_dir . '/TiviMate_IPTV_Player_v4.6.0.apk'),
                    'features' => 'EPG avanzado, Grabación, Múltiples listas, Favoritos, Android TV',
                    'download_url' => 'apploader/TiviMate_IPTV_Player_v4.6.0.apk',
                    'external_url' => 'https://play.google.com/store/apps/details?id=ar.tvplayer.tv',
                    'status' => 'active'
                ],
                [
                    'name' => 'GSE Smart IPTV',
                    'description' => 'Reproductor IPTV gratuito para iOS con soporte para múltiples formatos y funciones avanzadas.',
                    'version' => '7.6',
                    'platform' => 'ios',
                    'file_path' => 'apploader/GSE_Smart_IPTV_v7.6.ipa',
                    'file_size' => filesize($apploader_dir . '/GSE_Smart_IPTV_v7.6.ipa'),
                    'features' => 'Chromecast, AirPlay, EPG, Subtítulos, Control parental',
                    'download_url' => 'apploader/GSE_Smart_IPTV_v7.6.ipa',
                    'external_url' => 'https://apps.apple.com/app/gse-smart-iptv/id1028734023',
                    'status' => 'active'
                ],
                [
                    'name' => 'VLC Media Player',
                    'description' => 'Reproductor multimedia universal compatible con IPTV y todos los formatos de video.',
                    'version' => '3.0.18',
                    'platform' => 'windows',
                    'file_path' => 'apploader/VLC_Media_Player_v3.0.18.exe',
                    'file_size' => filesize($apploader_dir . '/VLC_Media_Player_v3.0.18.exe'),
                    'features' => 'Multiplataforma, Códecs integrados, Streaming, Subtítulos',
                    'download_url' => 'apploader/VLC_Media_Player_v3.0.18.exe',
                    'external_url' => 'https://www.videolan.org/vlc/',
                    'status' => 'active'
                ],
                [
                    'name' => 'IPTV Player macOS',
                    'description' => 'Reproductor IPTV nativo para macOS con interfaz optimizada para Mac.',
                    'version' => '2.1',
                    'platform' => 'macos',
                    'file_path' => 'apploader/IPTV_Player_macOS_v2.1.dmg',
                    'file_size' => filesize($apploader_dir . '/IPTV_Player_macOS_v2.1.dmg'),
                    'features' => 'Interfaz nativa macOS, EPG, Favoritos, Pantalla completa',
                    'download_url' => 'apploader/IPTV_Player_macOS_v2.1.dmg',
                    'external_url' => 'https://apps.apple.com/app/iptv-player/id1234567890',
                    'status' => 'active'
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO support_apps (name, description, version, platform, file_path, file_size, features, download_url, external_url, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $inserted = 0;
            foreach ($sample_apps as $app) {
                try {
                    $stmt->execute([
                        $app['name'],
                        $app['description'],
                        $app['version'],
                        $app['platform'],
                        $app['file_path'],
                        $app['file_size'],
                        $app['features'],
                        $app['download_url'],
                        $app['external_url'],
                        $app['status']
                    ]);
                    $inserted++;
                    $success_messages[] = "✅ Insertada app: {$app['name']}";
                } catch (Exception $e) {
                    $error_messages[] = "❌ Error insertando {$app['name']}: " . $e->getMessage();
                }
            }
            
            $success_messages[] = "✅ Total de aplicaciones insertadas: $inserted";
        } else {
            $success_messages[] = "ℹ️ Ya existen $app_count aplicaciones en la base de datos";
        }
        
    } catch (Exception $e) {
        $error_messages[] = "❌ Error con base de datos: " . $e->getMessage();
    }
    
    // 4. Crear archivo .htaccess para descargas
    echo "<h2>🔒 Configurando descargas...</h2>";
    
    $htaccess_content = '# Configuración para descargas de aplicaciones
<Files "*.apk">
    Header set Content-Type "application/vnd.android.package-archive"
    Header set Content-Disposition "attachment"
</Files>

<Files "*.ipa">
    Header set Content-Type "application/octet-stream"
    Header set Content-Disposition "attachment"
</Files>

<Files "*.exe">
    Header set Content-Type "application/octet-stream"
    Header set Content-Disposition "attachment"
</Files>

<Files "*.dmg">
    Header set Content-Type "application/octet-stream"
    Header set Content-Disposition "attachment"
</Files>

# Permitir acceso a archivos de aplicaciones
Options -Indexes
';
    
    $htaccess_file = $apploader_dir . '/.htaccess';
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        $success_messages[] = "✅ Archivo .htaccess creado para descargas";
    } else {
        $error_messages[] = "❌ Error creando .htaccess";
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

function formatBytes($size, $precision = 2) {
    if ($size == 0) return '0 B';
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Crear Apps de Muestra - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Aplicaciones de Muestra Creadas</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Configuración Exitosa</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="user_apps.php" class="btn">📱 Ver Apps Usuario</a>
            <a href="debug_user_apps.php" class="btn">🔧 Debug Apps</a>
            <a href="apps_admin.php" class="btn">⚙️ Apps Admin</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
        
        <?php if (count($error_messages) == 0): ?>
        <div style="background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; border-radius: 12px; padding: 2rem; margin: 2rem 0; text-align: center;">
            <h2 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Apps de Muestra Creadas!</h2>
            <p style="color: #10b981; font-size: 1.1rem;">
                Ahora user_apps.php debería mostrar las aplicaciones con botones de descarga funcionales.
            </p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
