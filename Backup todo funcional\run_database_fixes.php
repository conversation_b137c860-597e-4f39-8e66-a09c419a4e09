<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

try {
    // 1. Corregir tabla support_apps
    echo "<h2>🔧 Corrigiendo estructura de support_apps...</h2>";
    
    // Agregar columnas faltantes si no existen
    $columns_to_add = [
        'features' => "ALTER TABLE support_apps ADD COLUMN features TEXT AFTER description",
        'download_url' => "ALTER TABLE support_apps ADD COLUMN download_url VARCHAR(500) AFTER file_size",
        'external_url' => "ALTER TABLE support_apps ADD COLUMN external_url VARCHAR(500) AFTER download_url",
        'status' => "ALTER TABLE support_apps ADD COLUMN status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active' AFTER is_active"
    ];
    
    foreach ($columns_to_add as $column => $sql) {
        try {
            // Verificar si la columna existe
            $stmt = $pdo->query("SHOW COLUMNS FROM support_apps LIKE '$column'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec($sql);
                $success_messages[] = "✅ Columna '$column' agregada a support_apps";
            } else {
                $success_messages[] = "ℹ️ Columna '$column' ya existe en support_apps";
            }
        } catch (Exception $e) {
            $error_messages[] = "❌ Error agregando columna '$column': " . $e->getMessage();
        }
    }
    
    // 2. Crear tabla admin_notifications
    echo "<h2>🔔 Creando tabla de notificaciones...</h2>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admin_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type ENUM('ticket', 'chat', 'channel_request', 'app_download', 'system') NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                reference_id INT NULL,
                reference_type VARCHAR(50) NULL,
                is_read BOOLEAN DEFAULT FALSE,
                admin_id INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_type (type),
                INDEX idx_is_read (is_read),
                INDEX idx_admin_id (admin_id),
                INDEX idx_created_at (created_at)
            )
        ");
        $success_messages[] = "✅ Tabla admin_notifications creada/verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando admin_notifications: " . $e->getMessage();
    }
    
    // 3. Crear tabla chat_status
    echo "<h2>💬 Creando tabla de estado de chat...</h2>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS chat_status (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                admin_online BOOLEAN DEFAULT FALSE,
                user_online BOOLEAN DEFAULT FALSE,
                last_admin_activity TIMESTAMP NULL,
                last_user_activity TIMESTAMP NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_session (session_id)
            )
        ");
        $success_messages[] = "✅ Tabla chat_status creada/verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando chat_status: " . $e->getMessage();
    }
    
    // 4. Crear tabla activation_codes si no existe
    echo "<h2>🔑 Creando tabla de códigos de activación...</h2>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activation_codes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(100) UNIQUE NOT NULL,
                list_name VARCHAR(255),
                list_url VARCHAR(500),
                duration_days INT DEFAULT 30,
                max_uses INT DEFAULT 1,
                current_uses INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                INDEX idx_code (code),
                INDEX idx_is_active (is_active)
            )
        ");
        $success_messages[] = "✅ Tabla activation_codes creada/verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando activation_codes: " . $e->getMessage();
    }
    
    // 5. Insertar datos de ejemplo
    echo "<h2>📊 Insertando datos de ejemplo...</h2>";
    
    // Verificar y corregir tabla users
    try {
        // Verificar si la tabla users existe y tiene las columnas correctas
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password_hash'");
        if ($stmt->rowCount() == 0) {
            // La columna password_hash no existe, agregarla
            $pdo->exec("ALTER TABLE users ADD COLUMN password_hash VARCHAR(255) AFTER email");
            $success_messages[] = "✅ Columna password_hash agregada a users";
        }

        // Insertar usuarios de ejemplo
        $stmt = $pdo->prepare("INSERT IGNORE INTO users (id, username, email, password_hash) VALUES (?, ?, ?, ?)");
        $stmt->execute([1, 'usuario', '<EMAIL>', 'demo_hash']);
        $stmt->execute([2, 'admin', '<EMAIL>', 'admin_hash']);
        $success_messages[] = "✅ Usuarios de ejemplo insertados";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error con usuarios: " . $e->getMessage();
    }
    
    // Aplicaciones de ejemplo
    try {
        $apps_data = [
            ['IPTV Player Android', 'Reproductor IPTV optimizado para Android con soporte para múltiples formatos', '2.1.0', 'android', 'uploads/apps/iptv-player-android-2.1.0.apk', 15728640, 'EPG, Grabación, Favoritos, Control parental', 'https://play.google.com/store/apps/details?id=com.rgs.iptv', 'https://rgs.com/android', 'active'],
            ['IPTV Player iOS', 'Reproductor IPTV para dispositivos iOS con interfaz intuitiva', '2.0.5', 'ios', 'uploads/apps/iptv-player-ios-2.0.5.ipa', 18874368, 'AirPlay, Chromecast, Favoritos', 'https://apps.apple.com/app/rgs-iptv/id123456789', 'https://rgs.com/ios', 'active'],
            ['IPTV Desktop', 'Cliente de escritorio para Windows y Mac con funciones avanzadas', '1.5.2', 'windows', 'uploads/apps/iptv-desktop-1.5.2.exe', 45678912, 'Múltiples ventanas, Grabación HD, Timeshift', 'https://rgs.com/downloads/desktop', 'https://rgs.com/desktop', 'active']
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO support_apps (name, description, version, platform, file_path, file_size, features, download_url, external_url, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($apps_data as $app) {
            $stmt->execute($app);
        }
        $success_messages[] = "✅ Aplicaciones de ejemplo insertadas";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error insertando aplicaciones: " . $e->getMessage();
    }
    
    // Verificar y corregir tabla support_tickets
    try {
        // Verificar si existe la columna 'title' o 'subject'
        $stmt = $pdo->query("SHOW COLUMNS FROM support_tickets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $has_title = in_array('title', $columns);
        $has_subject = in_array('subject', $columns);

        if (!$has_title && !$has_subject) {
            // Agregar columna title
            $pdo->exec("ALTER TABLE support_tickets ADD COLUMN title VARCHAR(255) NOT NULL AFTER user_id");
            $success_messages[] = "✅ Columna title agregada a support_tickets";
        }

        // Insertar tickets de ejemplo usando la columna correcta
        $title_column = $has_title ? 'title' : ($has_subject ? 'subject' : 'title');

        $stmt = $pdo->prepare("INSERT IGNORE INTO support_tickets (id, user_id, $title_column, description, priority, category, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([6, 1, 'Problema con la reproducción', 'Los canales se cortan constantemente durante la reproducción', 'high', 'technical', 'open']);
        $stmt->execute([7, 1, 'Solicitud de canal', 'Me gustaría que agreguen CNN en español a la lista', 'medium', 'general', 'in_progress']);
        $stmt->execute([8, 1, 'Error en la aplicación', 'La app se cierra al abrir ciertos canales HD', 'urgent', 'bug_report', 'resolved']);

        // Respuestas para el ticket 6
        $stmt = $pdo->prepare("INSERT IGNORE INTO ticket_responses (ticket_id, user_id, message, is_admin_response) VALUES (?, ?, ?, ?)");
        $stmt->execute([6, 1, 'Hola, tengo problemas con la reproducción de los canales. Se cortan cada pocos minutos y tengo que reiniciar la aplicación.', 0]);
        $stmt->execute([6, 2, 'Hola, gracias por contactarnos. ¿Podrías decirnos qué dispositivo estás usando y qué velocidad de internet tienes?', 1]);

        $success_messages[] = "✅ Tickets de ejemplo insertados";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error con tickets: " . $e->getMessage();
    }
    
    // Verificar y crear notificaciones de ejemplo
    try {
        // Verificar si la tabla admin_notifications existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
        if ($stmt->rowCount() > 0) {
            // Verificar si tiene las columnas necesarias
            $stmt = $pdo->query("SHOW COLUMNS FROM admin_notifications");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (in_array('reference_id', $columns) && in_array('reference_type', $columns)) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute(['ticket', 'Nuevo Ticket #6', 'Usuario reporta problemas de reproducción', 6, 'support_tickets']);
                $stmt->execute(['chat', 'Nueva sesión de chat', 'Usuario solicita ayuda en chat en vivo', 1, 'chat_sessions']);
                $success_messages[] = "✅ Notificaciones de ejemplo insertadas";
            } else {
                $success_messages[] = "ℹ️ Tabla admin_notifications existe pero faltan columnas";
            }
        } else {
            $success_messages[] = "ℹ️ Tabla admin_notifications no existe, se creará automáticamente";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error con notificaciones: " . $e->getMessage();
    }
    
    // 6. Verificar integridad
    echo "<h2>🔍 Verificando integridad de la base de datos...</h2>";
    
    $tables_to_check = ['support_tickets', 'ticket_responses', 'chat_sessions', 'chat_messages', 'support_apps', 'app_downloads', 'admin_notifications', 'chat_status', 'users'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $success_messages[] = "✅ Tabla '$table': $count registros";
        } catch (Exception $e) {
            $error_messages[] = "❌ Error verificando tabla '$table': " . $e->getMessage();
        }
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Reparación de Base de Datos - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Reparación de Base de Datos Completada</h1>
        
        <div class="summary">
            <h3>📊 Resumen de la Operación</h3>
            <p><strong>✅ Operaciones exitosas:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores encontrados:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Operaciones Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="admin2.php" class="btn">🏠 Ir al Panel de Admin</a>
            <a href="apps_admin.php" class="btn">📱 Gestionar Apps</a>
            <a href="ticket_detail.php?id=6" class="btn">🎫 Ver Ticket de Prueba</a>
        </div>
    </div>
</body>
</html>
