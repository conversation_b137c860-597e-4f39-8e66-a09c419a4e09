<?php
// Script para actualizar la lista TVPROTECH con la configuración correcta
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

echo "<h1>🔧 Actualizar Lista TVPROTECH</h1>";
echo "<style>body{font-family:Arial;margin:20px;background:#141414;color:white;} .success{color:#28a745;background:rgba(40,167,69,0.2);padding:15px;border-radius:8px;margin:15px 0;} .error{color:#dc3545;background:rgba(220,53,69,0.2);padding:15px;border-radius:8px;margin:15px 0;} .info{color:#17a2b8;background:rgba(23,162,184,0.2);padding:15px;border-radius:8px;margin:15px 0;} .section{background:#2d2d2d;padding:20px;border-radius:10px;margin:20px 0;border:1px solid #404040;} .btn{padding:10px 20px;border:none;border-radius:5px;cursor:pointer;margin:5px;text-decoration:none;display:inline-block;} .btn-primary{background:#46d347;color:#1a1a1a;} .btn-info{background:#17a2b8;color:white;}</style>";

// Datos de configuración correctos
$server_url = 'http://tvpro.tech:8080';
$username = 'infest';
$password = 'asdf45223';
$correct_m3u_url = 'http://tvpro.tech:8080/get.php?username=infest&password=asdf45223&type=m3u_plus';

echo "<div class='info'>";
echo "<h2>📋 Configuración Detectada</h2>";
echo "<p><strong>Servidor:</strong> $server_url</p>";
echo "<p><strong>Usuario:</strong> $username</p>";
echo "<p><strong>Contraseña:</strong> $password</p>";
echo "<p><strong>URL M3U:</strong> $correct_m3u_url</p>";
echo "</div>";

// Buscar la lista TVPROTECH
$stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE name LIKE '%TVPROTECH%' OR url LIKE '%tvpro.tech%'");
$stmt->execute();
$existing_list = $stmt->fetch(PDO::FETCH_ASSOC);

if ($existing_list) {
    echo "<div class='section'>";
    echo "<h2>🔍 Lista Encontrada</h2>";
    echo "<p><strong>ID:</strong> " . $existing_list['id'] . "</p>";
    echo "<p><strong>Nombre:</strong> " . htmlspecialchars($existing_list['name']) . "</p>";
    echo "<p><strong>URL actual:</strong> " . htmlspecialchars($existing_list['url']) . "</p>";
    echo "<p><strong>Usuario actual:</strong> " . htmlspecialchars($existing_list['username'] ?? 'N/A') . "</p>";
    echo "</div>";
    
    if (isset($_POST['update_list'])) {
        try {
            // Actualizar la lista con la configuración correcta
            $stmt = $pdo->prepare("
                UPDATE m3u_lists 
                SET url = ?, 
                    server_url = ?, 
                    username = ?, 
                    password = ?, 
                    list_type = 'xtream_codes',
                    last_updated = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([
                $correct_m3u_url,
                $server_url,
                $username,
                $password,
                $existing_list['id']
            ]);
            
            echo "<div class='success'>";
            echo "<h2>✅ Lista Actualizada Exitosamente</h2>";
            echo "<p>La lista TVPROTECH ha sido configurada correctamente como Xtream Codes.</p>";
            echo "<p><strong>Nueva URL M3U:</strong> $correct_m3u_url</p>";
            echo "</div>";
            
            echo "<div class='section'>";
            echo "<h2>🚀 Próximos Pasos</h2>";
            echo "<p>1. <a href='test_m3u_access.php' class='btn btn-info'>🧪 Probar Acceso</a> - Verificar que la URL funciona</p>";
            echo "<p>2. <a href='m3u_analyzer_debug.php?list_id=" . $existing_list['id'] . "' class='btn btn-primary'>🔍 Analizar Lista</a> - Extraer contenido</p>";
            echo "<p>3. <a href='m3u_downloader.php' class='btn btn-info'>📥 Descargar Lista</a> - Guardar localmente</p>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>";
            echo "<h2>❌ Error al Actualizar</h2>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    } else {
        // Mostrar botón para confirmar actualización
        echo "<div class='section'>";
        echo "<h2>⚠️ Confirmar Actualización</h2>";
        echo "<p>¿Quieres actualizar la lista con la configuración correcta de Xtream Codes?</p>";
        echo "<p><strong>Cambios que se realizarán:</strong></p>";
        echo "<ul>";
        echo "<li>URL: " . htmlspecialchars($existing_list['url']) . " → $correct_m3u_url</li>";
        echo "<li>Tipo: direct_m3u → xtream_codes</li>";
        echo "<li>Servidor: → $server_url</li>";
        echo "<li>Usuario: → $username</li>";
        echo "<li>Contraseña: → $password</li>";
        echo "</ul>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='update_list' class='btn btn-primary'>✅ Sí, Actualizar Lista</button>";
        echo "<a href='m3u_manager.php' class='btn' style='background:#666;color:white;'>❌ Cancelar</a>";
        echo "</form>";
        echo "</div>";
    }
    
} else {
    echo "<div class='error'>";
    echo "<h2>❌ Lista No Encontrada</h2>";
    echo "<p>No se encontró la lista TVPROTECH en la base de datos.</p>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>➕ Crear Nueva Lista</h2>";
    echo "<p>¿Quieres crear una nueva lista con la configuración correcta?</p>";
    
    if (isset($_POST['create_list'])) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO m3u_lists (name, url, server_url, username, password, list_type) 
                VALUES (?, ?, ?, ?, ?, 'xtream_codes')
            ");
            
            $stmt->execute([
                'TVPROTECH',
                $correct_m3u_url,
                $server_url,
                $username,
                $password
            ]);
            
            $new_list_id = $pdo->lastInsertId();
            
            // Crear carpeta
            $folderName = 'TVPROTECH_' . $new_list_id;
            $folderPath = 'uploads/m3u_files/' . $folderName;
            if (!file_exists($folderPath)) {
                mkdir($folderPath, 0755, true);
            }
            
            // Actualizar con nombre de carpeta
            $stmt = $pdo->prepare("UPDATE m3u_lists SET folder_name = ? WHERE id = ?");
            $stmt->execute([$folderName, $new_list_id]);
            
            echo "<div class='success'>";
            echo "<h2>✅ Lista Creada Exitosamente</h2>";
            echo "<p>Nueva lista TVPROTECH creada con ID: $new_list_id</p>";
            echo "<p>Carpeta creada: $folderName</p>";
            echo "</div>";
            
            echo "<div class='section'>";
            echo "<h2>🚀 Próximos Pasos</h2>";
            echo "<p>1. <a href='test_m3u_access.php' class='btn btn-info'>🧪 Probar Acceso</a></p>";
            echo "<p>2. <a href='m3u_analyzer_debug.php?list_id=$new_list_id' class='btn btn-primary'>🔍 Analizar Lista</a></p>";
            echo "<p>3. <a href='m3u_downloader.php' class='btn btn-info'>📥 Descargar Lista</a></p>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>";
            echo "<h2>❌ Error al Crear Lista</h2>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='create_list' class='btn btn-primary'>✅ Crear Lista TVPROTECH</button>";
        echo "<a href='m3u_manager.php' class='btn' style='background:#666;color:white;'>❌ Cancelar</a>";
        echo "</form>";
        echo "</div>";
    }
}

// Función para probar la URL
function testM3uUrl($url) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $content = @file_get_contents($url, false, $context);
    
    return [
        'success' => $content !== false,
        'size' => $content ? strlen($content) : 0,
        'is_m3u' => $content ? (strpos($content, '#EXTM3U') !== false || strpos($content, '#EXTINF') !== false) : false,
        'preview' => $content ? substr($content, 0, 200) : null
    ];
}

echo "<div class='section'>";
echo "<h2>🧪 Test Rápido de la URL</h2>";
echo "<p>Probando acceso a: $correct_m3u_url</p>";

$test_result = testM3uUrl($correct_m3u_url);

if ($test_result['success']) {
    echo "<div class='success'>";
    echo "<p>✅ URL accesible</p>";
    echo "<p>📊 Tamaño: " . number_format($test_result['size']) . " bytes</p>";
    echo "<p>📄 Es M3U válido: " . ($test_result['is_m3u'] ? '✅ Sí' : '❌ No') . "</p>";
    if ($test_result['preview']) {
        echo "<p><strong>Vista previa:</strong></p>";
        echo "<pre style='background:#1a1a1a;padding:10px;border-radius:5px;font-size:12px;'>" . htmlspecialchars($test_result['preview']) . "...</pre>";
    }
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<p>❌ No se pudo acceder a la URL</p>";
    echo "<p>Verifica que las credenciales sean correctas</p>";
    echo "</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🛠️ Herramientas</h2>";
echo "<p><a href='m3u_manager.php' class='btn btn-info'>📡 Gestor M3U</a></p>";
echo "<p><a href='test_m3u_access.php' class='btn btn-info'>🧪 Test de Acceso</a></p>";
echo "<p><a href='admin.php' class='btn btn-info'>🏠 Panel Admin</a></p>";
echo "</div>";
?>
