<?php
/**
 * Sistema de Notificaciones en Tiempo Real
 * Usando Server-Sent Events (SSE) para comunicación entre admin.php y admin2.php
 */

session_start();
require_once 'config.php';

// Verificar que sea un administrador
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    exit('Access denied');
}

// Configurar headers para SSE
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Función para enviar evento SSE
function sendSSE($id, $event, $data) {
    echo "id: $id\n";
    echo "event: $event\n";
    echo "data: " . json_encode($data) . "\n\n";
    ob_flush();
    flush();
}

// Función para obtener notificaciones pendientes
function getNotifications($pdo, $last_check = null) {
    try {
        // Crear tabla de notificaciones si no existe
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admin_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type VARCHAR(50) NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                reference_id INT,
                reference_type VARCHAR(50),
                source_admin VARCHAR(50) DEFAULT 'system',
                target_admin VARCHAR(50) DEFAULT 'all',
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP NULL,
                INDEX idx_type (type),
                INDEX idx_read (is_read),
                INDEX idx_created (created_at),
                INDEX idx_target (target_admin)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        $where_clause = "WHERE is_read = FALSE";
        $params = [];
        
        if ($last_check) {
            $where_clause .= " AND created_at > ?";
            $params[] = $last_check;
        }
        
        $stmt = $pdo->prepare("
            SELECT * FROM admin_notifications 
            $where_clause
            ORDER BY 
                CASE priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'normal' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                created_at DESC
            LIMIT 50
        ");
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        return [];
    }
}

// Función para obtener estadísticas rápidas
function getQuickStats($pdo) {
    try {
        $stats = [];
        
        // Tickets pendientes
        $stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
        $stats['pending_tickets'] = $stmt->fetchColumn();
        
        // Chats activos
        $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) FROM chat_messages WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stats['active_chats'] = $stmt->fetchColumn();
        
        // Solicitudes de canales
        $stmt = $pdo->query("SELECT COUNT(*) FROM channel_requests WHERE status = 'pending'");
        $stats['pending_channels'] = $stmt->fetchColumn();
        
        // Nuevos mensajes de chat
        $stmt = $pdo->query("SELECT COUNT(*) FROM chat_messages WHERE is_admin = 0 AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTES)");
        $stats['new_chat_messages'] = $stmt->fetchColumn();
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error getting stats: " . $e->getMessage());
        return [];
    }
}

// Obtener último timestamp de verificación
$last_check = $_GET['last_check'] ?? null;
$admin_panel = $_GET['panel'] ?? 'admin'; // admin o admin2

// Loop principal de SSE
$start_time = time();
$max_execution_time = 300; // 5 minutos máximo

while (time() - $start_time < $max_execution_time) {
    // Verificar si la conexión sigue activa
    if (connection_aborted()) {
        break;
    }
    
    // Obtener notificaciones nuevas
    $notifications = getNotifications($pdo, $last_check);
    
    if (!empty($notifications)) {
        foreach ($notifications as $notification) {
            sendSSE(
                $notification['id'],
                'notification',
                [
                    'id' => $notification['id'],
                    'type' => $notification['type'],
                    'title' => $notification['title'],
                    'message' => $notification['message'],
                    'priority' => $notification['priority'],
                    'reference_id' => $notification['reference_id'],
                    'reference_type' => $notification['reference_type'],
                    'created_at' => $notification['created_at'],
                    'timestamp' => time()
                ]
            );
        }
        
        // Actualizar último timestamp
        $last_check = date('Y-m-d H:i:s');
    }
    
    // Enviar estadísticas cada 30 segundos
    if (time() % 30 == 0) {
        $stats = getQuickStats($pdo);
        sendSSE(
            'stats_' . time(),
            'stats_update',
            [
                'stats' => $stats,
                'timestamp' => time(),
                'panel' => $admin_panel
            ]
        );
    }
    
    // Enviar heartbeat cada 15 segundos
    if (time() % 15 == 0) {
        sendSSE(
            'heartbeat_' . time(),
            'heartbeat',
            [
                'timestamp' => time(),
                'status' => 'alive'
            ]
        );
    }
    
    // Esperar 2 segundos antes de la siguiente verificación
    sleep(2);
}

// Enviar evento de cierre
sendSSE(
    'close_' . time(),
    'close',
    ['message' => 'Connection closed']
);
?>
