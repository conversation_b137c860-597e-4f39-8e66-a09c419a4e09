<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Función para registrar evento de seguridad
function logSecurityEvent($pdo, $user_id, $action, $details = null, $severity = 'low') {
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $stmt = $pdo->prepare("INSERT INTO security_logs (user_id, action, ip_address, user_agent, details, severity) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$user_id, $action, $ip_address, $user_agent, json_encode($details), $severity]);
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $admin_id = $_SESSION['admin_id'] ?? 1;
        
        switch ($_POST['action']) {
            case 'clear_logs':
                $days = (int)$_POST['days'];
                
                if ($days > 0) {
                    $stmt = $pdo->prepare("DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
                    $stmt->execute([$days]);
                } else {
                    $stmt = $pdo->prepare("DELETE FROM security_logs");
                    $stmt->execute();
                }
                
                logSecurityEvent($pdo, $admin_id, 'security_logs_cleared', ['days' => $days], 'medium');
                $success_message = "Logs de seguridad limpiados correctamente";
                break;
                
            case 'block_ip':
                $ip_address = $_POST['ip_address'];
                $reason = $_POST['reason'];
                
                // En una implementación real, aquí se agregaría la IP a una lista de bloqueo
                logSecurityEvent($pdo, $admin_id, 'ip_blocked', ['ip' => $ip_address, 'reason' => $reason], 'high');
                $success_message = "IP $ip_address bloqueada correctamente";
                break;
                
            case 'force_logout':
                $target_user_id = (int)$_POST['user_id'];
                
                // En una implementación real, aquí se invalidarían las sesiones del usuario
                logSecurityEvent($pdo, $admin_id, 'force_logout', ['target_user_id' => $target_user_id], 'medium');
                $success_message = "Usuario desconectado forzosamente";
                break;
        }
    }
}

// Obtener filtros
$severity_filter = $_GET['severity'] ?? 'all';
$action_filter = $_GET['action'] ?? 'all';
$days_filter = (int)($_GET['days'] ?? 7);

// Construir consulta
$where_conditions = ["sl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)"];
$params = [$days_filter];

if ($severity_filter !== 'all') {
    $where_conditions[] = "sl.severity = ?";
    $params[] = $severity_filter;
}

if ($action_filter !== 'all') {
    $where_conditions[] = "sl.action LIKE ?";
    $params[] = "%$action_filter%";
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Obtener logs de seguridad
$stmt = $pdo->prepare("
    SELECT sl.*, u.username
    FROM security_logs sl 
    LEFT JOIN users u ON sl.user_id = u.id 
    $where_clause
    ORDER BY sl.created_at DESC
    LIMIT 100
");
$stmt->execute($params);
$security_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas de seguridad
$stats_stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_events,
        SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical_events,
        SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as high_events,
        SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as medium_events,
        SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as low_events,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_events,
        COUNT(DISTINCT ip_address) as unique_ips,
        COUNT(DISTINCT user_id) as unique_users
    FROM security_logs 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
");
$stats_stmt->execute([$days_filter]);
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Obtener IPs más activas
$top_ips_stmt = $pdo->prepare("
    SELECT ip_address, COUNT(*) as event_count, 
           MAX(created_at) as last_activity,
           GROUP_CONCAT(DISTINCT severity) as severities
    FROM security_logs 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY ip_address 
    ORDER BY event_count DESC 
    LIMIT 10
");
$top_ips_stmt->execute([$days_filter]);
$top_ips = $top_ips_stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener acciones más frecuentes
$top_actions_stmt = $pdo->prepare("
    SELECT action, COUNT(*) as action_count,
           MAX(created_at) as last_occurrence
    FROM security_logs 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY action 
    ORDER BY action_count DESC 
    LIMIT 10
");
$top_actions_stmt->execute([$days_filter]);
$top_actions = $top_actions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Verificar estado de seguridad general
$security_status = 'good';
if ($stats['critical_events'] > 0) {
    $security_status = 'critical';
} elseif ($stats['high_events'] > 5) {
    $security_status = 'warning';
} elseif ($stats['medium_events'] > 20) {
    $security_status = 'caution';
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Panel de Seguridad - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --critical-color: #dc2626;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--error-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .security-status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 500;
            margin-top: 1rem;
            font-size: 1.1rem;
        }

        .status-good {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-caution {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-warning {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .status-critical {
            background: rgba(220, 38, 38, 0.2);
            color: var(--critical-color);
            border: 1px solid rgba(220, 38, 38, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
            position: relative;
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
        }

        .stat-card.critical::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--critical-color);
        }

        .stat-card.high::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--error-color);
        }

        .stat-card.medium::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--warning-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-number.critical {
            color: var(--critical-color);
        }

        .stat-number.high {
            color: var(--error-color);
        }

        .stat-number.medium {
            color: var(--warning-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .section {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .section-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-content {
            padding: 1.5rem;
        }

        .filters {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-select {
            padding: 0.5rem 1rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .logs-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .log-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .log-item:hover {
            background: rgba(255,255,255,0.05);
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-critical {
            border-left: 4px solid var(--critical-color);
            background: rgba(220, 38, 38, 0.05);
        }

        .log-high {
            border-left: 4px solid var(--error-color);
        }

        .log-medium {
            border-left: 4px solid var(--warning-color);
        }

        .log-low {
            border-left: 4px solid var(--border-color);
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .log-action {
            font-weight: 600;
            color: var(--text-primary);
        }

        .log-time {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .log-details {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .severity-badge {
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .severity-critical {
            background: rgba(220, 38, 38, 0.2);
            color: var(--critical-color);
        }

        .severity-high {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
        }

        .severity-medium {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
        }

        .severity-low {
            background: rgba(107, 114, 128, 0.2);
            color: #6b7280;
        }

        .top-list {
            display: grid;
            gap: 1rem;
        }

        .top-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
        }

        .top-item-info {
            flex: 1;
        }

        .top-item-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        .top-item-detail {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .top-item-count {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .filters {
                width: 100%;
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="security_admin.php" class="logo">
                <i class="fas fa-shield-alt"></i>
                <span>Panel de Seguridad</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-shield-alt"></i>
                Panel de Seguridad
            </h1>
            <p class="page-subtitle">
                Monitoreo y gestión de la seguridad del sistema
            </p>
            
            <div class="security-status status-<?php echo $security_status; ?>">
                <i class="fas fa-<?php 
                    echo $security_status === 'good' ? 'shield-check' : 
                        ($security_status === 'critical' ? 'exclamation-triangle' : 'shield-alt'); 
                ?>"></i>
                <?php 
                $status_labels = [
                    'good' => 'Sistema Seguro',
                    'caution' => 'Precaución Requerida',
                    'warning' => 'Advertencia de Seguridad',
                    'critical' => 'Alerta Crítica de Seguridad'
                ];
                echo $status_labels[$security_status];
                ?>
            </div>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas de Seguridad -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total_events']); ?></div>
                <div class="stat-label">Eventos Totales (<?php echo $days_filter; ?> días)</div>
            </div>
            <div class="stat-card critical">
                <div class="stat-number critical"><?php echo $stats['critical_events']; ?></div>
                <div class="stat-label">Eventos Críticos</div>
            </div>
            <div class="stat-card high">
                <div class="stat-number high"><?php echo $stats['high_events']; ?></div>
                <div class="stat-label">Eventos de Alta Prioridad</div>
            </div>
            <div class="stat-card medium">
                <div class="stat-number medium"><?php echo $stats['medium_events']; ?></div>
                <div class="stat-label">Eventos de Media Prioridad</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['low_events']; ?></div>
                <div class="stat-label">Eventos de Baja Prioridad</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today_events']; ?></div>
                <div class="stat-label">Eventos Hoy</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['unique_ips']; ?></div>
                <div class="stat-label">IPs Únicas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['unique_users']; ?></div>
                <div class="stat-label">Usuarios Únicos</div>
            </div>
        </div>

        <!-- Análisis de Seguridad -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
            <!-- IPs Más Activas -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-globe"></i>
                        IPs Más Activas
                    </div>
                </div>
                <div class="section-content">
                    <div class="top-list">
                        <?php if (empty($top_ips)): ?>
                        <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                            <i class="fas fa-globe" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>No hay datos de IPs para el período seleccionado</p>
                        </div>
                        <?php else: ?>
                            <?php foreach ($top_ips as $ip): ?>
                            <div class="top-item">
                                <div class="top-item-info">
                                    <div class="top-item-value"><?php echo htmlspecialchars($ip['ip_address']); ?></div>
                                    <div class="top-item-detail">
                                        Última actividad: <?php echo date('d/m/Y H:i', strtotime($ip['last_activity'])); ?>
                                        <br>Severidades: <?php echo htmlspecialchars($ip['severities']); ?>
                                    </div>
                                </div>
                                <div class="top-item-count"><?php echo $ip['event_count']; ?></div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Acciones Más Frecuentes -->
            <div class="section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-chart-bar"></i>
                        Acciones Más Frecuentes
                    </div>
                </div>
                <div class="section-content">
                    <div class="top-list">
                        <?php if (empty($top_actions)): ?>
                        <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                            <i class="fas fa-chart-bar" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <p>No hay datos de acciones para el período seleccionado</p>
                        </div>
                        <?php else: ?>
                            <?php foreach ($top_actions as $action): ?>
                            <div class="top-item">
                                <div class="top-item-info">
                                    <div class="top-item-value"><?php echo htmlspecialchars($action['action']); ?></div>
                                    <div class="top-item-detail">
                                        Última ocurrencia: <?php echo date('d/m/Y H:i', strtotime($action['last_occurrence'])); ?>
                                    </div>
                                </div>
                                <div class="top-item-count"><?php echo $action['action_count']; ?></div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones de Seguridad -->
        <div class="section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-tools"></i>
                    Acciones de Seguridad
                </div>
            </div>
            <div class="section-content">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                    <!-- Limpiar Logs -->
                    <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: var(--border-radius);">
                        <h4 style="margin-bottom: 1rem; color: var(--text-primary);">
                            <i class="fas fa-broom"></i>
                            Limpiar Logs de Seguridad
                        </h4>
                        <form method="POST" style="display: flex; gap: 1rem; align-items: end;">
                            <input type="hidden" name="action" value="clear_logs">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Días a mantener</label>
                                <select name="days" class="filter-select" style="width: 100%;">
                                    <option value="0">Eliminar todos</option>
                                    <option value="7">Últimos 7 días</option>
                                    <option value="30" selected>Últimos 30 días</option>
                                    <option value="90">Últimos 90 días</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-warning" onclick="return confirm('¿Estás seguro de que quieres limpiar los logs de seguridad?')">
                                <i class="fas fa-broom"></i>
                                Limpiar
                            </button>
                        </form>
                    </div>

                    <!-- Bloquear IP -->
                    <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: var(--border-radius);">
                        <h4 style="margin-bottom: 1rem; color: var(--text-primary);">
                            <i class="fas fa-ban"></i>
                            Bloquear Dirección IP
                        </h4>
                        <form method="POST" style="display: flex; flex-direction: column; gap: 1rem;">
                            <input type="hidden" name="action" value="block_ip">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Dirección IP</label>
                                <input type="text" name="ip_address" class="filter-select" style="width: 100%;" placeholder="***********" required>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Motivo</label>
                                <input type="text" name="reason" class="filter-select" style="width: 100%;" placeholder="Actividad sospechosa" required>
                            </div>
                            <button type="submit" class="btn btn-danger" onclick="return confirm('¿Estás seguro de que quieres bloquear esta IP?')">
                                <i class="fas fa-ban"></i>
                                Bloquear IP
                            </button>
                        </form>
                    </div>

                    <!-- Forzar Desconexión -->
                    <div style="background: rgba(255,255,255,0.05); padding: 1.5rem; border-radius: var(--border-radius);">
                        <h4 style="margin-bottom: 1rem; color: var(--text-primary);">
                            <i class="fas fa-sign-out-alt"></i>
                            Forzar Desconexión
                        </h4>
                        <form method="POST" style="display: flex; flex-direction: column; gap: 1rem;">
                            <input type="hidden" name="action" value="force_logout">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">ID de Usuario</label>
                                <input type="number" name="user_id" class="filter-select" style="width: 100%;" placeholder="123" required>
                            </div>
                            <button type="submit" class="btn btn-warning" onclick="return confirm('¿Estás seguro de que quieres desconectar a este usuario?')">
                                <i class="fas fa-sign-out-alt"></i>
                                Desconectar
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs de Seguridad -->
        <div class="section">
            <div class="section-header">
                <div class="section-title">
                    <i class="fas fa-list"></i>
                    Logs de Seguridad
                </div>
                <div class="filters">
                    <form method="GET" style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                        <div>
                            <label style="display: block; margin-bottom: 0.3rem; color: var(--text-secondary); font-size: 0.8rem;">Período</label>
                            <select name="days" class="filter-select" onchange="this.form.submit()">
                                <option value="1" <?php echo $days_filter === 1 ? 'selected' : ''; ?>>Último día</option>
                                <option value="7" <?php echo $days_filter === 7 ? 'selected' : ''; ?>>Últimos 7 días</option>
                                <option value="30" <?php echo $days_filter === 30 ? 'selected' : ''; ?>>Últimos 30 días</option>
                                <option value="90" <?php echo $days_filter === 90 ? 'selected' : ''; ?>>Últimos 90 días</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.3rem; color: var(--text-secondary); font-size: 0.8rem;">Severidad</label>
                            <select name="severity" class="filter-select" onchange="this.form.submit()">
                                <option value="all" <?php echo $severity_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                                <option value="critical" <?php echo $severity_filter === 'critical' ? 'selected' : ''; ?>>Crítica</option>
                                <option value="high" <?php echo $severity_filter === 'high' ? 'selected' : ''; ?>>Alta</option>
                                <option value="medium" <?php echo $severity_filter === 'medium' ? 'selected' : ''; ?>>Media</option>
                                <option value="low" <?php echo $severity_filter === 'low' ? 'selected' : ''; ?>>Baja</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.3rem; color: var(--text-secondary); font-size: 0.8rem;">Acción</label>
                            <select name="action" class="filter-select" onchange="this.form.submit()">
                                <option value="all" <?php echo $action_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                                <option value="login" <?php echo $action_filter === 'login' ? 'selected' : ''; ?>>Login</option>
                                <option value="logout" <?php echo $action_filter === 'logout' ? 'selected' : ''; ?>>Logout</option>
                                <option value="admin" <?php echo $action_filter === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                <option value="security" <?php echo $action_filter === 'security' ? 'selected' : ''; ?>>Seguridad</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>
            <div class="section-content">
                <div class="logs-container">
                    <?php if (empty($security_logs)): ?>
                    <div style="text-align: center; color: var(--text-secondary); padding: 3rem;">
                        <i class="fas fa-shield-alt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>No hay logs de seguridad para el período y filtros seleccionados</p>
                    </div>
                    <?php else: ?>
                        <?php foreach ($security_logs as $log): ?>
                        <div class="log-item log-<?php echo $log['severity']; ?>">
                            <div class="log-header">
                                <div class="log-action">
                                    <i class="fas fa-<?php
                                        echo strpos($log['action'], 'login') !== false ? 'sign-in-alt' :
                                            (strpos($log['action'], 'logout') !== false ? 'sign-out-alt' :
                                            (strpos($log['action'], 'admin') !== false ? 'user-shield' : 'shield-alt'));
                                    ?>"></i>
                                    <?php echo htmlspecialchars($log['action']); ?>
                                </div>
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <span class="severity-badge severity-<?php echo $log['severity']; ?>">
                                        <?php echo strtoupper($log['severity']); ?>
                                    </span>
                                    <div class="log-time">
                                        <?php echo date('d/m/Y H:i:s', strtotime($log['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="log-details">
                                <strong>Usuario:</strong> <?php echo htmlspecialchars($log['username'] ?? 'Sistema'); ?> |
                                <strong>IP:</strong> <?php echo htmlspecialchars($log['ip_address']); ?> |
                                <strong>User Agent:</strong> <?php echo htmlspecialchars(substr($log['user_agent'], 0, 100)); ?>
                                <?php if (strlen($log['user_agent']) > 100): ?>...<?php endif; ?>
                            </div>
                            <?php if ($log['details'] && $log['details'] !== 'null'): ?>
                            <div style="background: rgba(255,255,255,0.05); padding: 0.75rem; border-radius: 6px; margin-top: 0.5rem; font-family: monospace; font-size: 0.8rem;">
                                <strong>Detalles:</strong> <?php echo htmlspecialchars($log['details']); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Auto-refresh cada 30 segundos
        setInterval(function() {
            if (document.hidden) return;

            const activeElement = document.activeElement;
            if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'SELECT') {
                location.reload();
            }
        }, 30000);

        // Resaltar eventos críticos
        document.querySelectorAll('.log-critical').forEach(item => {
            item.style.animation = 'pulse 3s infinite';
        });

        // Confirmación para acciones críticas
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'clear_logs') {
                    const days = this.querySelector('select[name="days"]').value;
                    const message = days === '0' ?
                        '¿Estás seguro de que quieres eliminar TODOS los logs de seguridad? Esta acción no se puede deshacer.' :
                        `¿Estás seguro de que quieres eliminar los logs de seguridad anteriores a ${days} días?`;
                    if (!confirm(message)) {
                        e.preventDefault();
                    }
                } else if (action === 'block_ip') {
                    const ip = this.querySelector('input[name="ip_address"]').value;
                    if (!confirm(`¿Estás seguro de que quieres bloquear la IP ${ip}?`)) {
                        e.preventDefault();
                    }
                } else if (action === 'force_logout') {
                    const userId = this.querySelector('input[name="user_id"]').value;
                    if (!confirm(`¿Estás seguro de que quieres desconectar forzosamente al usuario ${userId}?`)) {
                        e.preventDefault();
                    }
                }
            });
        });

        // Validación de IP
        document.querySelector('input[name="ip_address"]')?.addEventListener('input', function(e) {
            const ip = e.target.value;
            const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

            if (ip && !ipRegex.test(ip)) {
                e.target.style.borderColor = 'var(--error-color)';
            } else {
                e.target.style.borderColor = 'var(--border-color)';
            }
        });

        // Mostrar alerta si hay eventos críticos
        const criticalEvents = <?php echo $stats['critical_events']; ?>;
        if (criticalEvents > 0) {
            console.warn(`⚠️ ${criticalEvents} evento(s) crítico(s) de seguridad detectado(s)`);
        }

        // Animación de entrada para las tarjetas
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.stat-card, .section').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
