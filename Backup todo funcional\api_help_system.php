<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_articles':
            $category = $_GET['category'] ?? '';
            $search = $_GET['search'] ?? '';
            $featured_only = isset($_GET['featured']) && $_GET['featured'] === 'true';
            
            $where_conditions = ["status = 'published'"];
            $params = [];
            
            if (!empty($category)) {
                $where_conditions[] = "category = ?";
                $params[] = $category;
            }
            
            if (!empty($search)) {
                $where_conditions[] = "(title LIKE ? OR content LIKE ? OR excerpt LIKE ?)";
                $search_term = "%$search%";
                $params[] = $search_term;
                $params[] = $search_term;
                $params[] = $search_term;
            }
            
            if ($featured_only) {
                $where_conditions[] = "is_featured = 1";
            }
            
            $where_clause = "WHERE " . implode(" AND ", $where_conditions);
            
            $stmt = $pdo->prepare("
                SELECT id, title, excerpt, category, subcategory, is_featured, view_count, created_at
                FROM help_articles 
                $where_clause
                ORDER BY is_featured DESC, view_count DESC, created_at DESC
            ");
            $stmt->execute($params);
            $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'articles' => $articles,
                'total' => count($articles)
            ]);
            break;
            
        case 'get_article':
            $id = (int)($_GET['id'] ?? 0);
            
            if (!$id) {
                throw new Exception('ID de artículo requerido');
            }
            
            $stmt = $pdo->prepare("
                SELECT * FROM help_articles 
                WHERE id = ? AND status = 'published'
            ");
            $stmt->execute([$id]);
            $article = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$article) {
                throw new Exception('Artículo no encontrado');
            }
            
            // Incrementar contador de vistas
            $stmt = $pdo->prepare("UPDATE help_articles SET view_count = view_count + 1 WHERE id = ?");
            $stmt->execute([$id]);
            
            // Crear notificación para admin (opcional)
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE 'admin_notifications'");
                if ($stmt->rowCount() > 0) {
                    // Solo crear notificación si es un artículo muy visto (cada 10 vistas)
                    if (($article['view_count'] + 1) % 10 === 0) {
                        $stmt = $pdo->prepare("INSERT INTO admin_notifications (type, title, message, reference_id, reference_type) VALUES (?, ?, ?, ?, ?)");
                        $stmt->execute([
                            'system',
                            'Artículo popular',
                            "El artículo '{$article['title']}' ha alcanzado " . ($article['view_count'] + 1) . " vistas",
                            $id,
                            'help_articles'
                        ]);
                    }
                }
            } catch (Exception $e) {
                // Continuar sin notificación si hay error
            }
            
            echo json_encode([
                'success' => true,
                'article' => $article
            ]);
            break;
            
        case 'get_categories':
            $stmt = $pdo->query("
                SELECT 
                    category,
                    COUNT(*) as count,
                    SUM(view_count) as total_views
                FROM help_articles 
                WHERE status = 'published'
                GROUP BY category
                ORDER BY count DESC
            ");
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'categories' => $categories
            ]);
            break;
            
        case 'search_articles':
            $query = $_GET['q'] ?? '';
            
            if (empty($query)) {
                throw new Exception('Término de búsqueda requerido');
            }
            
            $search_term = "%$query%";
            $stmt = $pdo->prepare("
                SELECT id, title, excerpt, category, subcategory, view_count,
                       MATCH(title, content) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance
                FROM help_articles 
                WHERE status = 'published' 
                AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ?)
                ORDER BY relevance DESC, view_count DESC
                LIMIT 20
            ");
            $stmt->execute([$query, $search_term, $search_term, $search_term]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'results' => $results,
                'query' => $query,
                'total' => count($results)
            ]);
            break;
            
        case 'get_popular':
            $limit = (int)($_GET['limit'] ?? 5);
            
            $stmt = $pdo->prepare("
                SELECT id, title, excerpt, category, view_count
                FROM help_articles 
                WHERE status = 'published'
                ORDER BY view_count DESC, created_at DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            $popular = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'articles' => $popular
            ]);
            break;
            
        case 'get_featured':
            $stmt = $pdo->query("
                SELECT id, title, excerpt, category, view_count
                FROM help_articles 
                WHERE status = 'published' AND is_featured = 1
                ORDER BY view_count DESC, created_at DESC
            ");
            $featured = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'articles' => $featured
            ]);
            break;
            
        case 'get_stats':
            // Solo para admins
            if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
                throw new Exception('Acceso denegado');
            }
            
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total_articles,
                    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published_articles,
                    SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured_articles,
                    SUM(view_count) as total_views,
                    AVG(view_count) as avg_views,
                    MAX(view_count) as max_views
                FROM help_articles
            ");
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Artículos más populares
            $stmt = $pdo->query("
                SELECT title, view_count
                FROM help_articles 
                WHERE status = 'published'
                ORDER BY view_count DESC
                LIMIT 5
            ");
            $top_articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'stats' => $stats,
                'top_articles' => $top_articles
            ]);
            break;
            
        case 'submit_feedback':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Método no permitido');
            }
            
            $article_id = (int)($_POST['article_id'] ?? 0);
            $rating = (int)($_POST['rating'] ?? 0);
            $feedback = clean_input($_POST['feedback'] ?? '');
            
            if (!$article_id || $rating < 1 || $rating > 5) {
                throw new Exception('Datos inválidos');
            }
            
            // Crear tabla de feedback si no existe
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS help_feedback (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    article_id INT NOT NULL,
                    rating INT NOT NULL,
                    feedback TEXT,
                    user_id INT NULL,
                    ip_address VARCHAR(45),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (article_id) REFERENCES help_articles(id) ON DELETE CASCADE
                )
            ");
            
            $user_id = $_SESSION['user_id'] ?? null;
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
            
            $stmt = $pdo->prepare("INSERT INTO help_feedback (article_id, rating, feedback, user_id, ip_address) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$article_id, $rating, $feedback, $user_id, $ip_address]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Feedback enviado correctamente'
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
