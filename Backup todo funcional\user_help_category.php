<?php
session_start();
require_once 'config.php';

// Obtener categoría
$category = $_GET['category'] ?? '';

if (empty($category)) {
    header('Location: user_help.php');
    exit;
}

// Obtener artículos de la categoría usando la API
$articles = [];
try {
    $api_url = 'api_help_system.php?action=get_articles&category=' . urlencode($category);
    $response = @file_get_contents($api_url);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $articles = $data['articles'];
        }
    }
} catch (Exception $e) {
    // Fallback: obtener directamente de la base de datos
    try {
        $stmt = $pdo->prepare("
            SELECT id, title, excerpt, subcategory, view_count, created_at, is_featured
            FROM help_articles 
            WHERE category = ? AND status = 'published'
            ORDER BY is_featured DESC, view_count DESC, created_at DESC
        ");
        $stmt->execute([$category]);
        $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Error de base de datos
    }
}

// Configuración de categorías
$category_config = [
    'faq' => [
        'name' => 'Preguntas Frecuentes',
        'icon' => 'fas fa-question-circle',
        'description' => 'Respuestas a las preguntas más comunes sobre nuestros servicios'
    ],
    'tutorial' => [
        'name' => 'Tutoriales',
        'icon' => 'fas fa-graduation-cap',
        'description' => 'Guías paso a paso para configurar y usar nuestros servicios'
    ],
    'troubleshooting' => [
        'name' => 'Solución de Problemas',
        'icon' => 'fas fa-tools',
        'description' => 'Soluciones para problemas técnicos comunes'
    ],
    'setup' => [
        'name' => 'Configuración',
        'icon' => 'fas fa-cog',
        'description' => 'Instrucciones para configurar dispositivos y aplicaciones'
    ],
    'guide' => [
        'name' => 'Guías',
        'icon' => 'fas fa-book',
        'description' => 'Guías detalladas y documentación completa'
    ],
    'billing' => [
        'name' => 'Facturación',
        'icon' => 'fas fa-credit-card',
        'description' => 'Información sobre pagos, suscripciones y facturación'
    ]
];

$current_category = $category_config[$category] ?? [
    'name' => ucfirst($category),
    'icon' => 'fas fa-folder',
    'description' => 'Artículos de ' . ucfirst($category)
];

// Agrupar por subcategoría
$articles_by_subcategory = [];
foreach ($articles as $article) {
    $subcategory = $article['subcategory'] ?? 'general';
    $articles_by_subcategory[$subcategory][] = $article;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $current_category['name']; ?> - Centro de Ayuda RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --dark-bg: #0f172a;
            --border-color: #334155;
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, #020617 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .back-button {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .back-button:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .category-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .category-title {
            font-size: 2.5rem;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .category-description {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .category-stats {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .search-section {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
        }

        .search-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .search-button:hover {
            background: var(--primary-dark);
        }

        .subcategory-section {
            margin-bottom: 2rem;
        }

        .subcategory-title {
            color: var(--text-primary);
            font-size: 1.5rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--border-color);
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .article-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            position: relative;
        }

        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            border-color: var(--primary-color);
        }

        .article-card.featured {
            border-color: var(--warning-color);
        }

        .article-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .article-title {
            color: var(--text-primary);
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            line-height: 1.3;
        }

        .featured-badge {
            background: var(--warning-color);
            color: var(--dark-bg);
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .article-excerpt {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .article-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .no-articles {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-articles i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .category-title {
                font-size: 2rem;
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .articles-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="user_help.php" class="back-button">
                <i class="fas fa-arrow-left"></i> Volver al Centro de Ayuda
            </a>
            
            <div class="category-icon">
                <i class="<?php echo $current_category['icon']; ?>"></i>
            </div>
            
            <h1 class="category-title"><?php echo $current_category['name']; ?></h1>
            <p class="category-description"><?php echo $current_category['description']; ?></p>
            
            <div class="category-stats">
                <i class="fas fa-file-alt"></i> <?php echo count($articles); ?> artículos disponibles
            </div>
        </div>

        <div class="search-section">
            <form method="GET" action="user_help.php" class="search-form">
                <input type="text" name="search" class="search-input" 
                       placeholder="Buscar en <?php echo $current_category['name']; ?>..." 
                       value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                <button type="submit" class="search-button">
                    <i class="fas fa-search"></i> Buscar
                </button>
            </form>
        </div>

        <?php if (empty($articles)): ?>
        <div class="no-articles">
            <i class="fas fa-search"></i>
            <h3>No hay artículos disponibles</h3>
            <p>Esta categoría aún no tiene artículos publicados.</p>
        </div>
        <?php else: ?>
            <?php foreach ($articles_by_subcategory as $subcategory => $subcategory_articles): ?>
            <div class="subcategory-section">
                <h2 class="subcategory-title">
                    <?php echo $subcategory === 'general' ? 'General' : ucfirst($subcategory); ?>
                    <span style="color: var(--text-secondary); font-size: 0.8rem; font-weight: normal;">
                        (<?php echo count($subcategory_articles); ?> artículos)
                    </span>
                </h2>
                
                <div class="articles-grid">
                    <?php foreach ($subcategory_articles as $article): ?>
                    <a href="user_help_article.php?id=<?php echo $article['id']; ?>" 
                       class="article-card <?php echo $article['is_featured'] ? 'featured' : ''; ?>">
                        <div class="article-header">
                            <div>
                                <h3 class="article-title"><?php echo htmlspecialchars($article['title']); ?></h3>
                            </div>
                            <?php if ($article['is_featured']): ?>
                            <span class="featured-badge">Destacado</span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if (!empty($article['excerpt'])): ?>
                        <p class="article-excerpt"><?php echo htmlspecialchars($article['excerpt']); ?></p>
                        <?php endif; ?>
                        
                        <div class="article-meta">
                            <span><i class="fas fa-eye"></i> <?php echo number_format($article['view_count']); ?> vistas</span>
                            <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($article['created_at'])); ?></span>
                        </div>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <script>
        // Resaltar término de búsqueda si existe
        const searchTerm = new URLSearchParams(window.location.search).get('search');
        if (searchTerm) {
            const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            
            document.querySelectorAll('.article-title, .article-excerpt').forEach(element => {
                element.innerHTML = element.innerHTML.replace(regex, 
                    '<mark style="background: var(--warning-color); color: var(--dark-bg); padding: 0.1rem 0.2rem; border-radius: 3px;">$1</mark>'
                );
            });
        }
    </script>
</body>
</html>
