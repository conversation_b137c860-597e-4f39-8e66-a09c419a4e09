<?php
// Script para actualizar las tablas M3U con las nuevas columnas
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h1>🔧 Actualizar Tablas M3U</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; } .success { color: #28a745; } .error { color: #dc3545; } .info { color: #17a2b8; } .warning { color: #ffc107; } .step { background: #222; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007bff; }</style>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión a BD exitosa</p>";
    
    // Verificar estructura actual de m3u_lists
    echo "<h2>📋 Verificando Estructura Actual:</h2>";
    $stmt = $pdo->query("DESCRIBE m3u_lists");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $existing_column_names = array_column($existing_columns, 'Field');
    echo "<p class='info'>Columnas existentes: " . implode(', ', $existing_column_names) . "</p>";
    
    // Definir las nuevas columnas que necesitamos
    $new_columns = [
        'server_url' => "VARCHAR(500) DEFAULT NULL COMMENT 'URL del servidor Xtream Codes'",
        'list_type' => "ENUM('direct_m3u', 'xtream_codes') DEFAULT 'direct_m3u' COMMENT 'Tipo de lista'",
        'folder_name' => "VARCHAR(255) DEFAULT NULL COMMENT 'Nombre de la carpeta local'",
        'local_file_path' => "TEXT DEFAULT NULL COMMENT 'Ruta del archivo descargado'",
        'last_download' => "TIMESTAMP NULL DEFAULT NULL COMMENT 'Última descarga'",
        'file_size' => "BIGINT DEFAULT 0 COMMENT 'Tamaño del archivo descargado'"
    ];
    
    echo "<h2>🔧 Agregando Columnas Faltantes:</h2>";
    
    foreach ($new_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_column_names)) {
            try {
                $sql = "ALTER TABLE m3u_lists ADD COLUMN $column_name $column_definition";
                $pdo->exec($sql);
                echo "<div class='step'>";
                echo "<p class='success'>✅ Columna '$column_name' agregada exitosamente</p>";
                echo "</div>";
            } catch (PDOException $e) {
                echo "<div class='step'>";
                echo "<p class='error'>❌ Error agregando '$column_name': " . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='step'>";
            echo "<p class='info'>ℹ️ Columna '$column_name' ya existe</p>";
            echo "</div>";
        }
    }
    
    // Verificar estructura final
    echo "<h2>📊 Estructura Final de m3u_lists:</h2>";
    $stmt = $pdo->query("DESCRIBE m3u_lists");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='step'>";
    echo "<table style='width: 100%; border-collapse: collapse; color: white;'>";
    echo "<tr style='background: #333;'><th style='border: 1px solid #555; padding: 8px;'>Campo</th><th style='border: 1px solid #555; padding: 8px;'>Tipo</th><th style='border: 1px solid #555; padding: 8px;'>Nulo</th><th style='border: 1px solid #555; padding: 8px;'>Por Defecto</th></tr>";
    
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td style='border: 1px solid #555; padding: 8px;'>" . $column['Field'] . "</td>";
        echo "<td style='border: 1px solid #555; padding: 8px;'>" . $column['Type'] . "</td>";
        echo "<td style='border: 1px solid #555; padding: 8px;'>" . $column['Null'] . "</td>";
        echo "<td style='border: 1px solid #555; padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Actualizar listas existentes para que tengan valores por defecto
    echo "<h2>🔄 Actualizando Listas Existentes:</h2>";
    
    try {
        // Establecer list_type por defecto para listas existentes
        $stmt = $pdo->exec("UPDATE m3u_lists SET list_type = 'direct_m3u' WHERE list_type IS NULL");
        echo "<p class='success'>✅ Tipo de lista establecido para $stmt registros existentes</p>";
        
        // Crear carpetas para listas existentes que no las tengan
        $stmt = $pdo->query("SELECT id, name FROM m3u_lists WHERE folder_name IS NULL");
        $lists_without_folders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($lists_without_folders as $list) {
            $folderName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $list['name']) . '_' . $list['id'];
            $folderPath = 'uploads/m3u_files/' . $folderName;
            
            // Crear directorio
            if (!file_exists($folderPath)) {
                mkdir($folderPath, 0755, true);
            }
            
            // Actualizar BD
            $updateStmt = $pdo->prepare("UPDATE m3u_lists SET folder_name = ? WHERE id = ?");
            $updateStmt->execute([$folderName, $list['id']]);
            
            echo "<p class='success'>✅ Carpeta '$folderName' creada para lista: " . htmlspecialchars($list['name']) . "</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Error actualizando listas existentes: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>🎉 ¡Actualización Completada!</h2>";
    echo "<div class='step'>";
    echo "<p class='success'>✅ Todas las columnas necesarias han sido agregadas</p>";
    echo "<p class='success'>✅ Las listas existentes han sido actualizadas</p>";
    echo "<p class='success'>✅ Las carpetas han sido creadas</p>";
    echo "<br>";
    echo "<p><strong>Próximos pasos:</strong></p>";
    echo "<ul>";
    echo "<li><a href='m3u_manager.php' style='color: #28a745;'>📡 Ir al Gestor M3U</a></li>";
    echo "<li><a href='m3u_downloader.php' style='color: #17a2b8;'>📥 Descargar Listas</a></li>";
    echo "<li><a href='admin.php' style='color: #ffc107;'>📊 Volver al Admin</a></li>";
    echo "</ul>";
    echo "</div>";
    
    // Mostrar estadísticas finales
    echo "<h2>📈 Estadísticas del Sistema:</h2>";
    echo "<div class='step'>";
    
    $stats = [];
    $stats['total_lists'] = $pdo->query("SELECT COUNT(*) FROM m3u_lists")->fetchColumn();
    $stats['active_lists'] = $pdo->query("SELECT COUNT(*) FROM m3u_lists WHERE is_active = 1")->fetchColumn();
    $stats['xtream_lists'] = $pdo->query("SELECT COUNT(*) FROM m3u_lists WHERE list_type = 'xtream_codes'")->fetchColumn();
    $stats['direct_lists'] = $pdo->query("SELECT COUNT(*) FROM m3u_lists WHERE list_type = 'direct_m3u'")->fetchColumn();
    $stats['downloaded_lists'] = $pdo->query("SELECT COUNT(*) FROM m3u_lists WHERE local_file_path IS NOT NULL")->fetchColumn();
    
    echo "<ul>";
    echo "<li>📊 Total de listas: {$stats['total_lists']}</li>";
    echo "<li>✅ Listas activas: {$stats['active_lists']}</li>";
    echo "<li>🔐 Listas Xtream Codes: {$stats['xtream_lists']}</li>";
    echo "<li>📄 Listas URL directa: {$stats['direct_lists']}</li>";
    echo "<li>📥 Listas descargadas: {$stats['downloaded_lists']}</li>";
    echo "</ul>";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='admin.php'>📊 Panel Admin</a> | <a href='m3u_manager.php'>📡 Gestor M3U</a> | <a href='index.php'>🏠 Inicio</a></p>";
?>
