<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_status':
                $ticket_id = (int)$_POST['ticket_id'];
                $new_status = $_POST['status'];
                $admin_notes = $_POST['admin_notes'] ?? '';
                
                $stmt = $pdo->prepare("UPDATE support_tickets SET status = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$new_status, $ticket_id])) {
                    if ($admin_notes) {
                        $stmt = $pdo->prepare("INSERT INTO ticket_responses (ticket_id, user_id, message, is_admin) VALUES (?, ?, ?, 1)");
                        $stmt->execute([$ticket_id, $_SESSION['admin_id'] ?? 1, $admin_notes]);
                    }
                    $message = "Ticket actualizado correctamente";
                    $message_type = "success";
                } else {
                    $message = "Error al actualizar el ticket";
                    $message_type = "error";
                }
                break;
                
            case 'respond':
                $ticket_id = (int)$_POST['ticket_id'];
                $response = $_POST['response'];
                
                $stmt = $pdo->prepare("INSERT INTO ticket_responses (ticket_id, user_id, message, is_admin) VALUES (?, ?, ?, 1)");
                if ($stmt->execute([$ticket_id, $_SESSION['admin_id'] ?? 1, $response])) {
                    $message = "Respuesta enviada correctamente";
                    $message_type = "success";
                } else {
                    $message = "Error al enviar la respuesta";
                    $message_type = "error";
                }
                break;
        }
    }
}

// Obtener filtros
$status_filter = $_GET['status'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';
$category_filter = $_GET['category'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($priority_filter !== 'all') {
    $where_conditions[] = "priority = ?";
    $params[] = $priority_filter;
}

if ($category_filter !== 'all') {
    $where_conditions[] = "category = ?";
    $params[] = $category_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener tickets
$stmt = $pdo->prepare("
    SELECT st.*, u.username 
    FROM support_tickets st 
    LEFT JOIN users u ON st.user_id = u.id 
    $where_clause 
    ORDER BY 
        CASE st.priority 
            WHEN 'urgent' THEN 1 
            WHEN 'high' THEN 2 
            WHEN 'medium' THEN 3 
            WHEN 'low' THEN 4 
        END,
        st.created_at DESC
");
$stmt->execute($params);
$tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'open'");
$open_count = $stmt->fetchColumn() ?: 0;

$stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'in_progress'");
$in_progress_count = $stmt->fetchColumn() ?: 0;

$stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE status = 'resolved'");
$resolved_count = $stmt->fetchColumn() ?: 0;

$stmt = $pdo->query("SELECT COUNT(*) FROM support_tickets WHERE priority = 'urgent' AND status IN ('open', 'in_progress')");
$urgent_count = $stmt->fetchColumn() ?: 0;
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Gestión de Tickets Profesional - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1e40af;
            --primary-light: #3b82f6;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --light-bg: #f8fafc;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #f8fafc;
            --accent-color: #10b981;
            --accent-light: #34d399;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
            --gradient-error: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .header-nav {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--light-bg);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        .nav-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .nav-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Main Content */
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .stat-card.open::before {
            background: var(--gradient-error);
        }

        .stat-card.progress::before {
            background: var(--gradient-warning);
        }

        .stat-card.resolved::before {
            background: var(--gradient-success);
        }

        .stat-card.urgent::before {
            background: var(--gradient-primary);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .stat-icon.open {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .stat-icon.progress {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.resolved {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-icon.urgent {
            background: rgba(30, 64, 175, 0.1);
            color: var(--primary-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        /* Filters */
        .filters-section {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .filter-select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: white;
            color: var(--text-primary);
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .filter-btn {
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
        }

        .filter-btn:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
        }

        /* Tickets Table */
        .tickets-section {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .section-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tickets-table {
            width: 100%;
            border-collapse: collapse;
        }

        .tickets-table th {
            background: var(--light-bg);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .tickets-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-light);
            vertical-align: top;
        }

        .tickets-table tr:hover {
            background: var(--light-bg);
        }

        .ticket-id {
            font-weight: 600;
            color: var(--primary-color);
        }

        .ticket-subject {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .ticket-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .priority-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-badge.urgent {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .priority-badge.high {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .priority-badge.medium {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info-color);
        }

        .priority-badge.low {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.open {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .status-badge.in_progress {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-badge.resolved {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-badge.closed {
            background: rgba(100, 116, 139, 0.1);
            color: var(--text-secondary);
        }

        .ticket-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.8rem;
            min-width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn.view {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info-color);
        }

        .action-btn.edit {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .action-btn.respond {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .modal-close:hover {
            background: var(--light-bg);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--light-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        /* Message */
        .message {
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .message.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .tickets-table {
                font-size: 0.8rem;
            }

            .tickets-table th,
            .tickets-table td {
                padding: 0.5rem;
            }

            .ticket-description {
                max-width: 150px;
            }
        }

        /* Loading Animation */
        .loading {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="tickets_admin_professional.php" class="logo">
                <i class="fas fa-ticket-alt"></i>
                <span>Gestión de Tickets</span>
            </a>

            <nav class="header-nav">
                <a href="admin2_professional.php" class="nav-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin_professional.php" class="nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="user_tickets.php" class="nav-btn primary">
                    <i class="fas fa-plus"></i>
                    <span>Nuevo Ticket</span>
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-ticket-alt" style="color: var(--primary-color);"></i>
                Sistema de Gestión de Tickets
            </h1>
            <p class="page-subtitle">
                Administración completa de tickets de soporte técnico con seguimiento en tiempo real
            </p>
        </div>

        <!-- Message -->
        <?php if ($message): ?>
        <div class="message <?php echo $message_type; ?>">
            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card open">
                <div class="stat-header">
                    <div class="stat-title">Tickets Abiertos</div>
                    <div class="stat-icon open">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                </div>
                <div class="stat-number" id="openCount"><?php echo $open_count; ?></div>
                <div class="stat-change">Requieren atención</div>
            </div>

            <div class="stat-card progress">
                <div class="stat-header">
                    <div class="stat-title">En Progreso</div>
                    <div class="stat-icon progress">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-number" id="progressCount"><?php echo $in_progress_count; ?></div>
                <div class="stat-change">Siendo procesados</div>
            </div>

            <div class="stat-card resolved">
                <div class="stat-header">
                    <div class="stat-title">Resueltos</div>
                    <div class="stat-icon resolved">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-number" id="resolvedCount"><?php echo $resolved_count; ?></div>
                <div class="stat-change">Completados</div>
            </div>

            <div class="stat-card urgent">
                <div class="stat-header">
                    <div class="stat-title">Urgentes</div>
                    <div class="stat-icon urgent">
                        <i class="fas fa-fire"></i>
                    </div>
                </div>
                <div class="stat-number" id="urgentCount"><?php echo $urgent_count; ?></div>
                <div class="stat-change">Prioridad alta</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>Abiertos</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>En Progreso</option>
                        <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>Resueltos</option>
                        <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Cerrados</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Prioridad</label>
                    <select name="priority" class="filter-select">
                        <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>Urgente</option>
                        <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>Alta</option>
                        <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>Media</option>
                        <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>Baja</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Categoría</label>
                    <select name="category" class="filter-select">
                        <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="technical" <?php echo $category_filter === 'technical' ? 'selected' : ''; ?>>Técnico</option>
                        <option value="billing" <?php echo $category_filter === 'billing' ? 'selected' : ''; ?>>Facturación</option>
                        <option value="content" <?php echo $category_filter === 'content' ? 'selected' : ''; ?>>Contenido</option>
                        <option value="app" <?php echo $category_filter === 'app' ? 'selected' : ''; ?>>Aplicación</option>
                        <option value="general" <?php echo $category_filter === 'general' ? 'selected' : ''; ?>>General</option>
                    </select>
                </div>

                <div class="filter-group">
                    <button type="submit" class="filter-btn">
                        <i class="fas fa-filter"></i>
                        Filtrar
                    </button>
                </div>
            </form>
        </div>

        <!-- Tickets Table -->
        <div class="tickets-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    Lista de Tickets (<?php echo count($tickets); ?>)
                </h2>
            </div>

            <?php if (empty($tickets)): ?>
            <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h3>No hay tickets</h3>
                <p>No se encontraron tickets con los filtros seleccionados</p>
            </div>
            <?php else: ?>
            <table class="tickets-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Asunto</th>
                        <th>Usuario</th>
                        <th>Prioridad</th>
                        <th>Estado</th>
                        <th>Categoría</th>
                        <th>Creado</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($tickets as $ticket): ?>
                    <tr>
                        <td>
                            <span class="ticket-id">#<?php echo $ticket['id']; ?></span>
                        </td>
                        <td>
                            <div class="ticket-subject"><?php echo htmlspecialchars($ticket['subject']); ?></div>
                            <div class="ticket-description"><?php echo htmlspecialchars($ticket['description']); ?></div>
                        </td>
                        <td><?php echo htmlspecialchars($ticket['username'] ?? 'Usuario #' . $ticket['user_id']); ?></td>
                        <td>
                            <span class="priority-badge <?php echo $ticket['priority']; ?>">
                                <?php echo ucfirst($ticket['priority']); ?>
                            </span>
                        </td>
                        <td>
                            <span class="status-badge <?php echo $ticket['status']; ?>">
                                <?php echo str_replace('_', ' ', ucfirst($ticket['status'])); ?>
                            </span>
                        </td>
                        <td><?php echo ucfirst($ticket['category']); ?></td>
                        <td><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></td>
                        <td>
                            <div class="ticket-actions">
                                <button class="action-btn view" onclick="viewTicket(<?php echo $ticket['id']; ?>)" title="Ver detalles">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn edit" onclick="editTicket(<?php echo $ticket['id']; ?>)" title="Editar estado">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn respond" onclick="respondTicket(<?php echo $ticket['id']; ?>)" title="Responder">
                                    <i class="fas fa-reply"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>
    </main>

    <!-- Modal para editar ticket -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Editar Ticket</h3>
                <button class="modal-close" onclick="closeModal('editModal')">&times;</button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="ticket_id" id="editTicketId">

                    <div class="form-group">
                        <label class="form-label">Estado</label>
                        <select name="status" id="editStatus" class="form-select">
                            <option value="open">Abierto</option>
                            <option value="in_progress">En Progreso</option>
                            <option value="resolved">Resuelto</option>
                            <option value="closed">Cerrado</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Notas del Administrador (Opcional)</label>
                        <textarea name="admin_notes" class="form-textarea" placeholder="Agregar notas sobre el cambio de estado..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Guardar Cambios
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para responder ticket -->
    <div id="respondModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Responder Ticket</h3>
                <button class="modal-close" onclick="closeModal('respondModal')">&times;</button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="respond">
                    <input type="hidden" name="ticket_id" id="respondTicketId">

                    <div class="form-group">
                        <label class="form-label">Respuesta</label>
                        <textarea name="response" class="form-textarea" placeholder="Escribir respuesta al usuario..." required style="min-height: 150px;"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('respondModal')">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Enviar Respuesta
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Auto-refresh stats every 30 seconds
        setInterval(async function() {
            try {
                const response = await fetch('api_admin_stats.php');
                const data = await response.json();

                if (data.success && data.stats) {
                    updateElement('openCount', data.stats.tickets_open);
                    updateElement('progressCount', data.stats.tickets_pending - data.stats.tickets_open);
                    updateElement('resolvedCount', data.stats.tickets_resolved || 0);
                    updateElement('urgentCount', data.stats.tickets_urgent || 0);
                }
            } catch (error) {
                console.log('Stats update failed:', error);
            }
        }, 30000);

        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element && element.textContent != value) {
                element.textContent = value;
                element.style.transform = 'scale(1.1)';
                element.style.color = 'var(--accent-color)';

                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 300);
            }
        }

        function viewTicket(ticketId) {
            // Implementar vista de detalles del ticket
            window.open(`ticket_details.php?id=${ticketId}`, '_blank');
        }

        function editTicket(ticketId) {
            document.getElementById('editTicketId').value = ticketId;
            document.getElementById('editModal').style.display = 'block';
        }

        function respondTicket(ticketId) {
            document.getElementById('respondTicketId').value = ticketId;
            document.getElementById('respondModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Add loading states to action buttons
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                const originalClass = icon.className;

                icon.className = 'fas fa-spinner fa-spin';

                setTimeout(() => {
                    icon.className = originalClass;
                }, 1000);
            });
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth animations to stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-2px)';
                });
            });
        });
    </script>
</body>
</html>
