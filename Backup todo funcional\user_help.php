<?php
session_start();
require_once 'config.php';

// Para demo, usar usuario por defecto si no está logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'usuario';
}

// Obtener artículos de ayuda publicados
try {
    $stmt = $pdo->prepare("
        SELECT * FROM help_articles
        WHERE status = 'published'
        ORDER BY is_featured DESC, category, title
    ");
    $stmt->execute();
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug: mostrar cuántos artículos se encontraron
    error_log("Artículos encontrados: " . count($articles));

} catch (Exception $e) {
    $articles = [];
    $error_message = "Error al cargar artículos: " . $e->getMessage();
    error_log("Error en user_help: " . $e->getMessage());
}

// Agrupar por categoría
$articles_by_category = [];
foreach ($articles as $article) {
    $articles_by_category[$article['category']][] = $article;
}

// Obtener artículos destacados
$featured_articles = array_filter($articles, function($article) {
    return $article['is_featured'] == 1;
});

// Procesar búsqueda usando la API
$search_query = $_GET['search'] ?? '';
$search_results = [];

if ($search_query) {
    $api_url = 'api_help_system.php?action=search_articles&q=' . urlencode($search_query);
    $response = @file_get_contents($api_url);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $search_results = $data['results'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>❓ Centro de Ayuda - RGS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--warning-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .search-section {
            max-width: 600px;
            margin: 0 auto 3rem;
        }

        .search-form {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            background: var(--secondary-color);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 1rem;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .featured-section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .featured-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .article-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            transition: var(--transition);
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }

        .article-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
            border-color: var(--accent-color);
        }

        .article-card:hover::before {
            transform: scaleX(1);
        }

        .article-card.featured {
            border-color: var(--warning-color);
        }

        .article-card.featured::before {
            background: var(--warning-color);
            transform: scaleX(1);
        }

        .article-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .article-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .featured-badge {
            background: var(--warning-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .article-excerpt {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .category-badge {
            background: rgba(37, 99, 235, 0.2);
            color: var(--primary-color);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .categories-section {
            margin-bottom: 3rem;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .category-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-decoration: none;
            color: inherit;
            transition: var(--transition);
            text-align: center;
        }

        .category-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-color);
            box-shadow: 0 8px 32px rgba(0,0,0,0.6);
        }

        .category-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .category-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .category-count {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .search-results {
            margin-bottom: 3rem;
        }

        .results-header {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .results-count {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .quick-links {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .quick-links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .quick-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--text-primary);
            transition: var(--transition);
        }

        .quick-link:hover {
            background: var(--primary-color);
            transform: translateX(5px);
        }

        .quick-link i {
            color: var(--accent-color);
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .featured-grid, .categories-grid {
                grid-template-columns: 1fr;
            }

            .article-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .quick-links-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Estilos para feedback */
        .feedback-section {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 2rem;
        }

        .rating-stars {
            display: flex;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .star {
            font-size: 1.5rem;
            color: #6b7280;
            cursor: pointer;
            transition: color 0.2s;
        }

        .star:hover,
        .star.active {
            color: #f59e0b;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="user_help.php" class="logo">
                <i class="fas fa-question-circle"></i>
                <span>Centro de Ayuda</span>
            </a>
            
            <div class="nav-buttons">
                <a href="index2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver a Servicios</span>
                </a>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-question-circle" style="color: var(--warning-color);"></i>
                Centro de Ayuda
            </h1>
            <p class="page-subtitle">
                Encuentra respuestas a tus preguntas y aprende a sacar el máximo provecho de tu servicio IPTV
            </p>
        </div>

        <!-- Búsqueda -->
        <div class="search-section">
            <form method="GET" class="search-form">
                <i class="fas fa-search search-icon"></i>
                <input 
                    type="text" 
                    name="search" 
                    class="search-input" 
                    placeholder="Buscar en el centro de ayuda..."
                    value="<?php echo htmlspecialchars($search_query); ?>"
                >
            </form>
        </div>

        <?php if ($search_query): ?>
        <!-- Resultados de Búsqueda -->
        <div class="search-results">
            <div class="results-header">
                <h2 class="section-title">
                    <i class="fas fa-search"></i>
                    Resultados de Búsqueda
                </h2>
                <div class="results-count">
                    <?php echo count($search_results); ?> resultado(s) para "<?php echo htmlspecialchars($search_query); ?>"
                </div>
            </div>
            
            <?php if (empty($search_results)): ?>
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con otros términos de búsqueda o explora las categorías disponibles</p>
            </div>
            <?php else: ?>
            <div class="featured-grid">
                <?php foreach ($search_results as $article): ?>
                <a href="user_help_article.php?id=<?php echo $article['id']; ?>" class="article-card">
                    <div class="article-header">
                        <div>
                            <h3 class="article-title"><?php echo htmlspecialchars($article['title']); ?></h3>
                        </div>
                        <?php if ($article['is_featured']): ?>
                        <span class="featured-badge">Destacado</span>
                        <?php endif; ?>
                    </div>
                    <div class="article-excerpt">
                        <?php echo htmlspecialchars(substr(strip_tags($article['content']), 0, 150)); ?>...
                    </div>
                    <div class="article-meta">
                        <span class="category-badge"><?php echo ucfirst($article['category']); ?></span>
                        <span><i class="fas fa-eye"></i> <?php echo $article['views']; ?> vistas</span>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php else: ?>

        <!-- Enlaces Rápidos -->
        <div class="quick-links">
            <h2 class="section-title">
                <i class="fas fa-bolt"></i>
                Acceso Rápido
            </h2>
            <div class="quick-links-grid">
                <a href="user_tickets.php" class="quick-link">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Crear Ticket de Soporte</span>
                </a>
                <a href="user_chat.php" class="quick-link">
                    <i class="fas fa-comments"></i>
                    <span>Chat en Vivo</span>
                </a>
                <a href="user_apps.php" class="quick-link">
                    <i class="fas fa-mobile-alt"></i>
                    <span>Descargar Aplicaciones</span>
                </a>
                <a href="user_channels.php" class="quick-link">
                    <i class="fas fa-tv"></i>
                    <span>Solicitar Canales</span>
                </a>
            </div>
        </div>

        <!-- Artículos Destacados -->
        <?php if (!empty($featured_articles)): ?>
        <div class="featured-section">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                Artículos Destacados
            </h2>
            <div class="featured-grid">
                <?php foreach ($featured_articles as $article): ?>
                <a href="user_help_article.php?id=<?php echo $article['id']; ?>" class="article-card featured">
                    <div class="article-header">
                        <div>
                            <h3 class="article-title"><?php echo htmlspecialchars($article['title']); ?></h3>
                        </div>
                        <span class="featured-badge">Destacado</span>
                    </div>
                    <div class="article-excerpt">
                        <?php echo htmlspecialchars(substr(strip_tags($article['content']), 0, 150)); ?>...
                    </div>
                    <div class="article-meta">
                        <span class="category-badge"><?php echo ucfirst($article['category']); ?></span>
                        <span><i class="fas fa-eye"></i> <?php echo $article['views']; ?> vistas</span>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Categorías -->
        <div class="categories-section">
            <h2 class="section-title">
                <i class="fas fa-th-large"></i>
                Explorar por Categorías
            </h2>
            
            <?php if (empty($articles_by_category)): ?>
            <div class="no-results">
                <i class="fas fa-folder-open"></i>
                <h3>No hay artículos disponibles</h3>
                <p>Los artículos de ayuda estarán disponibles próximamente</p>
            </div>
            <?php else: ?>
            <div class="categories-grid">
                <?php 
                $category_icons = [
                    'setup' => 'fas fa-cog',
                    'troubleshooting' => 'fas fa-wrench',
                    'apps' => 'fas fa-mobile-alt',
                    'channels' => 'fas fa-tv',
                    'billing' => 'fas fa-credit-card',
                    'account' => 'fas fa-user',
                    'general' => 'fas fa-info-circle'
                ];
                
                $category_names = [
                    'setup' => 'Configuración',
                    'troubleshooting' => 'Solución de Problemas',
                    'apps' => 'Aplicaciones',
                    'channels' => 'Canales',
                    'billing' => 'Facturación',
                    'account' => 'Cuenta de Usuario',
                    'general' => 'General'
                ];
                ?>
                
                <?php foreach ($articles_by_category as $category => $category_articles): ?>
                <a href="user_help_category.php?category=<?php echo urlencode($category); ?>" class="category-card">
                    <div class="category-icon">
                        <i class="<?php echo $category_icons[$category] ?? 'fas fa-folder'; ?>"></i>
                    </div>
                    <h3 class="category-name">
                        <?php echo $category_names[$category] ?? ucfirst($category); ?>
                    </h3>
                    <div class="category-count">
                        <?php echo count($category_articles); ?> artículo(s)
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </main>

    <script>
        // Auto-focus en el campo de búsqueda
        document.querySelector('.search-input').focus();

        // Animación de entrada para las tarjetas
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        document.querySelectorAll('.article-card, .category-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Búsqueda en tiempo real (opcional)
        const searchInput = document.querySelector('.search-input');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    // En una implementación real, esto haría una búsqueda AJAX
                    console.log('Buscando:', query);
                }, 500);
            }
        });

        // Resaltar términos de búsqueda en los resultados
        <?php if ($search_query): ?>
        const searchTerm = '<?php echo addslashes($search_query); ?>';
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        
        document.querySelectorAll('.article-title, .article-excerpt').forEach(element => {
            element.innerHTML = element.innerHTML.replace(regex, '<mark style="background: var(--warning-color); color: var(--dark-bg); padding: 0.1rem 0.2rem; border-radius: 3px;">$1</mark>');
        });
        <?php endif; ?>
    </script>
</body>
</html>
