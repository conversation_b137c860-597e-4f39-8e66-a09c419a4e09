<?php
// Script para verificar y arreglar la tabla users
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h1>🔧 Reparar Tabla Users</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; } .success { color: #28a745; } .error { color: #dc3545; } .info { color: #17a2b8; } .warning { color: #ffc107; } pre { background: #333; padding: 10px; border-radius: 5px; }</style>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión a BD exitosa</p>";
    
    // Verificar estructura actual de la tabla users
    echo "<h2>📋 Estructura Actual de la Tabla Users:</h2>";
    $stmt = $pdo->query("DESCRIBE users");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    foreach ($existing_columns as $column) {
        echo $column['Field'] . " - " . $column['Type'] . " - " . $column['Default'] . "\n";
    }
    echo "</pre>";
    
    // Lista de columnas que deberían existir
    $required_columns = [
        'is_admin' => 'TINYINT(1) DEFAULT 0',
        'is_cliente_actual' => 'TINYINT(1) DEFAULT 0',
        'is_mavistv' => 'TINYINT(1) DEFAULT 0',
        'is_tvdigital' => 'TINYINT(1) DEFAULT 0',
        'is_limites507' => 'TINYINT(1) DEFAULT 0',
        'is_worldtv' => 'TINYINT(1) DEFAULT 0',
        'is_infest84' => 'TINYINT(1) DEFAULT 0',
        'is_rogsmediatv' => 'TINYINT(1) DEFAULT 0',
        'is_saul' => 'TINYINT(1) DEFAULT 0'
    ];
    
    // Verificar qué columnas faltan
    $existing_column_names = array_column($existing_columns, 'Field');
    $missing_columns = [];
    
    echo "<h2>🔍 Verificación de Columnas:</h2>";
    foreach ($required_columns as $column_name => $column_definition) {
        if (in_array($column_name, $existing_column_names)) {
            echo "<p class='success'>✅ $column_name - Existe</p>";
        } else {
            echo "<p class='error'>❌ $column_name - FALTA</p>";
            $missing_columns[$column_name] = $column_definition;
        }
    }
    
    // Si hay columnas faltantes, preguntar si agregar
    if (!empty($missing_columns)) {
        echo "<h2>⚠️ Columnas Faltantes Detectadas</h2>";
        echo "<p class='warning'>Se encontraron " . count($missing_columns) . " columnas faltantes.</p>";
        
        if (isset($_POST['add_columns']) && $_POST['add_columns'] === 'yes') {
            echo "<h3>🔧 Agregando Columnas Faltantes:</h3>";
            
            foreach ($missing_columns as $column_name => $column_definition) {
                try {
                    $sql = "ALTER TABLE users ADD COLUMN $column_name $column_definition";
                    $pdo->exec($sql);
                    echo "<p class='success'>✅ Agregada: $column_name</p>";
                } catch (PDOException $e) {
                    echo "<p class='error'>❌ Error agregando $column_name: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p class='success'>🎉 ¡Proceso completado! Ahora puedes probar admin.php</p>";
            echo "<p><a href='admin.php' style='color: #28a745; font-weight: bold;'>🔄 Probar admin.php</a></p>";
            
        } else {
            echo "<form method='POST'>";
            echo "<p>¿Quieres agregar las columnas faltantes?</p>";
            echo "<button type='submit' name='add_columns' value='yes' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>✅ Sí, agregar columnas</button>";
            echo " ";
            echo "<button type='button' onclick='window.location.reload()' style='background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔄 Verificar de nuevo</button>";
            echo "</form>";
        }
    } else {
        echo "<h2>🎉 ¡Tabla Users Completa!</h2>";
        echo "<p class='success'>Todas las columnas necesarias están presentes.</p>";
        echo "<p><a href='admin.php' style='color: #28a745; font-weight: bold;'>🔄 Probar admin.php</a></p>";
    }
    
    // Mostrar algunos usuarios de ejemplo
    echo "<h2>👥 Usuarios Existentes:</h2>";
    $stmt = $pdo->query("SELECT id, username, is_admin FROM users LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<pre>";
        foreach ($users as $user) {
            echo "ID: " . $user['id'] . " - Usuario: " . $user['username'] . " - Admin: " . ($user['is_admin'] ? 'Sí' : 'No') . "\n";
        }
        echo "</pre>";
    } else {
        echo "<p class='warning'>No se encontraron usuarios</p>";
    }
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='test_admin_access.php'>🔍 Test de acceso</a> | <a href='admin_debug.php'>🐛 Debug admin</a> | <a href='index.php'>🏠 Inicio</a></p>";
?>
