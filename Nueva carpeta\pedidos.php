<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'Usuario';

// --- CONEXIÓN MYSQL ---
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// --- OBTENER PEDIDOS DEL USUARIO ---
$stmt = $pdo->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC");
$stmt->execute([$user_id]);
$mis_pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);

// --- MARCAR PEDIDOS COMO VISTOS POR EL CLIENTE ---
// Cuando el cliente accede a esta página, marcar todos sus pedidos como vistos
$stmt = $pdo->prepare("
    UPDATE orders
    SET client_notif_seen = 1, client_notif_seen_at = NOW()
    WHERE user_id = ? AND client_notif_seen = 0
");
$stmt->execute([$user_id]);

// Separar pedidos por estado
$pedidos_pendientes = array_filter($mis_pedidos, function($p) {
    return in_array($p['status'], ['Recibido', 'En Cola', 'Procesando']);
});
$pedidos_listos = array_filter($mis_pedidos, function($p) {
    return $p['status'] === 'Listo';
});
$pedidos_no_disponibles = array_filter($mis_pedidos, function($p) {
    return $p['status'] === 'No disponible';
});

// Estadísticas
$total_pedidos = count($mis_pedidos);
$pendientes = count($pedidos_pendientes);
$listos = count($pedidos_listos);
$no_disponibles = count($pedidos_no_disponibles);

// --- CREAR TABLA HISTORIAL SI NO EXISTE (MYSQL) ---
$pdo->exec("CREATE TABLE IF NOT EXISTS orders_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(64),
    title VARCHAR(255),
    media_type VARCHAR(32),
    year VARCHAR(8),
    country VARCHAR(64),
    city VARCHAR(64),
    ip_address VARCHAR(45),
    status VARCHAR(32),
    created_at DATETIME,
    tmdb_id INT,
    notif_seen TINYINT(1) DEFAULT 0,
    archived_at DATETIME
)");

// Obtener historial de pedidos archivados
$stmt_hist = $pdo->prepare("SELECT * FROM orders_history WHERE user_id = ? ORDER BY archived_at DESC");
$stmt_hist->execute([$user_id]);
$historial_pedidos = $stmt_hist->fetchAll(PDO::FETCH_ASSOC);
?><!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 Mis Pedidos - RogsMediaTV</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📦</text></svg>">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: var(--gradient-dark);
            padding: 1.5rem 0;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-title h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        /* Stats Section */
        .stats-section {
            padding: 2rem 0;
            background: var(--dark-bg);
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
        }

        .stat-icon.total { background: rgba(37, 99, 235, 0.2); color: var(--primary-color); }
        .stat-icon.pending { background: rgba(245, 158, 11, 0.2); color: var(--warning-color); }
        .stat-icon.ready { background: rgba(16, 185, 129, 0.2); color: var(--accent-color); }
        .stat-icon.unavailable { background: rgba(239, 68, 68, 0.2); color: var(--error-color); }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Content Section */
        .content-section {
            padding: 2rem 0;
        }

        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .section-title.pending { color: var(--warning-color); }
        .section-title.ready { color: var(--accent-color); }
        .section-title.unavailable { color: var(--error-color); }

        /* Orders Grid */
        .orders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .order-card {
            background: var(--secondary-color);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: var(--transition);
            position: relative;
        }

        .order-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .order-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }

        .order-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .order-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .order-type {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .order-body {
            padding: 1.5rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .status-recibido { background: rgba(74, 144, 226, 0.2); color: #4a90e2; }
        .status-en-cola { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .status-procesando { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        .status-listo { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
        .status-no-disponible { background: rgba(244, 67, 54, 0.2); color: #f44336; }

        .order-date {
            color: var(--text-secondary);
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1.5rem;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .stats-container {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .orders-grid {
                grid-template-columns: 1fr;
            }

            .order-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="page-title">
                <h1>
                    <i class="fas fa-shopping-cart"></i>
                    Mis Pedidos
                </h1>
            </div>

            <a href="index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Volver al inicio
            </a>
        </div>
    </header>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-icon total">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-number"><?php echo $total_pedidos; ?></div>
                <div class="stat-label">Total Pedidos</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo $pendientes; ?></div>
                <div class="stat-label">En Proceso</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon ready">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?php echo $listos; ?></div>
                <div class="stat-label">Listos</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon unavailable">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number"><?php echo $no_disponibles; ?></div>
                <div class="stat-label">No Disponibles</div>
            </div>
        </div>
    </section>

    <!-- Content Section -->
    <section class="content-section">
        <div class="content-container">

            <!-- Pedidos Pendientes -->
            <?php if (!empty($pedidos_pendientes)): ?>
            <div class="section-header">
                <i class="fas fa-clock" style="color: var(--warning-color);"></i>
                <h2 class="section-title pending">Pedidos en Proceso</h2>
            </div>

            <div class="orders-grid">
                <?php foreach ($pedidos_pendientes as $pedido): ?>
                <div class="order-card">
                    <div class="order-header">
                        <h3 class="order-title"><?php echo htmlspecialchars($pedido['title']); ?></h3>
                        <div class="order-meta">
                            <div class="order-type">
                                <i class="fas fa-<?php echo $pedido['media_type'] === 'movie' ? 'film' : 'tv'; ?>"></i>
                                <span><?php echo $pedido['media_type'] === 'movie' ? 'Película' : 'Serie'; ?></span>
                            </div>
                            <span>•</span>
                            <span><?php echo htmlspecialchars($pedido['year']); ?></span>
                        </div>
                    </div>
                    <div class="order-body">
                        <div class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $pedido['status'])); ?>">
                            <i class="fas fa-<?php
                                echo $pedido['status'] === 'Recibido' ? 'inbox' :
                                    ($pedido['status'] === 'En Cola' ? 'clock' : 'cog');
                            ?>"></i>
                            <?php echo htmlspecialchars($pedido['status']); ?>
                        </div>
                        <div class="order-date">
                            <i class="fas fa-calendar-alt"></i>
                            <?php
                                $dt = new DateTime($pedido['created_at'], new DateTimeZone('UTC'));
                                $dt->setTimezone(new DateTimeZone('America/Bogota'));
                                echo $dt->format('d/m/Y h:i A');
                            ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Pedidos Listos -->
            <?php if (!empty($pedidos_listos)): ?>
            <div class="section-header">
                <i class="fas fa-check-circle" style="color: var(--accent-color);"></i>
                <h2 class="section-title ready">Contenido Disponible</h2>
            </div>

            <div class="orders-grid">
                <?php foreach ($pedidos_listos as $pedido): ?>
                <div class="order-card">
                    <div class="order-header">
                        <h3 class="order-title"><?php echo htmlspecialchars($pedido['title']); ?></h3>
                        <div class="order-meta">
                            <div class="order-type">
                                <i class="fas fa-<?php echo $pedido['media_type'] === 'movie' ? 'film' : 'tv'; ?>"></i>
                                <span><?php echo $pedido['media_type'] === 'movie' ? 'Película' : 'Serie'; ?></span>
                            </div>
                            <span>•</span>
                            <span><?php echo htmlspecialchars($pedido['year']); ?></span>
                        </div>
                    </div>
                    <div class="order-body">
                        <div class="status-badge status-listo">
                            <i class="fas fa-check-circle"></i>
                            ¡Ya Disponible!
                        </div>
                        <div class="order-date">
                            <i class="fas fa-calendar-alt"></i>
                            Pedido: <?php
                                $dt = new DateTime($pedido['created_at'], new DateTimeZone('UTC'));
                                $dt->setTimezone(new DateTimeZone('America/Bogota'));
                                echo $dt->format('d/m/Y h:i A');
                            ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Pedidos No Disponibles -->
            <?php if (!empty($pedidos_no_disponibles)): ?>
            <div class="section-header">
                <i class="fas fa-times-circle" style="color: var(--error-color);"></i>
                <h2 class="section-title unavailable">Contenido No Disponible</h2>
            </div>

            <div class="orders-grid">
                <?php foreach ($pedidos_no_disponibles as $pedido): ?>
                <div class="order-card">
                    <div class="order-header">
                        <h3 class="order-title"><?php echo htmlspecialchars($pedido['title']); ?></h3>
                        <div class="order-meta">
                            <div class="order-type">
                                <i class="fas fa-<?php echo $pedido['media_type'] === 'movie' ? 'film' : 'tv'; ?>"></i>
                                <span><?php echo $pedido['media_type'] === 'movie' ? 'Película' : 'Serie'; ?></span>
                            </div>
                            <span>•</span>
                            <span><?php echo htmlspecialchars($pedido['year']); ?></span>
                        </div>
                    </div>
                    <div class="order-body">
                        <div class="status-badge status-no-disponible">
                            <i class="fas fa-times-circle"></i>
                            No Disponible
                        </div>
                        <div class="order-date">
                            <i class="fas fa-calendar-alt"></i>
                            Pedido: <?php
                                $dt = new DateTime($pedido['created_at'], new DateTimeZone('UTC'));
                                $dt->setTimezone(new DateTimeZone('America/Bogota'));
                                echo $dt->format('d/m/Y h:i A');
                            ?>
                        </div>
                        <p style="color: var(--text-secondary); font-size: 0.85rem; margin-top: 0.5rem;">
                            <i class="fas fa-info-circle"></i>
                            Este contenido es difícil de conseguir o no está disponible actualmente.
                        </p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Estado Vacío -->
            <?php if (empty($mis_pedidos)): ?>
            <div class="empty-state">
                <i class="fas fa-shopping-cart"></i>
                <h3>No tienes pedidos aún</h3>
                <p>Cuando hagas tu primer pedido, aparecerá aquí.</p>
                <a href="index.php" class="back-btn" style="margin-top: 1rem; display: inline-flex;">
                    <i class="fas fa-plus"></i>
                    Hacer mi primer pedido
                </a>
            </div>
            <?php endif; ?>

        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--dark-bg); padding: 2rem 0; border-top: 1px solid var(--border-color); margin-top: 3rem;">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; text-align: center;">
            <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                <i class="fas fa-user"></i>
                Bienvenido, <strong style="color: var(--primary-color);"><?php echo htmlspecialchars($username); ?></strong>
            </p>
            <p style="color: var(--text-secondary); font-size: 0.9rem;">
                © 2024 RogsMediaTV - Gestión de Pedidos
            </p>
        </div>
    </footer>

</body>
</html>
