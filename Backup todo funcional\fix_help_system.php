<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔧 Reparando Sistema de Ayuda</h1>";

try {
    // 1. Ejecutar corrección de estructura de tabla
    echo "<h2>🔧 Ejecutando corrección de estructura...</h2>";

    // Incluir el script de corrección de estructura
    ob_start();
    include 'fix_help_table_structure.php';
    $structure_output = ob_get_clean();

    // Verificar que la tabla esté correctamente estructurada
    $stmt = $pdo->query("DESCRIBE help_articles");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $required_columns = ['id', 'title', 'content', 'excerpt', 'category', 'subcategory', 'status', 'is_featured', 'view_count'];
    $missing_columns = array_diff($required_columns, $columns);

    if (empty($missing_columns)) {
        $success_messages[] = "✅ Estructura de tabla help_articles correcta";
    } else {
        $error_messages[] = "❌ Columnas faltantes: " . implode(', ', $missing_columns);
    }

    // 2. Verificar que los artículos de ejemplo estén disponibles
    echo "<h2>📊 Verificando contenido...</h2>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles WHERE status = 'published'");
        $published_count = $stmt->fetchColumn();

        if ($published_count > 0) {
            $success_messages[] = "✅ Sistema tiene $published_count artículos publicados";
        } else {
            $error_messages[] = "❌ No hay artículos publicados disponibles";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error verificando contenido: " . $e->getMessage();
    }

    // 3. Verificar API del sistema
    echo "<h2>🔌 Verificando API...</h2>";
    try {
        if (file_exists('api_help_system.php')) {
            $success_messages[] = "✅ API del sistema de ayuda disponible";
        } else {
            $error_messages[] = "❌ API del sistema de ayuda no encontrada";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error verificando API: " . $e->getMessage();
    }

    // 4. Verificar archivos principales
    echo "<h2>📁 Verificando archivos...</h2>";
    $required_files = [
        'user_help.php' => 'Centro de Ayuda para Usuarios',
        'help_admin.php' => 'Panel de Administración',
        'api_help_system.php' => 'API del Sistema'
    ];

    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            $success_messages[] = "✅ $description disponible";
        } else {
            $error_messages[] = "❌ $description no encontrado";
        }
    }

    // 5. Estadísticas finales
    echo "<h2>📊 Estadísticas finales...</h2>";
    try {
        $stmt = $pdo->query("
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured,
                SUM(view_count) as total_views
            FROM help_articles
        ");
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        $success_messages[] = "📊 Total de artículos: " . $stats['total'];
        $success_messages[] = "📊 Artículos publicados: " . $stats['published'];
        $success_messages[] = "📊 Artículos destacados: " . $stats['featured'];
        $success_messages[] = "📊 Total de vistas: " . $stats['total_views'];

    } catch (Exception $e) {
        $error_messages[] = "❌ Error obteniendo estadísticas: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Reparar Sistema de Ayuda - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Sistema de Ayuda Reparado</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Operaciones Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="user_help.php" class="btn">👀 Ver Centro de Ayuda</a>
            <a href="help_admin.php" class="btn">⚙️ Panel Admin</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
    </div>
</body>
</html>
