<?php
// Script de diagnóstico para el sistema de ayuda
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnóstico del Sistema de Ayuda</h1>";
echo "<style>
body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; padding: 20px; }
.success { color: #10b981; }
.error { color: #ef4444; }
.warning { color: #f59e0b; }
.section { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 8px; }
</style>";

// 1. Verificar config.php
echo "<div class='section'>";
echo "<h2>1. Verificando config.php</h2>";
if (file_exists('config.php')) {
    echo "<span class='success'>✅ config.php existe</span><br>";
    try {
        require_once 'config.php';
        echo "<span class='success'>✅ config.php se carga correctamente</span><br>";
        
        if (isset($pdo)) {
            echo "<span class='success'>✅ Variable \$pdo está definida</span><br>";
            
            // Probar conexión
            $stmt = $pdo->query("SELECT 1");
            echo "<span class='success'>✅ Conexión a base de datos funciona</span><br>";
        } else {
            echo "<span class='error'>❌ Variable \$pdo no está definida</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Error en config.php: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span class='error'>❌ config.php no existe</span><br>";
}
echo "</div>";

// 2. Verificar tabla help_articles
echo "<div class='section'>";
echo "<h2>2. Verificando tabla help_articles</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'help_articles'");
    if ($stmt->rowCount() > 0) {
        echo "<span class='success'>✅ Tabla help_articles existe</span><br>";
        
        // Verificar estructura
        $stmt = $pdo->query("DESCRIBE help_articles");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<span class='success'>✅ Columnas: " . implode(', ', $columns) . "</span><br>";
        
        // Verificar datos
        $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles");
        $count = $stmt->fetchColumn();
        echo "<span class='success'>✅ Total de artículos: $count</span><br>";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT id, title FROM help_articles LIMIT 3");
            $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<span class='success'>✅ Artículos de ejemplo:</span><br>";
            foreach ($articles as $article) {
                echo "&nbsp;&nbsp;- ID {$article['id']}: {$article['title']}<br>";
            }
        }
    } else {
        echo "<span class='error'>❌ Tabla help_articles no existe</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Error verificando tabla: " . $e->getMessage() . "</span><br>";
}
echo "</div>";

// 3. Verificar archivos del sistema
echo "<div class='section'>";
echo "<h2>3. Verificando archivos del sistema</h2>";
$files = [
    'user_help.php' => 'Centro de Ayuda Principal',
    'user_help_article.php' => 'Página de Artículo',
    'user_help_category.php' => 'Página de Categoría',
    'help_admin.php' => 'Panel de Administración',
    'api_help_system.php' => 'API del Sistema'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "<span class='success'>✅ $description ($file)</span><br>";
        
        // Verificar sintaxis PHP básica
        $content = file_get_contents($file);
        if (strpos($content, '<?php') === 0) {
            echo "&nbsp;&nbsp;<span class='success'>✓ Sintaxis PHP válida</span><br>";
        } else {
            echo "&nbsp;&nbsp;<span class='warning'>⚠️ No inicia con <?php</span><br>";
        }
    } else {
        echo "<span class='error'>❌ $description ($file) no existe</span><br>";
    }
}
echo "</div>";

// 4. Probar API
echo "<div class='section'>";
echo "<h2>4. Probando API</h2>";
if (file_exists('api_help_system.php')) {
    try {
        // Probar endpoint básico
        $test_url = 'api_help_system.php?action=get_articles';
        
        // Simular llamada interna
        ob_start();
        $_GET['action'] = 'get_articles';
        include 'api_help_system.php';
        $response = ob_get_clean();
        
        if (!empty($response)) {
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                if ($data['success']) {
                    echo "<span class='success'>✅ API get_articles funciona</span><br>";
                    echo "&nbsp;&nbsp;Artículos encontrados: " . (isset($data['articles']) ? count($data['articles']) : 0) . "<br>";
                } else {
                    echo "<span class='error'>❌ API get_articles error: " . ($data['error'] ?? 'Unknown') . "</span><br>";
                }
            } else {
                echo "<span class='error'>❌ API respuesta inválida: $response</span><br>";
            }
        } else {
            echo "<span class='error'>❌ API no responde</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Error probando API: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span class='error'>❌ api_help_system.php no existe</span><br>";
}
echo "</div>";

// 5. Verificar permisos de archivos
echo "<div class='section'>";
echo "<h2>5. Verificando permisos</h2>";
$files_to_check = ['user_help.php', 'user_help_article.php', 'config.php'];
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<span class='success'>✅ $file es legible</span><br>";
        } else {
            echo "<span class='error'>❌ $file no es legible</span><br>";
        }
    }
}
echo "</div>";

// 6. Probar user_help_article.php específicamente
echo "<div class='section'>";
echo "<h2>6. Probando user_help_article.php</h2>";
if (file_exists('user_help_article.php')) {
    try {
        // Verificar sintaxis
        $output = shell_exec('php -l user_help_article.php 2>&1');
        if (strpos($output, 'No syntax errors') !== false) {
            echo "<span class='success'>✅ Sintaxis PHP correcta</span><br>";
        } else {
            echo "<span class='error'>❌ Error de sintaxis: $output</span><br>";
        }
        
        // Probar con ID válido
        if (isset($pdo)) {
            $stmt = $pdo->query("SELECT id FROM help_articles LIMIT 1");
            $article = $stmt->fetch();
            if ($article) {
                echo "<span class='success'>✅ ID de prueba disponible: {$article['id']}</span><br>";
                echo "<span class='success'>✅ URL de prueba: user_help_article.php?id={$article['id']}</span><br>";
            } else {
                echo "<span class='warning'>⚠️ No hay artículos para probar</span><br>";
            }
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ Error probando user_help_article.php: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span class='error'>❌ user_help_article.php no existe</span><br>";
}
echo "</div>";

// 7. Información del servidor
echo "<div class='section'>";
echo "<h2>7. Información del servidor</h2>";
echo "<span class='success'>PHP Version: " . phpversion() . "</span><br>";
echo "<span class='success'>Error Reporting: " . error_reporting() . "</span><br>";
echo "<span class='success'>Display Errors: " . ini_get('display_errors') . "</span><br>";
echo "<span class='success'>Memory Limit: " . ini_get('memory_limit') . "</span><br>";
echo "</div>";

// 8. Logs de errores recientes
echo "<div class='section'>";
echo "<h2>8. Verificando logs de errores</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "<span class='success'>✅ Log de errores: $error_log</span><br>";
    $recent_errors = shell_exec("tail -10 $error_log 2>/dev/null");
    if ($recent_errors) {
        echo "<pre style='background: #000; padding: 10px; border-radius: 4px; overflow-x: auto;'>$recent_errors</pre>";
    }
} else {
    echo "<span class='warning'>⚠️ No se encontró log de errores configurado</span><br>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Recomendaciones</h2>";
echo "<p>1. Si hay errores de sintaxis, corregir los archivos PHP</p>";
echo "<p>2. Si la tabla no existe, ejecutar: <a href='recreate_help_table.php' style='color: #10b981;'>recreate_help_table.php</a></p>";
echo "<p>3. Si la API no funciona, verificar permisos y configuración</p>";
echo "<p>4. Para probar el sistema: <a href='user_help.php' style='color: #10b981;'>user_help.php</a></p>";
echo "</div>";
?>
