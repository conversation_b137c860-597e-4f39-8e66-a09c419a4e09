<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔧 Corrigiendo Estructura de Tabla help_articles</h1>";

try {
    // 1. Verificar si la tabla existe
    echo "<h2>🔍 Verificando tabla help_articles...</h2>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'help_articles'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ La tabla help_articles no existe. Creándola...</p>";
        
        // Crear tabla completa desde cero
        $pdo->exec("
            CREATE TABLE help_articles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                excerpt TEXT,
                category ENUM('faq', 'tutorial', 'guide', 'troubleshooting', 'setup', 'billing') DEFAULT 'faq',
                subcategory VARCHAR(100),
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                is_featured BOOLEAN DEFAULT FALSE,
                view_count INT DEFAULT 0,
                author_id INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_category (category),
                INDEX idx_status (status),
                INDEX idx_featured (is_featured),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $success_messages[] = "✅ Tabla help_articles creada completamente";
    } else {
        $success_messages[] = "✅ Tabla help_articles existe";
        
        // 2. Verificar estructura actual
        echo "<h2>🔍 Verificando estructura actual...</h2>";
        
        $stmt = $pdo->query("DESCRIBE help_articles");
        $existing_columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $existing_columns[] = $row['Field'];
        }
        
        echo "<p>Columnas existentes: " . implode(', ', $existing_columns) . "</p>";
        
        // 3. Agregar columnas faltantes
        $required_columns = [
            'excerpt' => "ADD COLUMN excerpt TEXT AFTER content",
            'subcategory' => "ADD COLUMN subcategory VARCHAR(100) AFTER category",
            'status' => "ADD COLUMN status ENUM('draft', 'published', 'archived') DEFAULT 'published' AFTER subcategory",
            'author_id' => "ADD COLUMN author_id INT DEFAULT 1 AFTER view_count"
        ];
        
        foreach ($required_columns as $column => $sql) {
            if (!in_array($column, $existing_columns)) {
                try {
                    $pdo->exec("ALTER TABLE help_articles $sql");
                    $success_messages[] = "✅ Columna '$column' agregada";
                } catch (Exception $e) {
                    $error_messages[] = "❌ Error agregando columna '$column': " . $e->getMessage();
                }
            } else {
                $success_messages[] = "✅ Columna '$column' ya existe";
            }
        }
        
        // 4. Verificar y corregir tipos de datos
        echo "<h2>🔧 Verificando tipos de datos...</h2>";
        
        // Verificar si category es ENUM
        $stmt = $pdo->query("SHOW COLUMNS FROM help_articles LIKE 'category'");
        $category_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($category_info && !strpos($category_info['Type'], 'enum')) {
            try {
                $pdo->exec("ALTER TABLE help_articles MODIFY COLUMN category ENUM('faq', 'tutorial', 'guide', 'troubleshooting', 'setup', 'billing') DEFAULT 'faq'");
                $success_messages[] = "✅ Tipo de columna 'category' corregido a ENUM";
            } catch (Exception $e) {
                $error_messages[] = "❌ Error corrigiendo tipo de 'category': " . $e->getMessage();
            }
        }
        
        // 5. Migrar datos de help_content si existe
        echo "<h2>🔄 Migrando datos de help_content...</h2>";
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'help_content'");
        if ($stmt->rowCount() > 0) {
            try {
                // Verificar si help_content tiene datos
                $stmt = $pdo->query("SELECT COUNT(*) FROM help_content");
                $content_count = $stmt->fetchColumn();
                
                if ($content_count > 0) {
                    // Verificar estructura de help_content
                    $stmt = $pdo->query("DESCRIBE help_content");
                    $content_columns = [];
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        $content_columns[] = $row['Field'];
                    }
                    
                    // Construir query de migración dinámicamente
                    $select_fields = ['id', 'title', 'content'];
                    $insert_fields = ['id', 'title', 'content'];
                    
                    // Agregar excerpt si no existe en help_content
                    if (in_array('excerpt', $content_columns)) {
                        $select_fields[] = 'excerpt';
                        $insert_fields[] = 'excerpt';
                    } else {
                        $select_fields[] = "SUBSTRING(content, 1, 200) as excerpt";
                        $insert_fields[] = 'excerpt';
                    }
                    
                    // Agregar category
                    if (in_array('category', $content_columns)) {
                        $select_fields[] = 'category';
                        $insert_fields[] = 'category';
                    } else {
                        $select_fields[] = "'faq' as category";
                        $insert_fields[] = 'category';
                    }
                    
                    // Agregar subcategory
                    if (in_array('subcategory', $content_columns)) {
                        $select_fields[] = 'subcategory';
                        $insert_fields[] = 'subcategory';
                    } else {
                        $select_fields[] = "NULL as subcategory";
                        $insert_fields[] = 'subcategory';
                    }
                    
                    // Agregar status
                    if (in_array('is_published', $content_columns)) {
                        $select_fields[] = "CASE WHEN is_published = 1 THEN 'published' ELSE 'draft' END as status";
                    } else {
                        $select_fields[] = "'published' as status";
                    }
                    $insert_fields[] = 'status';
                    
                    // Agregar is_featured
                    if (in_array('is_featured', $content_columns)) {
                        $select_fields[] = 'is_featured';
                        $insert_fields[] = 'is_featured';
                    } else {
                        $select_fields[] = "0 as is_featured";
                        $insert_fields[] = 'is_featured';
                    }
                    
                    // Agregar view_count
                    if (in_array('view_count', $content_columns)) {
                        $select_fields[] = 'view_count';
                        $insert_fields[] = 'view_count';
                    } else {
                        $select_fields[] = "0 as view_count";
                        $insert_fields[] = 'view_count';
                    }
                    
                    // Agregar timestamps
                    if (in_array('created_at', $content_columns)) {
                        $select_fields[] = 'created_at';
                        $insert_fields[] = 'created_at';
                    }
                    
                    if (in_array('updated_at', $content_columns)) {
                        $select_fields[] = 'updated_at';
                        $insert_fields[] = 'updated_at';
                    }
                    
                    $migration_query = "
                        INSERT IGNORE INTO help_articles (" . implode(', ', $insert_fields) . ")
                        SELECT " . implode(', ', $select_fields) . "
                        FROM help_content
                    ";
                    
                    $stmt = $pdo->prepare($migration_query);
                    $stmt->execute();
                    $migrated = $stmt->rowCount();
                    
                    $success_messages[] = "✅ Migrados $migrated artículos de help_content";
                } else {
                    $success_messages[] = "ℹ️ help_content está vacía, no hay datos que migrar";
                }
            } catch (Exception $e) {
                $error_messages[] = "❌ Error migrando datos: " . $e->getMessage();
            }
        } else {
            $success_messages[] = "ℹ️ Tabla help_content no existe, no hay datos que migrar";
        }
    }
    
    // 6. Insertar artículos de ejemplo
    echo "<h2>📝 Insertando artículos de ejemplo...</h2>";
    
    // Verificar si ya hay artículos
    $stmt = $pdo->query("SELECT COUNT(*) FROM help_articles");
    $existing_articles = $stmt->fetchColumn();
    
    if ($existing_articles == 0) {
        $sample_articles = [
            [
                'title' => '¿Cómo configurar IPTV en Android?',
                'content' => "Para configurar IPTV en Android, sigue estos pasos:\n\n1. **Descarga la aplicación IPTV** desde Play Store o desde nuestro centro de descargas\n2. **Abre la aplicación** una vez instalada\n3. **Ve a Configuración** > Agregar lista\n4. **Introduce la URL de tu lista M3U** que recibiste por email\n5. **Guarda la configuración** y espera a que cargue\n\n¡Tu lista IPTV estará lista para usar! Si tienes problemas, contacta nuestro soporte.",
                'excerpt' => 'Guía paso a paso para configurar IPTV en dispositivos Android con aplicaciones compatibles',
                'category' => 'tutorial',
                'subcategory' => 'android',
                'status' => 'published',
                'is_featured' => 1
            ],
            [
                'title' => '¿Qué hacer si los canales no cargan?',
                'content' => "Si los canales no cargan, verifica lo siguiente:\n\n**1. Conexión a Internet**\n- Verifica que tengas conexión estable\n- Velocidad mínima recomendada: 10 Mbps para HD\n\n**2. URL de la Lista**\n- Asegúrate de que la URL sea correcta\n- Verifica que no haya espacios extra\n\n**3. Suscripción Activa**\n- Confirma que tu suscripción no haya expirado\n- Revisa tu email de activación\n\n**4. Reiniciar Aplicación**\n- Cierra completamente la app\n- Vuelve a abrirla\n\n**5. Contactar Soporte**\n- Si el problema persiste, contáctanos",
                'excerpt' => 'Soluciones paso a paso para problemas de carga de canales IPTV',
                'category' => 'troubleshooting',
                'subcategory' => 'playback',
                'status' => 'published',
                'is_featured' => 1
            ],
            [
                'title' => '¿Cómo activar mi código de suscripción?',
                'content' => "Para activar tu código de suscripción:\n\n**Paso 1: Acceder al Panel**\n- Ve a la sección \"Activación\" en nuestro sitio web\n- O usa el enlace que recibiste por email\n\n**Paso 2: Introducir Código**\n- Introduce tu código de activación (12 dígitos)\n- Asegúrate de escribirlo correctamente\n\n**Paso 3: Seleccionar Duración**\n- Elige la duración de tu suscripción\n- Confirma los detalles\n\n**Paso 4: Activar**\n- Haz clic en \"Activar\"\n- Espera la confirmación\n\n**Paso 5: Recibir Lista**\n- Recibirás tu lista M3U por email\n- Guarda el enlace en lugar seguro",
                'excerpt' => 'Proceso completo de activación de códigos de suscripción IPTV',
                'category' => 'setup',
                'subcategory' => 'activation',
                'status' => 'published',
                'is_featured' => 0
            ],
            [
                'title' => 'Requisitos del Sistema',
                'content' => "**Requisitos Mínimos para IPTV:**\n\n**📱 Android:**\n- Android 5.0 o superior\n- 2GB RAM mínimo\n- Conexión a internet estable\n- Aplicación IPTV compatible\n\n**🍎 iOS:**\n- iOS 12.0 o superior\n- iPhone 6s o superior\n- iPad Air 2 o superior\n\n**📺 Smart TV:**\n- Android TV 7.0+\n- Samsung Tizen 4.0+\n- LG webOS 4.0+\n- Roku OS 9.0+\n\n**💻 PC/Mac:**\n- Windows 10 o macOS 10.14+\n- VLC Media Player o similar\n- Navegador web moderno\n\n**🌐 Internet:**\n- Velocidad mínima: 5 Mbps para SD\n- Recomendado: 10 Mbps para HD\n- Óptimo: 25 Mbps para 4K",
                'excerpt' => 'Requisitos técnicos completos para usar servicios IPTV en diferentes dispositivos',
                'category' => 'faq',
                'subcategory' => 'requirements',
                'status' => 'published',
                'is_featured' => 0
            ],
            [
                'title' => 'Solucionar Problemas de Buffering',
                'content' => "**Para solucionar problemas de buffering:**\n\n**🔍 Diagnóstico:**\n1. **Verifica tu velocidad de internet**\n   - Usa speedtest.net\n   - Mínimo 10 Mbps para HD\n\n2. **Prueba en diferentes horarios**\n   - El buffering puede ser por congestión\n\n**🔧 Soluciones:**\n\n**Inmediatas:**\n- Cambia la calidad de video a una menor\n- Pausa el video 30 segundos para que cargue\n- Cierra otras aplicaciones que usen internet\n\n**Técnicas:**\n- Reinicia tu router/modem\n- Usa conexión por cable en lugar de WiFi\n- Cambia el servidor DNS (*******)\n\n**Avanzadas:**\n- Contacta a tu ISP si el problema persiste\n- Considera cambiar de proveedor de internet\n- Verifica que no haya descargas activas",
                'excerpt' => 'Guía completa para solucionar problemas de buffering y interrupciones en IPTV',
                'category' => 'troubleshooting',
                'subcategory' => 'buffering',
                'status' => 'published',
                'is_featured' => 1
            ],
            [
                'title' => 'Configurar EPG (Guía de Programación)',
                'content' => "**Para configurar la Guía Electrónica de Programación:**\n\n**¿Qué es EPG?**\nLa EPG muestra la programación de TV con horarios, descripciones y géneros de programas.\n\n**Configuración:**\n\n**Paso 1: Acceder a Configuración**\n- En tu aplicación IPTV, ve a **Configuración**\n- Busca la opción **EPG** o **Guía de TV**\n\n**Paso 2: Agregar URL del EPG**\n- Introduce la URL del EPG proporcionada\n- Formato típico: http://ejemplo.com/epg.xml\n\n**Paso 3: Configurar Actualización**\n- Establece actualización automática cada 12-24 horas\n- Selecciona zona horaria correcta\n\n**Paso 4: Guardar y Reiniciar**\n- Guarda la configuración\n- Reinicia la aplicación\n- La guía se descargará automáticamente\n\n**Solución de Problemas:**\n- Si no aparece la guía, verifica la URL\n- Asegúrate de tener conexión a internet\n- Contacta soporte si persiste el problema",
                'excerpt' => 'Configuración completa de la guía electrónica de programación (EPG) para IPTV',
                'category' => 'guide',
                'subcategory' => 'epg',
                'status' => 'published',
                'is_featured' => 0
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO help_articles (title, content, excerpt, category, subcategory, status, is_featured) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $inserted = 0;
        foreach ($sample_articles as $article) {
            try {
                $result = $stmt->execute([
                    $article['title'],
                    $article['content'],
                    $article['excerpt'],
                    $article['category'],
                    $article['subcategory'],
                    $article['status'],
                    $article['is_featured']
                ]);
                if ($result) $inserted++;
            } catch (Exception $e) {
                $error_messages[] = "❌ Error insertando artículo '{$article['title']}': " . $e->getMessage();
            }
        }
        
        $success_messages[] = "✅ Insertados $inserted artículos de ejemplo";
    } else {
        $success_messages[] = "ℹ️ Ya existen $existing_articles artículos, no se insertan ejemplos";
    }
    
    // 7. Verificar estructura final
    echo "<h2>📊 Verificación final...</h2>";
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_articles,
            SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
            SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured
        FROM help_articles
    ");
    $final_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $success_messages[] = "📊 Total de artículos: " . $final_stats['total_articles'];
    $success_messages[] = "📊 Artículos publicados: " . $final_stats['published'];
    $success_messages[] = "📊 Artículos destacados: " . $final_stats['featured'];
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Corregir Estructura - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Estructura de Tabla Corregida</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Operaciones Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="test_help_communication.php" class="btn">🧪 Probar Sistema</a>
            <a href="user_help.php" class="btn">👥 Centro de Ayuda</a>
            <a href="help_admin.php" class="btn">⚙️ Admin Ayuda</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
    </div>
</body>
</html>
